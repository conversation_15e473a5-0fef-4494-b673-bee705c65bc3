/**
 * Global teardown for Soil Master E2E tests.
 * 
 * Cleans up test environment, generates reports,
 * and ensures clean state for future test runs.
 */

import { FullConfig } from '@playwright/test';
import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:8000';

async function globalTeardown(config: FullConfig) {
  console.log('🧹 Starting Soil Master Demo E2E Test Teardown...');
  
  try {
    await generateTestReport();
    await cleanupTestData();
    await validateSystemHealth();
    await archiveArtifacts();
    await cleanupTempFiles();
    
    console.log('✅ Demo E2E Test Teardown Complete!');
  } catch (error) {
    console.error('❌ Teardown failed:', error);
    // Don't throw to avoid masking test failures
  }
}

async function generateTestReport() {
  console.log('📊 Generating test report...');
  
  try {
    const testResultsDir = path.join(__dirname, 'test-results');
    const reportPath = path.join(testResultsDir, 'test-summary.json');
    
    // Collect test results
    const testSummary = {
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'test',
      demo_version: '1.0.2',
      test_results: {
        total_tests: 0,
        passed_tests: 0,
        failed_tests: 0,
        skipped_tests: 0,
        duration_ms: 0
      },
      performance_metrics: {},
      coverage_summary: {},
      artifacts: {
        screenshots: 0,
        videos: 0,
        traces: 0
      }
    };
    
    // Count artifacts
    if (fs.existsSync(testResultsDir)) {
      const files = fs.readdirSync(testResultsDir, { recursive: true });
      testSummary.artifacts.screenshots = files.filter(f => f.toString().endsWith('.png')).length;
      testSummary.artifacts.videos = files.filter(f => f.toString().endsWith('.webm')).length;
      testSummary.artifacts.traces = files.filter(f => f.toString().endsWith('.zip')).length;
    }
    
    // Load performance baseline if exists
    const baselinePath = path.join(testResultsDir, 'performance-baseline.json');
    if (fs.existsSync(baselinePath)) {
      testSummary.performance_metrics = JSON.parse(fs.readFileSync(baselinePath, 'utf8'));
    }
    
    // Save test summary
    fs.writeFileSync(reportPath, JSON.stringify(testSummary, null, 2));
    
    console.log('✅ Test report generated');
  } catch (error) {
    console.error('❌ Test report generation failed:', error);
  }
}

async function cleanupTestData() {
  console.log('🗑️ Cleaning up test data...');
  
  try {
    // Clear demo scenarios created during tests
    const response = await fetch(`${API_BASE_URL}/api/v1/demo/scenarios?test_data=true`, {
      method: 'DELETE',
      headers: {
        'Authorization': 'Bearer demo-cleanup-token'
      }
    });
    
    if (response.ok) {
      const result = await response.json();
      console.log(`  ✅ Cleaned up ${result.deleted_scenarios || 0} test scenarios`);
    }
    
    // Clear cache
    await fetch(`${API_BASE_URL}/api/v1/demo/cache/clear`, {
      method: 'POST',
      headers: {
        'Authorization': 'Bearer demo-cleanup-token'
      }
    });
    
    console.log('  ✅ Cache cleared');
    
    // Reset database to clean state (if in test environment)
    if (process.env.NODE_ENV === 'test') {
      try {
        execSync('cd ../soil-backend && python scripts/reset_test_db.py', {
          stdio: 'pipe',
          timeout: 30000
        });
        console.log('  ✅ Test database reset');
      } catch (error) {
        console.warn('  ⚠️ Database reset failed:', error);
      }
    }
    
    console.log('✅ Test data cleanup complete');
  } catch (error) {
    console.error('❌ Test data cleanup failed:', error);
  }
}

async function validateSystemHealth() {
  console.log('🏥 Validating system health post-tests...');
  
  try {
    const healthResponse = await fetch(`${API_BASE_URL}/api/v1/demo/health`);
    if (healthResponse.ok) {
      const health = await healthResponse.json();
      
      console.log(`  ✅ System health: ${health.status}`);
      console.log(`  📊 Performance score: ${health.overall_performance_score || 'N/A'}`);
      console.log(`  💾 Memory usage: ${health.system_metrics?.memory_usage_percent || 'N/A'}%`);
      console.log(`  🔄 Cache hit rate: ${health.cache_health?.hit_rate_percent || 'N/A'}%`);
      
      // Log any issues
      if (health.status !== 'healthy') {
        console.warn('  ⚠️ System health degraded after tests');
      }
      
      if (health.system_metrics?.memory_usage_percent > 90) {
        console.warn('  ⚠️ High memory usage detected');
      }
    } else {
      console.warn('  ⚠️ Unable to check system health');
    }
  } catch (error) {
    console.error('❌ System health validation failed:', error);
  }
}

async function archiveArtifacts() {
  console.log('📦 Archiving test artifacts...');
  
  try {
    const testResultsDir = path.join(__dirname, 'test-results');
    const archiveDir = path.join(__dirname, 'archives');
    
    if (!fs.existsSync(testResultsDir)) {
      console.log('  ℹ️ No test results to archive');
      return;
    }
    
    // Create archive directory
    if (!fs.existsSync(archiveDir)) {
      fs.mkdirSync(archiveDir, { recursive: true });
    }
    
    // Create timestamped archive
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const archiveName = `test-results-${timestamp}`;
    const archivePath = path.join(archiveDir, archiveName);
    
    // Copy test results to archive
    fs.cpSync(testResultsDir, archivePath, { recursive: true });
    
    // Compress if possible
    try {
      execSync(`cd ${archiveDir} && tar -czf ${archiveName}.tar.gz ${archiveName}`, {
        stdio: 'pipe'
      });
      
      // Remove uncompressed directory
      fs.rmSync(archivePath, { recursive: true, force: true });
      
      console.log(`  ✅ Artifacts archived: ${archiveName}.tar.gz`);
    } catch (error) {
      console.log(`  ✅ Artifacts archived: ${archiveName}/`);
    }
    
    // Clean up old archives (keep last 10)
    const archives = fs.readdirSync(archiveDir)
      .filter(f => f.startsWith('test-results-'))
      .sort()
      .reverse();
    
    if (archives.length > 10) {
      const toDelete = archives.slice(10);
      for (const archive of toDelete) {
        fs.rmSync(path.join(archiveDir, archive), { recursive: true, force: true });
      }
      console.log(`  🗑️ Cleaned up ${toDelete.length} old archives`);
    }
    
  } catch (error) {
    console.error('❌ Artifact archiving failed:', error);
  }
}

async function cleanupTempFiles() {
  console.log('🧽 Cleaning up temporary files...');
  
  try {
    const tempFiles = [
      path.join(__dirname, 'auth-state.json'),
      path.join(__dirname, 'test-results', 'performance-baseline.json')
    ];
    
    for (const file of tempFiles) {
      if (fs.existsSync(file)) {
        fs.unlinkSync(file);
        console.log(`  🗑️ Removed: ${path.basename(file)}`);
      }
    }
    
    // Clean up any remaining lock files
    const lockFiles = [
      path.join(__dirname, '..', 'soil-backend', '.pytest_cache'),
      path.join(__dirname, '..', 'soil-frontend', '.next'),
      path.join(__dirname, '..', 'soil-frontend', 'node_modules', '.cache')
    ];
    
    for (const lockDir of lockFiles) {
      if (fs.existsSync(lockDir)) {
        try {
          fs.rmSync(lockDir, { recursive: true, force: true });
          console.log(`  🗑️ Removed cache: ${path.basename(lockDir)}`);
        } catch (error) {
          // Ignore cache cleanup errors
        }
      }
    }
    
    console.log('✅ Temporary file cleanup complete');
  } catch (error) {
    console.error('❌ Temporary file cleanup failed:', error);
  }
}

async function generatePerformanceReport() {
  console.log('📈 Generating performance report...');
  
  try {
    const testResultsDir = path.join(__dirname, 'test-results');
    const performanceReportPath = path.join(testResultsDir, 'performance-report.json');
    
    // Collect final performance metrics
    const performanceResponse = await fetch(`${API_BASE_URL}/api/v1/demo/performance/snapshot`);
    if (performanceResponse.ok) {
      const finalMetrics = await performanceResponse.json();
      
      // Load baseline if exists
      const baselinePath = path.join(testResultsDir, 'performance-baseline.json');
      let baseline = null;
      if (fs.existsSync(baselinePath)) {
        baseline = JSON.parse(fs.readFileSync(baselinePath, 'utf8'));
      }
      
      const performanceReport = {
        timestamp: new Date().toISOString(),
        baseline: baseline,
        final_metrics: finalMetrics,
        performance_analysis: {
          response_time_change: baseline ? 
            finalMetrics.current_metrics?.response_times?.heatmap_generation_ms - 
            baseline.current_metrics?.response_times?.heatmap_generation_ms : null,
          memory_usage_change: baseline ?
            finalMetrics.current_metrics?.system_metrics?.memory_usage_percent -
            baseline.current_metrics?.system_metrics?.memory_usage_percent : null,
          cache_hit_rate_change: baseline ?
            finalMetrics.current_metrics?.cache_metrics?.hit_rate_percent -
            baseline.current_metrics?.cache_metrics?.hit_rate_percent : null
        },
        recommendations: []
      };
      
      // Generate recommendations based on performance changes
      if (performanceReport.performance_analysis.response_time_change > 100) {
        performanceReport.recommendations.push('Response times increased significantly - investigate performance bottlenecks');
      }
      
      if (performanceReport.performance_analysis.memory_usage_change > 10) {
        performanceReport.recommendations.push('Memory usage increased - check for memory leaks');
      }
      
      if (performanceReport.performance_analysis.cache_hit_rate_change < -5) {
        performanceReport.recommendations.push('Cache hit rate decreased - review caching strategy');
      }
      
      fs.writeFileSync(performanceReportPath, JSON.stringify(performanceReport, null, 2));
      console.log('✅ Performance report generated');
    }
  } catch (error) {
    console.error('❌ Performance report generation failed:', error);
  }
}

// Graceful shutdown handling
process.on('SIGINT', () => {
  console.log('\n🛑 Teardown interrupted');
  process.exit(0);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled rejection during teardown:', reason);
  // Don't exit to allow other cleanup to continue
});

export default globalTeardown;
