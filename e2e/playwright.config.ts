import { defineConfig, devices } from '@playwright/test';

/**
 * Playwright configuration for Soil Master v1.0.2 E2E testing.
 * 
 * Optimized for demo scenario testing with performance monitoring,
 * cross-browser compatibility, and enterprise-grade reliability.
 */

export default defineConfig({
  // Test directory
  testDir: './tests',
  
  // Global test timeout
  timeout: 60000, // 60 seconds for demo scenarios
  
  // Expect timeout for assertions
  expect: {
    timeout: 10000 // 10 seconds for UI assertions
  },
  
  // Fail the build on CI if you accidentally left test.only in the source code
  forbidOnly: !!process.env.CI,
  
  // Retry on CI only
  retries: process.env.CI ? 2 : 0,
  
  // Opt out of parallel tests on CI
  workers: process.env.CI ? 1 : undefined,
  
  // Reporter configuration
  reporter: [
    ['html', { outputFolder: 'test-results/html-report' }],
    ['json', { outputFile: 'test-results/results.json' }],
    ['junit', { outputFile: 'test-results/junit.xml' }],
    ['line'],
    ['allure-playwright', { outputFolder: 'test-results/allure-results' }]
  ],
  
  // Global setup and teardown
  globalSetup: require.resolve('./global-setup'),
  globalTeardown: require.resolve('./global-teardown'),
  
  // Output directory for test artifacts
  outputDir: 'test-results/artifacts',
  
  // Shared settings for all projects
  use: {
    // Base URL for tests
    baseURL: process.env.DEMO_BASE_URL || 'http://localhost:3000',
    
    // Browser context options
    viewport: { width: 1920, height: 1080 },
    ignoreHTTPSErrors: true,
    
    // Artifacts collection
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
    trace: 'retain-on-failure',
    
    // Network settings
    extraHTTPHeaders: {
      'Accept-Language': 'en-US,en;q=0.9'
    },
    
    // Performance monitoring
    actionTimeout: 15000, // 15 seconds for actions
    navigationTimeout: 30000, // 30 seconds for navigation
    
    // Demo-specific settings
    storageState: undefined, // Start fresh for each test
    
    // Locale and timezone
    locale: 'en-US',
    timezoneId: 'America/New_York'
  },
  
  // Test projects for different browsers and scenarios
  projects: [
    // Setup project for authentication
    {
      name: 'setup',
      testMatch: /.*\.setup\.ts/,
      teardown: 'cleanup'
    },
    
    // Cleanup project
    {
      name: 'cleanup',
      testMatch: /.*\.cleanup\.ts/
    },
    
    // Chrome - Primary demo browser
    {
      name: 'chromium-demo',
      use: { 
        ...devices['Desktop Chrome'],
        viewport: { width: 1920, height: 1080 },
        // Demo-optimized settings
        launchOptions: {
          args: [
            '--disable-web-security',
            '--disable-features=TranslateUI',
            '--disable-ipc-flooding-protection',
            '--disable-background-timer-throttling',
            '--disable-backgrounding-occluded-windows',
            '--disable-renderer-backgrounding'
          ]
        }
      },
      dependencies: ['setup'],
      testMatch: /.*\.spec\.ts/
    },
    
    // Firefox - Secondary browser testing
    {
      name: 'firefox-demo',
      use: { 
        ...devices['Desktop Firefox'],
        viewport: { width: 1920, height: 1080 }
      },
      dependencies: ['setup'],
      testMatch: /.*\.spec\.ts/
    },
    
    // Safari - Mac compatibility
    {
      name: 'webkit-demo',
      use: { 
        ...devices['Desktop Safari'],
        viewport: { width: 1920, height: 1080 }
      },
      dependencies: ['setup'],
      testMatch: /.*\.spec\.ts/
    },
    
    // Mobile Chrome - Responsive testing
    {
      name: 'mobile-chrome',
      use: { 
        ...devices['Pixel 5']
      },
      dependencies: ['setup'],
      testMatch: /.*mobile.*\.spec\.ts/
    },
    
    // Tablet - Medium screen testing
    {
      name: 'tablet',
      use: {
        ...devices['iPad Pro'],
        viewport: { width: 1024, height: 768 }
      },
      dependencies: ['setup'],
      testMatch: /.*tablet.*\.spec\.ts/
    },
    
    // Performance testing project
    {
      name: 'performance',
      use: {
        ...devices['Desktop Chrome'],
        viewport: { width: 1920, height: 1080 },
        // Performance monitoring enabled
        launchOptions: {
          args: [
            '--enable-precise-memory-info',
            '--enable-gpu-benchmarking'
          ]
        }
      },
      dependencies: ['setup'],
      testMatch: /.*performance.*\.spec\.ts/,
      timeout: 120000 // 2 minutes for performance tests
    },
    
    // Load testing project
    {
      name: 'load-testing',
      use: {
        ...devices['Desktop Chrome'],
        viewport: { width: 1920, height: 1080 }
      },
      dependencies: ['setup'],
      testMatch: /.*load.*\.spec\.ts/,
      timeout: 300000, // 5 minutes for load tests
      workers: 5 // Parallel load testing
    },
    
    // Accessibility testing
    {
      name: 'accessibility',
      use: {
        ...devices['Desktop Chrome'],
        viewport: { width: 1920, height: 1080 }
      },
      dependencies: ['setup'],
      testMatch: /.*a11y.*\.spec\.ts/
    }
  ],
  
  // Web server configuration for local development
  webServer: process.env.CI ? undefined : [
    {
      command: 'cd ../soil-frontend && npm run dev',
      port: 3000,
      timeout: 120000,
      reuseExistingServer: !process.env.CI
    },
    {
      command: 'cd ../soil-backend && python -m uvicorn app.main:app --host 0.0.0.0 --port 8000',
      port: 8000,
      timeout: 120000,
      reuseExistingServer: !process.env.CI
    }
  ],
  
  // Test metadata
  metadata: {
    'test-suite': 'Soil Master Demo E2E Tests',
    'version': '1.0.2',
    'environment': process.env.NODE_ENV || 'test',
    'demo-optimized': true
  }
});
