/**
 * End-to-End tests for Demo Scenarios functionality.
 * 
 * Tests complete demo workflows including scenario switching,
 * heatmap interactions, and performance validation for
 * high-stakes presentations.
 */

import { test, expect, Page } from '@playwright/test';

// Test configuration
const DEMO_BASE_URL = process.env.DEMO_BASE_URL || 'http://localhost:3000';
const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:8000';

// Performance thresholds for demo requirements
const PERFORMANCE_THRESHOLDS = {
  PAGE_LOAD_TIME: 3000,      // 3 seconds max
  SCENARIO_SWITCH_TIME: 5000, // 5 seconds max
  HEATMAP_LOAD_TIME: 2000,   // 2 seconds max
  API_RESPONSE_TIME: 1000    // 1 second max
};

// Test data
const DEMO_SCENARIOS = {
  HEALTHY_PALM_OIL: {
    name: 'palm_oil_100ha_healthy',
    title: 'Healthy Palm Oil Estate (100ha)',
    expectedDataPoints: 100
  },
  MIXED_RUBBER: {
    name: 'rubber_500ha_mixed',
    title: 'Large Rubber Estate with Mixed Conditions (500ha)',
    expectedDataPoints: 500
  },
  CRISIS_RECOVERY: {
    name: 'crisis_recovery_before_after',
    title: 'Crisis Recovery Success Story',
    expectedDataPoints: 200
  }
};

const SOIL_PARAMETERS = [
  { key: 'soil_nitrogen', label: 'Soil Nitrogen', unit: 'mg/kg' },
  { key: 'soil_phosphorus', label: 'Soil Phosphorus', unit: 'mg/kg' },
  { key: 'soil_potassium', label: 'Soil Potassium', unit: 'mg/kg' },
  { key: 'soil_ph', label: 'Soil pH', unit: 'pH' }
];

// Helper functions
async function loginAsDemo(page: Page) {
  await page.goto(`${DEMO_BASE_URL}/login`);
  await page.fill('[data-testid="email-input"]', '<EMAIL>');
  await page.fill('[data-testid="password-input"]', 'demo123');
  await page.click('[data-testid="login-button"]');
  await page.waitForURL(`${DEMO_BASE_URL}/demo`);
}

async function waitForDemoReady(page: Page) {
  // Wait for demo ready indicator
  await page.waitForSelector('[data-testid="demo-ready-indicator"]', {
    state: 'visible',
    timeout: 10000
  });
  
  // Verify performance indicators are green
  const performanceIndicator = page.locator('[data-testid="performance-indicator"]');
  await expect(performanceIndicator).toHaveClass(/ready|excellent/);
}

async function measurePerformance(page: Page, action: () => Promise<void>) {
  const startTime = Date.now();
  await action();
  const endTime = Date.now();
  return endTime - startTime;
}

test.describe('Demo Scenarios - Stakeholder Presentation Flow', () => {
  test.beforeEach(async ({ page }) => {
    await loginAsDemo(page);
    await waitForDemoReady(page);
  });

  test('should load demo dashboard within performance threshold', async ({ page }) => {
    const loadTime = await measurePerformance(page, async () => {
      await page.goto(`${DEMO_BASE_URL}/demo`);
      await page.waitForSelector('[data-testid="demo-dashboard"]');
    });

    expect(loadTime).toBeLessThan(PERFORMANCE_THRESHOLDS.PAGE_LOAD_TIME);
  });

  test('should display all available demo scenarios', async ({ page }) => {
    // Check scenario selector is visible
    await expect(page.locator('[data-testid="scenario-selector"]')).toBeVisible();
    
    // Verify key scenarios are available
    for (const scenario of Object.values(DEMO_SCENARIOS)) {
      await expect(page.locator(`[data-testid="scenario-card"]`, {
        hasText: scenario.title
      })).toBeVisible();
    }
  });

  test('should switch between scenarios smoothly', async ({ page }) => {
    // Start with healthy palm oil scenario
    await page.click(`[data-testid="scenario-card"]:has-text("${DEMO_SCENARIOS.HEALTHY_PALM_OIL.title}")`);
    
    const switchTime = await measurePerformance(page, async () => {
      // Switch to mixed rubber scenario
      await page.click(`[data-testid="scenario-card"]:has-text("${DEMO_SCENARIOS.MIXED_RUBBER.title}")`);
      
      // Wait for scenario switch to complete
      await page.waitForSelector('[data-testid="scenario-switching-loading"]', { state: 'hidden' });
      await page.waitForSelector('[data-testid="heatmap-container"]');
    });

    expect(switchTime).toBeLessThan(PERFORMANCE_THRESHOLDS.SCENARIO_SWITCH_TIME);
    
    // Verify scenario is selected
    await expect(page.locator(`[data-testid="scenario-card"]:has-text("${DEMO_SCENARIOS.MIXED_RUBBER.title}")`))
      .toHaveClass(/selected/);
  });

  test('should display scenario information correctly', async ({ page }) => {
    await page.click(`[data-testid="scenario-card"]:has-text("${DEMO_SCENARIOS.HEALTHY_PALM_OIL.title}")`);
    
    // Check scenario details
    await expect(page.locator('[data-testid="scenario-title"]'))
      .toHaveText(DEMO_SCENARIOS.HEALTHY_PALM_OIL.title);
    
    await expect(page.locator('[data-testid="scenario-type"]'))
      .toContainText('Sample Estate');
    
    await expect(page.locator('[data-testid="estate-size"]'))
      .toContainText('100 hectares');
    
    await expect(page.locator('[data-testid="crop-type"]'))
      .toContainText('Palm Oil');
  });
});

test.describe('Interactive Heatmap - Visual Impact Testing', () => {
  test.beforeEach(async ({ page }) => {
    await loginAsDemo(page);
    await waitForDemoReady(page);
    
    // Select a scenario with good data
    await page.click(`[data-testid="scenario-card"]:has-text("${DEMO_SCENARIOS.MIXED_RUBBER.title}")`);
    await page.waitForSelector('[data-testid="heatmap-container"]');
  });

  test('should render heatmap within performance threshold', async ({ page }) => {
    const loadTime = await measurePerformance(page, async () => {
      await page.click('[data-testid="parameter-soil_nitrogen"]');
      await page.waitForSelector('[data-testid="heatmap-layer"]');
    });

    expect(loadTime).toBeLessThan(PERFORMANCE_THRESHOLDS.HEATMAP_LOAD_TIME);
  });

  test('should switch between soil parameters smoothly', async ({ page }) => {
    for (const parameter of SOIL_PARAMETERS) {
      const switchTime = await measurePerformance(page, async () => {
        await page.click(`[data-testid="parameter-${parameter.key}"]`);
        await page.waitForSelector('[data-testid="heatmap-loading"]', { state: 'hidden' });
      });

      expect(switchTime).toBeLessThan(PERFORMANCE_THRESHOLDS.HEATMAP_LOAD_TIME);
      
      // Verify parameter is selected
      await expect(page.locator(`[data-testid="parameter-${parameter.key}"]`))
        .toHaveClass(/selected/);
      
      // Verify parameter info is displayed
      await expect(page.locator('[data-testid="parameter-info"]'))
        .toContainText(parameter.label);
    }
  });

  test('should display color legend correctly', async ({ page }) => {
    await page.click('[data-testid="parameter-soil_nitrogen"]');
    
    // Check color legend is visible
    await expect(page.locator('[data-testid="color-legend"]')).toBeVisible();
    
    // Verify legend has proper structure
    await expect(page.locator('[data-testid="legend-step"]')).toHaveCount(5); // 5 color steps
    
    // Check units are displayed
    await expect(page.locator('[data-testid="parameter-unit"]')).toContainText('mg/kg');
  });

  test('should highlight problem areas effectively', async ({ page }) => {
    await page.click('[data-testid="parameter-soil_nitrogen"]');
    
    // Wait for problem areas to load
    await page.waitForSelector('[data-testid="problem-area-marker"]');
    
    // Check problem areas summary
    const problemSummary = page.locator('[data-testid="problem-areas-summary"]');
    await expect(problemSummary).toBeVisible();
    
    // Click on a problem area
    await page.click('[data-testid="problem-area-marker"]');
    
    // Verify problem details popup
    await expect(page.locator('[data-testid="problem-area-details"]')).toBeVisible();
    await expect(page.locator('[data-testid="problem-severity"]')).toBeVisible();
    await expect(page.locator('[data-testid="problem-recommendations"]')).toBeVisible();
  });

  test('should support visual impact modes', async ({ page }) => {
    const visualModes = ['dramatic', 'professional', 'high_contrast'];
    
    for (const mode of visualModes) {
      await page.click(`[data-testid="visual-impact-${mode}"]`);
      
      // Wait for visual update
      await page.waitForTimeout(500);
      
      // Verify mode is selected
      await expect(page.locator(`[data-testid="visual-impact-${mode}"]`))
        .toHaveClass(/selected/);
      
      // Verify heatmap updates
      await expect(page.locator('[data-testid="heatmap-layer"]')).toBeVisible();
    }
  });
});

test.describe('Performance Monitoring - Demo Readiness', () => {
  test.beforeEach(async ({ page }) => {
    await loginAsDemo(page);
  });

  test('should display performance metrics', async ({ page }) => {
    await waitForDemoReady(page);
    
    // Check performance indicators
    await expect(page.locator('[data-testid="response-time-indicator"]')).toBeVisible();
    await expect(page.locator('[data-testid="cache-hit-rate-indicator"]')).toBeVisible();
    await expect(page.locator('[data-testid="demo-ready-indicator"]')).toBeVisible();
    
    // Verify metrics are within acceptable ranges
    const responseTime = await page.locator('[data-testid="response-time-value"]').textContent();
    const responseTimeMs = parseInt(responseTime?.replace('ms', '') || '0');
    expect(responseTimeMs).toBeLessThan(PERFORMANCE_THRESHOLDS.API_RESPONSE_TIME);
    
    const cacheHitRate = await page.locator('[data-testid="cache-hit-rate-value"]').textContent();
    const cacheHitPercent = parseInt(cacheHitRate?.replace('%', '') || '0');
    expect(cacheHitPercent).toBeGreaterThan(80); // 80% minimum
  });

  test('should handle performance degradation gracefully', async ({ page }) => {
    // Simulate slow network conditions
    await page.route('**/api/v1/demo/**', async route => {
      await new Promise(resolve => setTimeout(resolve, 2000)); // 2 second delay
      await route.continue();
    });
    
    await page.goto(`${DEMO_BASE_URL}/demo`);
    
    // Should show performance warning
    await expect(page.locator('[data-testid="performance-warning"]')).toBeVisible();
    
    // Should still be functional
    await expect(page.locator('[data-testid="scenario-selector"]')).toBeVisible();
  });

  test('should provide real-time performance feedback', async ({ page }) => {
    await waitForDemoReady(page);
    
    // Perform actions and monitor performance updates
    await page.click(`[data-testid="scenario-card"]:has-text("${DEMO_SCENARIOS.HEALTHY_PALM_OIL.title}")`);
    
    // Performance metrics should update
    await page.waitForFunction(() => {
      const indicator = document.querySelector('[data-testid="last-update-time"]');
      return indicator && indicator.textContent !== '';
    });
    
    // Check that metrics are recent (within last 10 seconds)
    const lastUpdate = await page.locator('[data-testid="last-update-time"]').textContent();
    const updateTime = new Date(lastUpdate || '').getTime();
    const now = Date.now();
    expect(now - updateTime).toBeLessThan(10000); // 10 seconds
  });
});

test.describe('Before/After Scenarios - ROI Demonstration', () => {
  test.beforeEach(async ({ page }) => {
    await loginAsDemo(page);
    await waitForDemoReady(page);
  });

  test('should display before/after comparison effectively', async ({ page }) => {
    // Select crisis recovery scenario
    await page.click(`[data-testid="scenario-card"]:has-text("${DEMO_SCENARIOS.CRISIS_RECOVERY.title}")`);
    
    // Should show before/after toggle
    await expect(page.locator('[data-testid="before-after-toggle"]')).toBeVisible();
    
    // Test before state
    await page.click('[data-testid="before-state-button"]');
    await page.waitForSelector('[data-testid="heatmap-layer"]');
    
    // Verify before state indicators
    await expect(page.locator('[data-testid="scenario-phase"]')).toContainText('Before');
    await expect(page.locator('[data-testid="problem-areas-summary"]')).toBeVisible();
    
    // Test after state
    await page.click('[data-testid="after-state-button"]');
    await page.waitForSelector('[data-testid="heatmap-layer"]');
    
    // Verify after state indicators
    await expect(page.locator('[data-testid="scenario-phase"]')).toContainText('After');
    await expect(page.locator('[data-testid="improvement-summary"]')).toBeVisible();
  });

  test('should show ROI calculations', async ({ page }) => {
    await page.click(`[data-testid="scenario-card"]:has-text("${DEMO_SCENARIOS.CRISIS_RECOVERY.title}")`);
    
    // Open ROI panel
    await page.click('[data-testid="roi-analysis-button"]');
    
    // Verify ROI metrics
    await expect(page.locator('[data-testid="roi-panel"]')).toBeVisible();
    await expect(page.locator('[data-testid="yield-improvement"]')).toBeVisible();
    await expect(page.locator('[data-testid="cost-reduction"]')).toBeVisible();
    await expect(page.locator('[data-testid="payback-period"]')).toBeVisible();
    await expect(page.locator('[data-testid="npv-calculation"]')).toBeVisible();
    
    // Verify realistic values
    const yieldImprovement = await page.locator('[data-testid="yield-improvement-value"]').textContent();
    const improvementPercent = parseInt(yieldImprovement?.replace('%', '') || '0');
    expect(improvementPercent).toBeGreaterThan(0);
    expect(improvementPercent).toBeLessThan(200); // Realistic upper bound
  });
});

test.describe('Error Handling and Resilience', () => {
  test('should handle API failures gracefully', async ({ page }) => {
    // Simulate API failure
    await page.route('**/api/v1/demo/scenarios', route => route.abort());
    
    await page.goto(`${DEMO_BASE_URL}/demo`);
    
    // Should show error state
    await expect(page.locator('[data-testid="scenario-selector-error"]')).toBeVisible();
    
    // Should provide retry option
    await expect(page.locator('[data-testid="retry-load-scenarios"]')).toBeVisible();
    
    // Test retry functionality
    await page.unroute('**/api/v1/demo/scenarios');
    await page.click('[data-testid="retry-load-scenarios"]');
    
    // Should recover
    await expect(page.locator('[data-testid="scenario-selector"]')).toBeVisible();
  });

  test('should handle network interruptions', async ({ page }) => {
    await loginAsDemo(page);
    await waitForDemoReady(page);
    
    // Simulate network interruption during scenario switch
    await page.route('**/api/v1/demo/scenarios/*/switch', route => route.abort());
    
    await page.click(`[data-testid="scenario-card"]:has-text("${DEMO_SCENARIOS.HEALTHY_PALM_OIL.title}")`);
    
    // Should show error message
    await expect(page.locator('[data-testid="scenario-switch-error"]')).toBeVisible();
    
    // Should maintain previous state
    await expect(page.locator('[data-testid="heatmap-container"]')).toBeVisible();
  });

  test('should provide offline fallback', async ({ page }) => {
    await loginAsDemo(page);
    await waitForDemoReady(page);
    
    // Simulate offline mode
    await page.context().setOffline(true);
    
    // Should show offline indicator
    await expect(page.locator('[data-testid="offline-indicator"]')).toBeVisible();
    
    // Should still allow interaction with cached data
    await expect(page.locator('[data-testid="scenario-selector"]')).toBeVisible();
    
    // Restore online mode
    await page.context().setOffline(false);
    
    // Should recover automatically
    await expect(page.locator('[data-testid="offline-indicator"]')).toBeHidden();
  });
});

test.describe('Accessibility and Usability', () => {
  test('should be keyboard navigable', async ({ page }) => {
    await loginAsDemo(page);
    await waitForDemoReady(page);
    
    // Test keyboard navigation through scenarios
    await page.keyboard.press('Tab'); // Focus first scenario
    await page.keyboard.press('Enter'); // Select scenario
    
    // Should switch scenario
    await page.waitForSelector('[data-testid="heatmap-container"]');
    
    // Test parameter navigation
    await page.keyboard.press('Tab'); // Focus parameter selector
    await page.keyboard.press('ArrowRight'); // Next parameter
    await page.keyboard.press('Enter'); // Select parameter
    
    // Should switch parameter
    await page.waitForSelector('[data-testid="heatmap-loading"]', { state: 'hidden' });
  });

  test('should have proper ARIA labels', async ({ page }) => {
    await loginAsDemo(page);
    await waitForDemoReady(page);
    
    // Check ARIA labels
    await expect(page.locator('[role="region"][aria-label*="Demo Scenario"]')).toBeVisible();
    await expect(page.locator('[role="group"][aria-label*="Parameter Selection"]')).toBeVisible();
    await expect(page.locator('[role="application"][aria-label*="Interactive Heatmap"]')).toBeVisible();
  });

  test('should work on different screen sizes', async ({ page }) => {
    await loginAsDemo(page);
    
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await waitForDemoReady(page);
    
    // Should be responsive
    await expect(page.locator('[data-testid="mobile-scenario-selector"]')).toBeVisible();
    
    // Test tablet viewport
    await page.setViewportSize({ width: 768, height: 1024 });
    await expect(page.locator('[data-testid="tablet-layout"]')).toBeVisible();
    
    // Test desktop viewport
    await page.setViewportSize({ width: 1920, height: 1080 });
    await expect(page.locator('[data-testid="desktop-layout"]')).toBeVisible();
  });
});
