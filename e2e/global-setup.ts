/**
 * Global setup for Soil Master E2E tests.
 * 
 * Prepares demo environment, validates system health,
 * and ensures optimal conditions for demo testing.
 */

import { chromium, FullConfig } from '@playwright/test';
import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

const DEMO_BASE_URL = process.env.DEMO_BASE_URL || 'http://localhost:3000';
const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:8000';
const SETUP_TIMEOUT = 300000; // 5 minutes

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting Soil Master Demo E2E Test Setup...');
  
  // Create test results directory
  const testResultsDir = path.join(__dirname, 'test-results');
  if (!fs.existsSync(testResultsDir)) {
    fs.mkdirSync(testResultsDir, { recursive: true });
  }
  
  // Setup steps
  await validateEnvironment();
  await prepareDatabase();
  await validateServices();
  await setupDemoData();
  await warmupCache();
  await createAuthState();
  
  console.log('✅ Demo E2E Test Setup Complete!');
}

async function validateEnvironment() {
  console.log('🔍 Validating test environment...');
  
  // Check required environment variables
  const requiredEnvVars = [
    'NODE_ENV',
    'DATABASE_URL',
    'SECRET_KEY'
  ];
  
  for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
      throw new Error(`Missing required environment variable: ${envVar}`);
    }
  }
  
  // Set test-specific environment variables
  process.env.TESTING = 'true';
  process.env.DEMO_MODE = 'true';
  process.env.LOG_LEVEL = 'error'; // Reduce noise during tests
  
  console.log('✅ Environment validation complete');
}

async function prepareDatabase() {
  console.log('🗄️ Preparing test database...');
  
  try {
    // Run database migrations
    execSync('cd ../soil-backend && alembic upgrade head', {
      stdio: 'pipe',
      timeout: 60000
    });
    
    // Clear existing test data
    execSync('cd ../soil-backend && python scripts/clear_test_data.py', {
      stdio: 'pipe',
      timeout: 30000
    });
    
    console.log('✅ Database preparation complete');
  } catch (error) {
    console.error('❌ Database preparation failed:', error);
    throw error;
  }
}

async function validateServices() {
  console.log('🔧 Validating services...');
  
  const services = [
    { name: 'Frontend', url: DEMO_BASE_URL, timeout: 30000 },
    { name: 'Backend API', url: `${API_BASE_URL}/health`, timeout: 30000 },
    { name: 'Database', url: `${API_BASE_URL}/health/db`, timeout: 15000 },
    { name: 'Cache', url: `${API_BASE_URL}/health/cache`, timeout: 15000 }
  ];
  
  for (const service of services) {
    await validateService(service.name, service.url, service.timeout);
  }
  
  console.log('✅ Service validation complete');
}

async function validateService(name: string, url: string, timeout: number) {
  const startTime = Date.now();
  const maxRetries = 10;
  let retries = 0;
  
  while (retries < maxRetries) {
    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: { 'Accept': 'application/json' },
        signal: AbortSignal.timeout(timeout)
      });
      
      if (response.ok) {
        const responseTime = Date.now() - startTime;
        console.log(`  ✅ ${name}: OK (${responseTime}ms)`);
        return;
      }
      
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    } catch (error) {
      retries++;
      if (retries >= maxRetries) {
        console.error(`  ❌ ${name}: Failed after ${maxRetries} retries`);
        throw new Error(`Service validation failed for ${name}: ${error}`);
      }
      
      console.log(`  ⏳ ${name}: Retry ${retries}/${maxRetries} in 2s...`);
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
}

async function setupDemoData() {
  console.log('📊 Setting up demo data...');
  
  try {
    // Generate sample estate scenarios
    const response = await fetch(`${API_BASE_URL}/api/v1/demo/sample-data/generate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer demo-setup-token'
      }
    });
    
    if (!response.ok) {
      throw new Error(`Failed to generate demo data: ${response.statusText}`);
    }
    
    const result = await response.json();
    console.log(`  ✅ Generated ${result.scenarios_created} demo scenarios`);
    
    // Generate before/after scenarios
    const beforeAfterResponse = await fetch(`${API_BASE_URL}/api/v1/demo/sample-data/before-after`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer demo-setup-token'
      }
    });
    
    if (beforeAfterResponse.ok) {
      const beforeAfterResult = await beforeAfterResponse.json();
      console.log(`  ✅ Generated ${beforeAfterResult.scenario_pairs} before/after pairs`);
    }
    
    console.log('✅ Demo data setup complete');
  } catch (error) {
    console.error('❌ Demo data setup failed:', error);
    throw error;
  }
}

async function warmupCache() {
  console.log('🔥 Warming up cache...');
  
  try {
    // Get list of scenarios
    const scenariosResponse = await fetch(`${API_BASE_URL}/api/v1/demo/scenarios`);
    if (!scenariosResponse.ok) {
      throw new Error('Failed to fetch scenarios for cache warmup');
    }
    
    const { scenarios } = await scenariosResponse.json();
    
    // Preload data for each scenario
    for (const scenario of scenarios.slice(0, 3)) { // Limit to first 3 scenarios
      try {
        // Generate predictions
        await fetch(`${API_BASE_URL}/api/v1/demo/scenarios/${scenario.id}/predictions/generate`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' }
        });
        
        // Preload heatmap data for each parameter
        const parameters = ['soil_nitrogen', 'soil_phosphorus', 'soil_potassium', 'soil_ph'];
        for (const parameter of parameters) {
          await fetch(`${API_BASE_URL}/api/v1/demo/heatmap/optimized?scenario_id=${scenario.id}&parameter=${parameter}`);
        }
        
        console.log(`  ✅ Warmed up cache for scenario: ${scenario.name}`);
      } catch (error) {
        console.warn(`  ⚠️ Cache warmup failed for scenario ${scenario.name}:`, error);
      }
    }
    
    console.log('✅ Cache warmup complete');
  } catch (error) {
    console.error('❌ Cache warmup failed:', error);
    // Don't throw - cache warmup is optional
  }
}

async function createAuthState() {
  console.log('🔐 Creating authentication state...');
  
  const browser = await chromium.launch();
  const context = await browser.newContext();
  const page = await context.newPage();
  
  try {
    // Navigate to login page
    await page.goto(`${DEMO_BASE_URL}/login`);
    
    // Login as demo user
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'demo123');
    await page.click('[data-testid="login-button"]');
    
    // Wait for successful login
    await page.waitForURL(`${DEMO_BASE_URL}/demo`, { timeout: 30000 });
    
    // Save authentication state
    await context.storageState({ path: path.join(__dirname, 'auth-state.json') });
    
    console.log('✅ Authentication state created');
  } catch (error) {
    console.error('❌ Authentication setup failed:', error);
    throw error;
  } finally {
    await browser.close();
  }
}

// Performance monitoring setup
async function setupPerformanceMonitoring() {
  console.log('📈 Setting up performance monitoring...');
  
  try {
    // Initialize performance baseline
    const performanceResponse = await fetch(`${API_BASE_URL}/api/v1/demo/performance/snapshot`);
    if (performanceResponse.ok) {
      const baseline = await performanceResponse.json();
      
      // Save baseline for comparison
      fs.writeFileSync(
        path.join(__dirname, 'test-results', 'performance-baseline.json'),
        JSON.stringify(baseline, null, 2)
      );
      
      console.log('✅ Performance baseline established');
    }
  } catch (error) {
    console.warn('⚠️ Performance monitoring setup failed:', error);
    // Don't throw - performance monitoring is optional
  }
}

// Health check before tests
async function finalHealthCheck() {
  console.log('🏥 Final health check...');
  
  try {
    const healthResponse = await fetch(`${API_BASE_URL}/api/v1/demo/health`);
    if (!healthResponse.ok) {
      throw new Error('Demo health check failed');
    }
    
    const health = await healthResponse.json();
    if (!health.demo_ready) {
      throw new Error('Demo system not ready');
    }
    
    console.log(`✅ Demo system healthy (score: ${health.overall_performance_score})`);
  } catch (error) {
    console.error('❌ Final health check failed:', error);
    throw error;
  }
}

// Error handling and cleanup
process.on('SIGINT', async () => {
  console.log('\n🛑 Setup interrupted, cleaning up...');
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled rejection during setup:', reason);
  process.exit(1);
});

export default globalSetup;
