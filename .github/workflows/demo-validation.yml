name: Demo System Validation

on:
  schedule:
    # Run demo validation every 4 hours
    - cron: '0 */4 * * *'
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to validate'
        required: true
        default: 'production'
        type: choice
        options:
          - production
          - staging
          - demo
      full_validation:
        description: 'Run full validation suite'
        required: false
        default: false
        type: boolean

env:
  NODE_VERSION: '18'
  PYTHON_VERSION: '3.11'

jobs:
  demo-health-check:
    name: Demo Health Check
    runs-on: ubuntu-24.04
    strategy:
      matrix:
        environment: [production, staging]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install dependencies
        run: |
          pip install requests pytest

      - name: Run demo health checks
        env:
          DEMO_URL: ${{ matrix.environment == 'production' && secrets.PROD_DEMO_URL || secrets.STAGING_DEMO_URL }}
          API_KEY: ${{ matrix.environment == 'production' && secrets.PROD_API_KEY || secrets.STAGING_API_KEY }}
        run: |
          python tests/demo/health_check.py --url $DEMO_URL --api-key $API_KEY

      - name: Upload health check results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: demo-health-${{ matrix.environment }}
          path: tests/demo/health-report.json

  demo-performance-validation:
    name: Demo Performance Validation
    runs-on: ubuntu-24.04
    needs: [demo-health-check]
    if: github.event.inputs.full_validation == 'true' || github.event_name == 'schedule'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Install Playwright
        run: |
          cd tests/e2e
          npm ci
          npx playwright install --with-deps chromium

      - name: Run demo performance tests
        env:
          DEMO_URL: ${{ secrets.PROD_DEMO_URL }}
        run: |
          cd tests/e2e
          npx playwright test tests/demo-performance.spec.ts --project=chromium

      - name: Upload performance results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: demo-performance-results
          path: tests/e2e/test-results/

  demo-accessibility-check:
    name: Demo Accessibility Check
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Install dependencies
        run: |
          npm install -g @axe-core/cli

      - name: Run accessibility tests
        env:
          DEMO_URL: ${{ secrets.PROD_DEMO_URL }}
        run: |
          axe $DEMO_URL --save demo-accessibility-report.json

      - name: Upload accessibility results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: demo-accessibility-results
          path: demo-accessibility-report.json

  demo-scenario-validation:
    name: Demo Scenario Validation
    runs-on: ubuntu-24.04
    strategy:
      matrix:
        scenario: [scenario-1, scenario-2, scenario-3]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install dependencies
        run: |
          pip install requests pytest

      - name: Validate scenario
        env:
          DEMO_URL: ${{ secrets.PROD_DEMO_URL }}
          API_KEY: ${{ secrets.PROD_API_KEY }}
          SCENARIO_ID: ${{ matrix.scenario }}
        run: |
          python tests/demo/scenario_validation.py \
            --url $DEMO_URL \
            --api-key $API_KEY \
            --scenario $SCENARIO_ID

      - name: Upload scenario validation results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: scenario-validation-${{ matrix.scenario }}
          path: tests/demo/scenario-${{ matrix.scenario }}-report.json

  demo-load-test:
    name: Demo Load Test
    runs-on: ubuntu-24.04
    if: github.event.inputs.full_validation == 'true' || github.event_name == 'schedule'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup K6
        run: |
          sudo apt-key adv --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
          echo "deb https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
          sudo apt-get update
          sudo apt-get install k6

      - name: Run demo load test
        env:
          DEMO_URL: ${{ secrets.PROD_DEMO_URL }}
        run: |
          cd tests/performance
          k6 run \
            --env DEMO_URL=$DEMO_URL \
            --out json=demo-load-test-results.json \
            demo_load_test.js

      - name: Upload load test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: demo-load-test-results
          path: tests/performance/demo-load-test-results.json

  demo-monitoring-check:
    name: Demo Monitoring Check
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Check demo metrics
        env:
          PROMETHEUS_URL: ${{ secrets.PROMETHEUS_URL }}
          GRAFANA_URL: ${{ secrets.GRAFANA_URL }}
          GRAFANA_API_KEY: ${{ secrets.GRAFANA_API_KEY }}
        run: |
          # Check demo response time
          curl -s "$PROMETHEUS_URL/api/v1/query?query=demo_response_time_seconds" | \
            jq '.data.result[0].value[1] | tonumber' > demo_response_time.txt
          
          # Check demo error rate
          curl -s "$PROMETHEUS_URL/api/v1/query?query=rate(demo_errors_total[5m])" | \
            jq '.data.result[0].value[1] | tonumber' > demo_error_rate.txt
          
          # Validate metrics
          python -c "
          import sys
          response_time = float(open('demo_response_time.txt').read().strip())
          error_rate = float(open('demo_error_rate.txt').read().strip())
          
          if response_time > 1.0:
              print(f'Demo response time too high: {response_time}s')
              sys.exit(1)
          
          if error_rate > 0.01:
              print(f'Demo error rate too high: {error_rate}')
              sys.exit(1)
          
          print('Demo metrics within acceptable ranges')
          "

  generate-demo-report:
    name: Generate Demo Report
    runs-on: ubuntu-24.04
    needs: [demo-health-check, demo-performance-validation, demo-accessibility-check, demo-scenario-validation, demo-monitoring-check]
    if: always()
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download all artifacts
        uses: actions/download-artifact@v3

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install dependencies
        run: |
          pip install jinja2 markdown

      - name: Generate demo validation report
        run: |
          python scripts/generate_demo_report.py \
            --output demo-validation-report.html \
            --artifacts-dir .

      - name: Upload demo report
        uses: actions/upload-artifact@v3
        with:
          name: demo-validation-report
          path: demo-validation-report.html

      - name: Send report to Slack
        if: failure()
        uses: 8398a7/action-slack@v3
        with:
          status: 'failure'
          channel: '#demo-alerts'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
          fields: repo,message,commit,author,action,eventName,ref,workflow
          custom_payload: |
            {
              "text": "🚨 Demo System Validation Failed",
              "attachments": [
                {
                  "color": "danger",
                  "fields": [
                    {
                      "title": "Environment",
                      "value": "${{ github.event.inputs.environment || 'production' }}",
                      "short": true
                    },
                    {
                      "title": "Validation Time",
                      "value": "${{ github.run_started_at }}",
                      "short": true
                    }
                  ]
                }
              ]
            }

  alert-on-failure:
    name: Alert on Demo Failure
    runs-on: ubuntu-24.04
    needs: [demo-health-check, demo-performance-validation, demo-scenario-validation, demo-monitoring-check]
    if: failure()
    steps:
      - name: Send critical alert
        uses: dawidd6/action-send-mail@v3
        with:
          server_address: ${{ secrets.SMTP_SERVER }}
          server_port: 587
          username: ${{ secrets.SMTP_USERNAME }}
          password: ${{ secrets.SMTP_PASSWORD }}
          subject: '🚨 CRITICAL: Soil Master Demo System Failure'
          to: ${{ secrets.CRITICAL_ALERT_EMAIL }}
          from: 'Demo Monitoring <<EMAIL>>'
          body: |
            CRITICAL ALERT: Soil Master Demo System Validation Failed
            
            Environment: ${{ github.event.inputs.environment || 'production' }}
            Time: ${{ github.run_started_at }}
            Workflow: ${{ github.workflow }}
            Run ID: ${{ github.run_id }}
            
            Failed Jobs:
            ${{ toJson(needs) }}
            
            Immediate action required to restore demo functionality.
            
            View full details: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}

      - name: Create incident issue
        uses: actions/github-script@v6
        with:
          script: |
            const issue = await github.rest.issues.create({
              owner: context.repo.owner,
              repo: context.repo.repo,
              title: '🚨 Demo System Validation Failure',
              body: `
              ## Demo System Validation Failed
              
              **Environment:** ${{ github.event.inputs.environment || 'production' }}
              **Time:** ${{ github.run_started_at }}
              **Workflow Run:** [${{ github.run_id }}](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }})
              
              ### Failed Validations
              
              Please check the workflow logs for detailed failure information.
              
              ### Action Required
              
              - [ ] Investigate root cause
              - [ ] Fix identified issues
              - [ ] Re-run validation
              - [ ] Update stakeholders
              
              **Priority:** Critical
              **Impact:** Demo system unavailable for presentations
              `,
              labels: ['critical', 'demo', 'incident']
            });
            
            console.log('Created incident issue:', issue.data.number);
