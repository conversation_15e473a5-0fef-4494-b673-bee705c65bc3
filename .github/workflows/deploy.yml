name: Deploy to Production

on:
  push:
    branches: [ main ]
    tags: [ 'v*' ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # Build application for deployment
  build-application:
    name: Build Application
    runs-on: ubuntu-latest

    outputs:
      build-artifact: ${{ steps.upload.outputs.artifact-id }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: ./soil-frontend/package-lock.json

      - name: Install dependencies
        working-directory: ./soil-frontend
        run: npm ci --production

      - name: Build application
        working-directory: ./soil-frontend
        run: npm run build
        env:
          NODE_ENV: production
          NEXT_PUBLIC_API_URL: ${{ secrets.PRODUCTION_API_URL }}

      - name: Create deployment package
        working-directory: ./soil-frontend
        run: |
          tar -czf ../frontend-build.tar.gz \
            .next \
            public \
            package.json \
            package-lock.json \
            next.config.js \
            ecosystem.config.js

      - name: Upload build artifact
        id: upload
        uses: actions/upload-artifact@v4
        with:
          name: frontend-build
          path: frontend-build.tar.gz
          retention-days: 30

  # Deploy to staging
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: build-application
    if: github.ref == 'refs/heads/dev' || (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'staging')
    environment:
      name: staging
      url: https://staging.yieldsight.com

    steps:
      - name: Download build artifact
        uses: actions/download-artifact@v4
        with:
          name: frontend-build

      - name: Deploy to staging server
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.STAGING_HOST }}
          username: ${{ secrets.STAGING_USER }}
          key: ${{ secrets.STAGING_SSH_KEY }}
          script: |
            # Stop current application
            pm2 stop yieldsight-frontend || true

            # Backup current deployment
            sudo cp -r /var/www/yieldsight-frontend /var/www/yieldsight-frontend.backup.$(date +%Y%m%d%H%M%S)

            # Create deployment directory
            sudo mkdir -p /var/www/yieldsight-frontend
            sudo chown $USER:$USER /var/www/yieldsight-frontend

      - name: Upload and extract build
        uses: appleboy/scp-action@v0.1.4
        with:
          host: ${{ secrets.STAGING_HOST }}
          username: ${{ secrets.STAGING_USER }}
          key: ${{ secrets.STAGING_SSH_KEY }}
          source: frontend-build.tar.gz
          target: /tmp/

      - name: Complete deployment
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.STAGING_HOST }}
          username: ${{ secrets.STAGING_USER }}
          key: ${{ secrets.STAGING_SSH_KEY }}
          script: |
            # Extract build
            cd /var/www/yieldsight-frontend
            tar -xzf /tmp/frontend-build.tar.gz

            # Install dependencies if needed
            npm ci --production

            # Start application
            pm2 start ecosystem.config.js
            pm2 save

      - name: Run smoke tests
        run: |
          sleep 30  # Wait for application to start
          curl -f https://staging.yieldsight.com/health || exit 1

  # Deploy to production
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: build-images
    if: github.ref == 'refs/heads/main' || (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'production')
    environment:
      name: production
      url: https://yieldsight.com

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: Create database backup
        run: |
          aws rds create-db-snapshot \
            --db-instance-identifier soil-master-prod \
            --db-snapshot-identifier soil-master-backup-$(date +%Y%m%d%H%M%S)

      - name: Deploy to ECS with blue-green
        run: |
          # Update task definitions with new images
          FRONTEND_TASK_DEF=$(aws ecs describe-task-definition \
            --task-definition soil-master-frontend-prod \
            --query 'taskDefinition' \
            --output json)
          
          BACKEND_TASK_DEF=$(aws ecs describe-task-definition \
            --task-definition soil-master-backend-prod \
            --query 'taskDefinition' \
            --output json)

          # Update image URIs in task definitions
          echo "$FRONTEND_TASK_DEF" | jq \
            --arg IMAGE "${{ needs.build-images.outputs.frontend-image }}" \
            '.containerDefinitions[0].image = $IMAGE' > frontend-task-def.json

          echo "$BACKEND_TASK_DEF" | jq \
            --arg IMAGE "${{ needs.build-images.outputs.backend-image }}" \
            '.containerDefinitions[0].image = $IMAGE' > backend-task-def.json

          # Register new task definitions
          aws ecs register-task-definition \
            --cli-input-json file://frontend-task-def.json

          aws ecs register-task-definition \
            --cli-input-json file://backend-task-def.json

          # Update services
          aws ecs update-service \
            --cluster soil-master-prod \
            --service frontend-service \
            --task-definition soil-master-frontend-prod

          aws ecs update-service \
            --cluster soil-master-prod \
            --service backend-service \
            --task-definition soil-master-backend-prod

      - name: Wait for deployment
        run: |
          aws ecs wait services-stable \
            --cluster soil-master-prod \
            --services frontend-service backend-service

      - name: Run production smoke tests
        run: |
          curl -f https://yieldsight.com/health || exit 1
          curl -f https://api.yieldsight.com/health || exit 1

      - name: Run post-deployment tests
        run: |
          # Run critical path tests
          npm run test:production --prefix ./soil-frontend

  # Database migrations
  migrate-database:
    name: Run Database Migrations
    runs-on: ubuntu-latest
    needs: build-images
    if: github.ref == 'refs/heads/main'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Install dependencies
        working-directory: ./soil-backend
        run: |
          pip install poetry
          poetry install --no-dev

      - name: Run migrations
        working-directory: ./soil-backend
        run: poetry run alembic upgrade head
        env:
          DATABASE_URL: ${{ secrets.PRODUCTION_DATABASE_URL }}

  # Rollback capability
  rollback:
    name: Rollback Deployment
    runs-on: ubuntu-latest
    if: failure()
    needs: [deploy-production]

    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: Rollback ECS services
        run: |
          # Get previous task definition revisions
          PREV_FRONTEND_TASK_DEF=$(aws ecs list-task-definitions \
            --family-prefix soil-master-frontend-prod \
            --status ACTIVE \
            --sort DESC \
            --query 'taskDefinitionArns[1]' \
            --output text)

          PREV_BACKEND_TASK_DEF=$(aws ecs list-task-definitions \
            --family-prefix soil-master-backend-prod \
            --status ACTIVE \
            --sort DESC \
            --query 'taskDefinitionArns[1]' \
            --output text)

          # Rollback to previous versions
          aws ecs update-service \
            --cluster soil-master-prod \
            --service frontend-service \
            --task-definition $PREV_FRONTEND_TASK_DEF

          aws ecs update-service \
            --cluster soil-master-prod \
            --service backend-service \
            --task-definition $PREV_BACKEND_TASK_DEF

  # Notification
  notify-deployment:
    name: Notify Deployment
    runs-on: ubuntu-latest
    needs: [deploy-staging, deploy-production]
    if: always()

    steps:
      - name: Notify Slack on success
        if: ${{ needs.deploy-production.result == 'success' }}
        uses: 8398a7/action-slack@v3
        with:
          status: success
          channel: '#deployments'
          text: '🚀 Production deployment successful for ${{ github.ref }}'
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

      - name: Notify Slack on failure
        if: ${{ needs.deploy-production.result == 'failure' }}
        uses: 8398a7/action-slack@v3
        with:
          status: failure
          channel: '#deployments'
          text: '❌ Production deployment failed for ${{ github.ref }}'
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
