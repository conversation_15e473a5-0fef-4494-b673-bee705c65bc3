name: Soil Master v1.0.2 CI/CD Pipeline

on:
  push:
    branches: [ main, dev, staging ]
  pull_request:
    branches: [ main, dev ]
  release:
    types: [ published ]

env:
  NODE_VERSION: '18'
  PYTHON_VERSION: '3.11'
  POSTGRES_VERSION: '15'
  REDIS_VERSION: '7'

jobs:
  # Code Quality and Security Checks
  code-quality:
    name: Code Quality & Security
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: soil-frontend/package-lock.json

      - name: Install Python dependencies
        run: |
          cd soil-backend
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install bandit safety black isort flake8 mypy

      - name: Install Node.js dependencies
        run: |
          cd soil-frontend
          npm ci

      - name: Python code formatting check
        run: |
          cd soil-backend
          black --check .
          isort --check-only .

      - name: Python linting
        run: |
          cd soil-backend
          flake8 .
          mypy app/

      - name: Python security scan
        run: |
          cd soil-backend
          bandit -r app/ -f json -o bandit-report.json || true
          safety check --json --output safety-report.json || true

      - name: TypeScript/JavaScript linting
        run: |
          cd soil-frontend
          npm run lint
          npm run type-check

      - name: Frontend security audit
        run: |
          cd soil-frontend
          npm audit --audit-level=moderate

      - name: Upload security reports
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: security-reports
          path: |
            soil-backend/bandit-report.json
            soil-backend/safety-report.json

  # Backend Testing
  backend-tests:
    name: Backend Tests
    runs-on: ubuntu-24.04
    services:
      postgres:
        image: postgres:15-alpine
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_USER: test_user
          POSTGRES_DB: soil_master_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install dependencies
        run: |
          cd soil-backend
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install pytest pytest-cov pytest-xdist pytest-mock pytest-asyncio

      - name: Setup test database
        env:
          DATABASE_URL: postgresql://test_user:test_password@localhost:5432/soil_master_test
        run: |
          cd soil-backend
          alembic upgrade head

      - name: Run backend tests
        env:
          DATABASE_URL: postgresql://test_user:test_password@localhost:5432/soil_master_test
          REDIS_URL: redis://localhost:6379/1
          ENVIRONMENT: test
        run: |
          cd soil-backend
          pytest \
            --cov=app \
            --cov-report=xml \
            --cov-report=html \
            --cov-fail-under=80 \
            --junitxml=pytest-report.xml \
            -v tests/

      - name: Upload test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: backend-test-results
          path: |
            soil-backend/pytest-report.xml
            soil-backend/htmlcov/
            soil-backend/coverage.xml

  # Frontend Testing
  frontend-tests:
    name: Frontend Tests
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: soil-frontend/package-lock.json

      - name: Install dependencies
        run: |
          cd soil-frontend
          npm ci

      - name: Run frontend tests
        run: |
          cd soil-frontend
          npm test -- \
            --coverage \
            --coverageReporters=lcov,text,html \
            --coverageThreshold='{"global":{"branches":80,"functions":80,"lines":80,"statements":80}}' \
            --watchAll=false \
            --ci

      - name: Upload test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: frontend-test-results
          path: |
            soil-frontend/coverage/

  # Demo System Tests
  demo-tests:
    name: Demo System Tests
    runs-on: ubuntu-24.04
    needs: [backend-tests, frontend-tests]
    services:
      postgres:
        image: postgres:15-alpine
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_USER: test_user
          POSTGRES_DB: soil_master_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: soil-frontend/package-lock.json

      - name: Install backend dependencies
        run: |
          cd soil-backend
          python -m pip install --upgrade pip
          pip install -r requirements.txt

      - name: Install frontend dependencies
        run: |
          cd soil-frontend
          npm ci

      - name: Setup test environment
        env:
          DATABASE_URL: postgresql://test_user:test_password@localhost:5432/soil_master_test
        run: |
          cd soil-backend
          alembic upgrade head

      - name: Start backend server
        env:
          DATABASE_URL: postgresql://test_user:test_password@localhost:5432/soil_master_test
          REDIS_URL: redis://localhost:6379/1
          ENVIRONMENT: test
        run: |
          cd soil-backend
          python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 &
          sleep 10

      - name: Build frontend
        run: |
          cd soil-frontend
          npm run build

      - name: Start frontend server
        run: |
          cd soil-frontend
          npm start &
          sleep 15

      - name: Run demo tests
        run: |
          cd tests/demo
          python -m pytest demo_tests/ -v --junitxml=demo-test-results.xml

      - name: Upload demo test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: demo-test-results
          path: tests/demo/demo-test-results.xml

  # End-to-End Tests
  e2e-tests:
    name: E2E Tests
    runs-on: ubuntu-24.04
    needs: [demo-tests]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Install Playwright
        run: |
          cd tests/e2e
          npm ci
          npx playwright install --with-deps

      - name: Run E2E tests
        run: |
          cd tests/e2e
          npx playwright test

      - name: Upload E2E results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: e2e-test-results
          path: |
            tests/e2e/test-results/
            tests/e2e/playwright-report/

  # Build and Package
  build:
    name: Build & Package
    runs-on: ubuntu-24.04
    needs: [code-quality, backend-tests, frontend-tests]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: soil-frontend/package-lock.json

      - name: Build backend
        run: |
          cd soil-backend
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          python -m py_compile app/main.py

      - name: Build frontend
        run: |
          cd soil-frontend
          npm ci
          npm run build

      - name: Create deployment package
        run: |
          mkdir -p dist/
          tar -czf dist/soil-master-${{ github.sha }}.tar.gz \
            --exclude='*/node_modules' \
            --exclude='*/venv' \
            --exclude='*/__pycache__' \
            --exclude='*/coverage' \
            --exclude='*/test-results' \
            .

      - name: Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: build-artifacts
          path: dist/

  # Security Scanning
  security-scan:
    name: Security Scanning
    runs-on: ubuntu-24.04
    needs: [build]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'

  # Performance Testing
  performance-tests:
    name: Performance Tests
    runs-on: ubuntu-24.04
    needs: [build]
    if: github.ref == 'refs/heads/main' || github.event_name == 'release'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup K6
        run: |
          sudo apt-key adv --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
          echo "deb https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
          sudo apt-get update
          sudo apt-get install k6

      - name: Run performance tests
        run: |
          cd tests/performance
          k6 run --out json=performance-results.json load_test.js

      - name: Upload performance results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: performance-results
          path: tests/performance/performance-results.json

  # Staging Deployment
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-24.04
    needs: [e2e-tests, security-scan]
    if: github.ref == 'refs/heads/dev'
    environment: staging
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download build artifacts
        uses: actions/download-artifact@v3
        with:
          name: build-artifacts
          path: dist/

      - name: Deploy to staging
        env:
          STAGING_HOST: ${{ secrets.STAGING_HOST }}
          STAGING_USER: ${{ secrets.STAGING_USER }}
          STAGING_KEY: ${{ secrets.STAGING_SSH_KEY }}
        run: |
          echo "$STAGING_KEY" > staging_key
          chmod 600 staging_key
          scp -i staging_key -o StrictHostKeyChecking=no \
            dist/soil-master-${{ github.sha }}.tar.gz \
            $STAGING_USER@$STAGING_HOST:/tmp/
          ssh -i staging_key -o StrictHostKeyChecking=no \
            $STAGING_USER@$STAGING_HOST \
            "cd /opt/soilmaster && \
             tar -xzf /tmp/soil-master-${{ github.sha }}.tar.gz && \
             ./deployment/staging/deploy.sh"

  # Production Deployment
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-24.04
    needs: [performance-tests]
    if: github.ref == 'refs/heads/main' || github.event_name == 'release'
    environment: production
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download build artifacts
        uses: actions/download-artifact@v3
        with:
          name: build-artifacts
          path: dist/

      - name: Deploy to production
        env:
          PROD_HOST: ${{ secrets.PROD_HOST }}
          PROD_USER: ${{ secrets.PROD_USER }}
          PROD_KEY: ${{ secrets.PROD_SSH_KEY }}
        run: |
          echo "$PROD_KEY" > prod_key
          chmod 600 prod_key
          scp -i prod_key -o StrictHostKeyChecking=no \
            dist/soil-master-${{ github.sha }}.tar.gz \
            $PROD_USER@$PROD_HOST:/tmp/
          ssh -i prod_key -o StrictHostKeyChecking=no \
            $PROD_USER@$PROD_HOST \
            "cd /opt/soilmaster && \
             ./deployment/production/deploy.sh"

      - name: Run post-deployment tests
        env:
          PROD_HOST: ${{ secrets.PROD_HOST }}
          PROD_USER: ${{ secrets.PROD_USER }}
          PROD_KEY: ${{ secrets.PROD_SSH_KEY }}
        run: |
          ssh -i prod_key -o StrictHostKeyChecking=no \
            $PROD_USER@$PROD_HOST \
            "cd /opt/soilmaster && \
             ./deployment/production/health-check.sh"

  # Notification
  notify:
    name: Notify Deployment
    runs-on: ubuntu-24.04
    needs: [deploy-production]
    if: always()
    steps:
      - name: Notify Slack
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: '#deployments'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
          fields: repo,message,commit,author,action,eventName,ref,workflow

      - name: Notify email
        if: failure()
        uses: dawidd6/action-send-mail@v3
        with:
          server_address: ${{ secrets.SMTP_SERVER }}
          server_port: 587
          username: ${{ secrets.SMTP_USERNAME }}
          password: ${{ secrets.SMTP_PASSWORD }}
          subject: 'Soil Master Deployment Failed'
          to: ${{ secrets.NOTIFICATION_EMAIL }}
          from: 'CI/CD Pipeline <<EMAIL>>'
          body: |
            Deployment of Soil Master v1.0.2 has failed.
            
            Commit: ${{ github.sha }}
            Branch: ${{ github.ref }}
            Author: ${{ github.actor }}
            
            Please check the GitHub Actions logs for details.
