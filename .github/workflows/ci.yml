name: Continuous Integration

on:
  push:
    branches: [ main, dev ]
  pull_request:
    branches: [ main, dev ]

env:
  NODE_VERSION: '18'
  PYTHON_VERSION: '3.11'

jobs:
  # Frontend CI
  frontend-ci:
    name: Frontend CI
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./soil-frontend

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: ./soil-frontend/package-lock.json

      - name: Install dependencies
        run: npm ci

      - name: Run ESLint
        run: npm run lint

      - name: Run TypeScript check
        run: npm run type-check

      - name: Run unit tests
        run: npm run test:ci
        env:
          CI: true

      - name: Run integration tests
        run: npm run test:integration
        env:
          CI: true

      - name: Build application
        run: npm run build
        env:
          NODE_ENV: production
          NEXT_PUBLIC_API_URL: ${{ secrets.STAGING_API_URL }}

      - name: Run E2E tests
        run: npm run test:e2e
        env:
          CI: true
          PLAYWRIGHT_BASE_URL: http://localhost:3000

      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: frontend-test-results
          path: |
            ./soil-frontend/coverage/
            ./soil-frontend/test-results/
            ./soil-frontend/playwright-report/

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: frontend-build
          path: ./soil-frontend/.next/

  # Backend CI
  backend-ci:
    name: Backend CI
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./soil-backend

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: soil_master_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install Poetry
        uses: snok/install-poetry@v1
        with:
          version: latest
          virtualenvs-create: true
          virtualenvs-in-project: true

      - name: Load cached venv
        id: cached-poetry-dependencies
        uses: actions/cache@v3
        with:
          path: ./soil-backend/.venv
          key: venv-${{ runner.os }}-${{ env.PYTHON_VERSION }}-${{ hashFiles('**/poetry.lock') }}

      - name: Install dependencies
        if: steps.cached-poetry-dependencies.outputs.cache-hit != 'true'
        run: poetry install --no-interaction --no-root

      - name: Install project
        run: poetry install --no-interaction

      - name: Run code formatting check
        run: poetry run black --check .

      - name: Run import sorting check
        run: poetry run isort --check-only .

      - name: Run linting
        run: poetry run flake8 .

      - name: Run type checking
        run: poetry run mypy .

      - name: Run security checks
        run: poetry run bandit -r . -x tests/

      - name: Run tests
        run: poetry run pytest --cov=. --cov-report=xml --cov-report=html
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/soil_master_test
          REDIS_URL: redis://localhost:6379/1
          JWT_SECRET: test-secret-key-for-ci
          ENVIRONMENT: test

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./soil-backend/coverage.xml
          flags: backend
          name: backend-coverage

      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: backend-test-results
          path: |
            ./soil-backend/htmlcov/
            ./soil-backend/coverage.xml

  # Security scanning
  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: [frontend-ci, backend-ci]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: Upload Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'

      - name: Frontend dependency check
        working-directory: ./soil-frontend
        run: |
          npm audit --audit-level=high
          npx audit-ci --config audit-ci.json

      - name: Backend dependency check
        working-directory: ./soil-backend
        run: |
          poetry run safety check
          poetry run pip-audit

  # Performance testing
  performance-test:
    name: Performance Test
    runs-on: ubuntu-latest
    needs: [frontend-ci]
    if: github.event_name == 'pull_request'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Install dependencies
        working-directory: ./soil-frontend
        run: npm ci

      - name: Build application
        working-directory: ./soil-frontend
        run: npm run build

      - name: Start application
        working-directory: ./soil-frontend
        run: npm start &
        env:
          PORT: 3000

      - name: Wait for application
        run: npx wait-on http://localhost:3000

      - name: Run Lighthouse CI
        working-directory: ./soil-frontend
        run: npx lhci autorun
        env:
          LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}

      - name: Upload Lighthouse results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: lighthouse-results
          path: ./soil-frontend/.lighthouseci/

  # Code quality
  code-quality:
    name: Code Quality
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: SonarCloud Scan
        uses: SonarSource/sonarcloud-github-action@master
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}

  # Accessibility testing
  accessibility-test:
    name: Accessibility Test
    runs-on: ubuntu-latest
    needs: [frontend-ci]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Install dependencies
        working-directory: ./soil-frontend
        run: npm ci

      - name: Build and start application
        working-directory: ./soil-frontend
        run: |
          npm run build
          npm start &
        env:
          PORT: 3000

      - name: Wait for application
        run: npx wait-on http://localhost:3000

      - name: Run accessibility tests
        working-directory: ./soil-frontend
        run: npm run test:a11y

      - name: Upload accessibility results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: accessibility-results
          path: ./soil-frontend/accessibility-report/

  # Notification
  notify:
    name: Notify
    runs-on: ubuntu-latest
    needs: [frontend-ci, backend-ci, security-scan, code-quality]
    if: always()

    steps:
      - name: Notify Slack on success
        if: ${{ needs.frontend-ci.result == 'success' && needs.backend-ci.result == 'success' }}
        uses: 8398a7/action-slack@v3
        with:
          status: success
          channel: '#ci-cd'
          text: '✅ CI pipeline passed for ${{ github.ref }}'
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

      - name: Notify Slack on failure
        if: ${{ needs.frontend-ci.result == 'failure' || needs.backend-ci.result == 'failure' }}
        uses: 8398a7/action-slack@v3
        with:
          status: failure
          channel: '#ci-cd'
          text: '❌ CI pipeline failed for ${{ github.ref }}'
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
