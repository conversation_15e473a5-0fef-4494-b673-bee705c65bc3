name: Security Scanning

on:
  schedule:
    # Run security scans daily at 2 AM UTC
    - cron: '0 2 * * *'
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:

env:
  PYTHON_VERSION: '3.11'
  NODE_VERSION: '18'

jobs:
  dependency-scan:
    name: Dependency Security Scan
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Install Python dependencies
        run: |
          cd soil-backend
          python -m pip install --upgrade pip
          pip install safety pip-audit

      - name: Python dependency scan with Safety
        run: |
          cd soil-backend
          safety check --json --output safety-report.json || true
          safety check --short-report

      - name: Python dependency scan with pip-audit
        run: |
          cd soil-backend
          pip-audit --format=json --output=pip-audit-report.json || true
          pip-audit

      - name: Node.js dependency scan
        run: |
          cd soil-frontend
          npm ci
          npm audit --audit-level=moderate --json > npm-audit-report.json || true
          npm audit --audit-level=moderate

      - name: Upload dependency scan results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: dependency-scan-results
          path: |
            soil-backend/safety-report.json
            soil-backend/pip-audit-report.json
            soil-frontend/npm-audit-report.json

  code-security-scan:
    name: Code Security Scan
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install security tools
        run: |
          pip install bandit semgrep

      - name: Run Bandit security scan
        run: |
          cd soil-backend
          bandit -r app/ -f json -o bandit-report.json || true
          bandit -r app/ -f txt

      - name: Run Semgrep security scan
        run: |
          semgrep --config=auto --json --output=semgrep-report.json . || true
          semgrep --config=auto .

      - name: Upload code security results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: code-security-results
          path: |
            soil-backend/bandit-report.json
            semgrep-report.json

  container-security-scan:
    name: Container Security Scan
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run Trivy filesystem scan
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-fs-results.sarif'

      - name: Upload Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: 'trivy-fs-results.sarif'

      - name: Run Trivy config scan
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'config'
          scan-ref: '.'
          format: 'json'
          output: 'trivy-config-results.json'

      - name: Upload Trivy config results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: trivy-config-results
          path: trivy-config-results.json

  secrets-scan:
    name: Secrets Scan
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Run TruffleHog secrets scan
        uses: trufflesecurity/trufflehog@main
        with:
          path: ./
          base: main
          head: HEAD
          extra_args: --debug --only-verified

      - name: Run GitLeaks secrets scan
        uses: gitleaks/gitleaks-action@v2
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          GITLEAKS_LICENSE: ${{ secrets.GITLEAKS_LICENSE }}

  infrastructure-security-scan:
    name: Infrastructure Security Scan
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install security tools
        run: |
          # Install Checkov for infrastructure as code scanning
          pip install checkov

      - name: Run Checkov IaC scan
        run: |
          checkov -d . --framework terraform,kubernetes,dockerfile,github_actions \
            --output json --output-file checkov-report.json || true
          checkov -d . --framework terraform,kubernetes,dockerfile,github_actions

      - name: Upload infrastructure scan results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: infrastructure-security-results
          path: checkov-report.json

  web-security-scan:
    name: Web Security Scan
    runs-on: ubuntu-24.04
    if: github.ref == 'refs/heads/main' || github.event_name == 'schedule'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup OWASP ZAP
        run: |
          docker pull owasp/zap2docker-stable

      - name: Run OWASP ZAP baseline scan
        env:
          TARGET_URL: ${{ secrets.STAGING_URL || 'https://staging.soilmaster.com' }}
        run: |
          docker run -v $(pwd):/zap/wrk/:rw \
            -t owasp/zap2docker-stable \
            zap-baseline.py \
            -t $TARGET_URL \
            -J zap-baseline-report.json \
            -r zap-baseline-report.html || true

      - name: Run OWASP ZAP full scan on demo
        env:
          DEMO_URL: ${{ secrets.STAGING_DEMO_URL || 'https://staging.soilmaster.com/demo' }}
        run: |
          docker run -v $(pwd):/zap/wrk/:rw \
            -t owasp/zap2docker-stable \
            zap-full-scan.py \
            -t $DEMO_URL \
            -J zap-demo-report.json \
            -r zap-demo-report.html || true

      - name: Upload web security results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: web-security-results
          path: |
            zap-baseline-report.json
            zap-baseline-report.html
            zap-demo-report.json
            zap-demo-report.html

  ssl-security-check:
    name: SSL/TLS Security Check
    runs-on: ubuntu-24.04
    if: github.ref == 'refs/heads/main' || github.event_name == 'schedule'
    steps:
      - name: Install SSL testing tools
        run: |
          sudo apt-get update
          sudo apt-get install -y openssl

      - name: Check SSL configuration
        env:
          DOMAIN: ${{ secrets.PROD_DOMAIN || 'soilmaster.com' }}
        run: |
          # Check SSL certificate
          echo | openssl s_client -servername $DOMAIN -connect $DOMAIN:443 2>/dev/null | \
            openssl x509 -noout -dates > ssl-cert-info.txt
          
          # Check SSL configuration
          echo | openssl s_client -servername $DOMAIN -connect $DOMAIN:443 2>/dev/null | \
            openssl x509 -noout -text > ssl-config-info.txt
          
          # Test SSL Labs API (if available)
          curl -s "https://api.ssllabs.com/api/v3/analyze?host=$DOMAIN&publish=off&all=done" \
            > ssl-labs-report.json || echo "SSL Labs API not available"

      - name: Upload SSL security results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: ssl-security-results
          path: |
            ssl-cert-info.txt
            ssl-config-info.txt
            ssl-labs-report.json

  compliance-check:
    name: Compliance Check
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install compliance tools
        run: |
          pip install detect-secrets

      - name: Run secrets detection
        run: |
          detect-secrets scan --all-files --baseline .secrets.baseline || true
          detect-secrets audit .secrets.baseline || true

      - name: Check for compliance violations
        run: |
          # Check for hardcoded credentials
          grep -r -i "password\|secret\|key\|token" --include="*.py" --include="*.js" --include="*.ts" \
            --exclude-dir=node_modules --exclude-dir=venv . > compliance-check.txt || true
          
          # Check for TODO/FIXME comments that might indicate security issues
          grep -r -i "TODO.*security\|FIXME.*security\|XXX.*security" \
            --include="*.py" --include="*.js" --include="*.ts" \
            --exclude-dir=node_modules --exclude-dir=venv . >> compliance-check.txt || true

      - name: Upload compliance results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: compliance-results
          path: |
            .secrets.baseline
            compliance-check.txt

  generate-security-report:
    name: Generate Security Report
    runs-on: ubuntu-24.04
    needs: [dependency-scan, code-security-scan, container-security-scan, secrets-scan, infrastructure-security-scan, compliance-check]
    if: always()
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download all security artifacts
        uses: actions/download-artifact@v3

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install report generation dependencies
        run: |
          pip install jinja2 markdown json2html

      - name: Generate security report
        run: |
          python scripts/generate_security_report.py \
            --output security-report.html \
            --artifacts-dir .

      - name: Upload security report
        uses: actions/upload-artifact@v3
        with:
          name: security-report
          path: security-report.html

      - name: Check for critical vulnerabilities
        run: |
          python scripts/check_critical_vulnerabilities.py \
            --artifacts-dir . \
            --output critical-vulns.json

      - name: Upload critical vulnerabilities
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: critical-vulnerabilities
          path: critical-vulns.json

  notify-security-team:
    name: Notify Security Team
    runs-on: ubuntu-24.04
    needs: [generate-security-report]
    if: failure() || (success() && github.event_name == 'schedule')
    steps:
      - name: Download critical vulnerabilities
        uses: actions/download-artifact@v3
        with:
          name: critical-vulnerabilities

      - name: Check for critical issues
        id: check_critical
        run: |
          if [ -f critical-vulns.json ]; then
            CRITICAL_COUNT=$(jq '.critical_count' critical-vulns.json)
            HIGH_COUNT=$(jq '.high_count' critical-vulns.json)
            echo "critical_count=$CRITICAL_COUNT" >> $GITHUB_OUTPUT
            echo "high_count=$HIGH_COUNT" >> $GITHUB_OUTPUT
            
            if [ "$CRITICAL_COUNT" -gt 0 ] || [ "$HIGH_COUNT" -gt 5 ]; then
              echo "alert_required=true" >> $GITHUB_OUTPUT
            else
              echo "alert_required=false" >> $GITHUB_OUTPUT
            fi
          else
            echo "alert_required=false" >> $GITHUB_OUTPUT
          fi

      - name: Send security alert
        if: steps.check_critical.outputs.alert_required == 'true'
        uses: dawidd6/action-send-mail@v3
        with:
          server_address: ${{ secrets.SMTP_SERVER }}
          server_port: 587
          username: ${{ secrets.SMTP_USERNAME }}
          password: ${{ secrets.SMTP_PASSWORD }}
          subject: '🔒 Security Alert: Critical Vulnerabilities Detected'
          to: ${{ secrets.SECURITY_TEAM_EMAIL }}
          from: 'Security Scanner <<EMAIL>>'
          body: |
            Security scan has detected critical vulnerabilities in Soil Master v1.0.2
            
            Critical Vulnerabilities: ${{ steps.check_critical.outputs.critical_count }}
            High Severity Vulnerabilities: ${{ steps.check_critical.outputs.high_count }}
            
            Scan Time: ${{ github.run_started_at }}
            Workflow: ${{ github.workflow }}
            
            Please review the security report and take immediate action.
            
            View full report: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}

      - name: Create security issue
        if: steps.check_critical.outputs.alert_required == 'true'
        uses: actions/github-script@v6
        with:
          script: |
            const issue = await github.rest.issues.create({
              owner: context.repo.owner,
              repo: context.repo.repo,
              title: '🔒 Security Alert: Critical Vulnerabilities Detected',
              body: `
              ## Security Vulnerabilities Detected
              
              **Critical:** ${{ steps.check_critical.outputs.critical_count }}
              **High:** ${{ steps.check_critical.outputs.high_count }}
              **Scan Time:** ${{ github.run_started_at }}
              
              ### Action Required
              
              - [ ] Review security report
              - [ ] Prioritize critical vulnerabilities
              - [ ] Create remediation plan
              - [ ] Update dependencies
              - [ ] Re-run security scan
              
              **Workflow:** [${{ github.run_id }}](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }})
              `,
              labels: ['security', 'critical', 'vulnerability']
            });

      - name: Send Slack notification
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: '#security'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
          fields: repo,message,commit,author,action,eventName,ref,workflow
          custom_payload: |
            {
              "text": "🔒 Security Scan Results",
              "attachments": [
                {
                  "color": "${{ steps.check_critical.outputs.alert_required == 'true' && 'danger' || 'good' }}",
                  "fields": [
                    {
                      "title": "Critical Vulnerabilities",
                      "value": "${{ steps.check_critical.outputs.critical_count || '0' }}",
                      "short": true
                    },
                    {
                      "title": "High Severity",
                      "value": "${{ steps.check_critical.outputs.high_count || '0' }}",
                      "short": true
                    }
                  ]
                }
              ]
            }
