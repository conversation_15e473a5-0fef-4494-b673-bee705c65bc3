# Soil Master v1.0.2 Production Deployment Guide

## Overview

This comprehensive guide provides step-by-step instructions for deploying Soil Master v1.0.2 in a production environment with enterprise-grade security, monitoring, and reliability.

## Prerequisites

### System Requirements

- **Operating System**: Ubuntu Server 24.04 LTS
- **CPU**: 4+ cores (8+ recommended)
- **RAM**: 8GB minimum (16GB+ recommended)
- **Storage**: 100GB+ SSD storage
- **Network**: Static IP address with domain name

### Required Software

- Node.js 18+ LTS
- Python 3.11+
- PostgreSQL 15+
- Redis 7+
- Nginx 1.24+
- PM2 (latest)
- Docker (optional, for development)

## Pre-Deployment Checklist

### 1. Server Preparation

```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install essential packages
sudo apt install -y curl wget git unzip software-properties-common

# Create application user
sudo useradd -m -s /bin/bash soilmaster
sudo usermod -aG sudo soilmaster

# Set up SSH key authentication
sudo mkdir -p /home/<USER>/.ssh
sudo chown soilmaster:soilmaster /home/<USER>/.ssh
sudo chmod 700 /home/<USER>/.ssh
```

### 2. Domain and DNS Configuration

- Configure DNS A record pointing to server IP
- Set up subdomain for API (api.yourdomain.com)
- Verify DNS propagation

### 3. Security Preparation

- Generate strong passwords for database and Redis
- Prepare SSL certificate (Let's Encrypt recommended)
- Configure firewall rules
- Set up monitoring and alerting

## Step-by-Step Deployment

### Phase 1: Infrastructure Setup (30-45 minutes)

#### 1.1 Install Node.js

```bash
# Install Node.js 18 LTS
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Verify installation
node --version
npm --version
```

#### 1.2 Install Python and Dependencies

```bash
# Install Python 3.11
sudo apt install -y python3.11 python3.11-venv python3.11-dev python3-pip

# Create Python virtual environment
sudo -u soilmaster python3.11 -m venv /opt/soilmaster/venv
```

#### 1.3 Install PostgreSQL

```bash
# Install PostgreSQL 15
sudo apt install -y postgresql postgresql-contrib

# Start and enable PostgreSQL
sudo systemctl start postgresql
sudo systemctl enable postgresql

# Create database and user
sudo -u postgres psql << EOF
CREATE DATABASE soil_master;
CREATE USER soil_master WITH ENCRYPTED PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE soil_master TO soil_master;
ALTER USER soil_master CREATEDB;
\q
EOF
```

#### 1.4 Install Redis

```bash
# Install Redis
sudo apt install -y redis-server

# Configure Redis
sudo sed -i 's/^# requirepass foobared/requirepass your_redis_password/' /etc/redis/redis.conf
sudo sed -i 's/^bind 127.0.0.1 ::1/bind 127.0.0.1/' /etc/redis/redis.conf

# Start and enable Redis
sudo systemctl restart redis-server
sudo systemctl enable redis-server
```

#### 1.5 Install Nginx

```bash
# Install Nginx
sudo apt install -y nginx

# Start and enable Nginx
sudo systemctl start nginx
sudo systemctl enable nginx
```

#### 1.6 Install PM2

```bash
# Install PM2 globally
sudo npm install -g pm2

# Set up PM2 startup script
sudo pm2 startup systemd -u soilmaster --hp /home/<USER>
```

### Phase 2: Application Deployment (45-60 minutes)

#### 2.1 Clone Repository

```bash
# Switch to application user
sudo su - soilmaster

# Create application directory
sudo mkdir -p /opt/soilmaster
sudo chown soilmaster:soilmaster /opt/soilmaster

# Clone repository
cd /opt/soilmaster
git clone https://github.com/Yield-Sight-System/soil-master.git .
```

#### 2.2 Backend Setup

```bash
# Navigate to backend directory
cd /opt/soilmaster/soil-backend

# Activate virtual environment
source /opt/soilmaster/venv/bin/activate

# Install Python dependencies
pip install -r requirements.txt

# Create environment file
cat > .env << EOF
DATABASE_URL=postgresql://soil_master:your_secure_password@localhost:5432/soil_master
REDIS_URL=redis://:your_redis_password@localhost:6379
SECRET_KEY=$(openssl rand -base64 32)
JWT_SECRET=$(openssl rand -base64 32)
ENVIRONMENT=production
LOG_LEVEL=info
DEMO_MODE=true
EOF

# Run database migrations
python -m alembic upgrade head

# Load demo data
python scripts/load_demo_data.py
```

#### 2.3 Frontend Setup

```bash
# Navigate to frontend directory
cd /opt/soilmaster/soil-frontend

# Install Node.js dependencies
npm ci --production

# Create environment file
cat > .env.local << EOF
NEXT_PUBLIC_API_BASE_URL=https://api.yourdomain.com
NEXT_PUBLIC_WS_URL=wss://api.yourdomain.com/ws
NEXT_PUBLIC_DEMO_MODE=true
NEXT_PUBLIC_MAPBOX_TOKEN=your_mapbox_token
EOF

# Build production application
npm run build
```

### Phase 3: Security Hardening (30-45 minutes)

#### 3.1 Run Security Hardening Script

```bash
# Run enterprise security hardening
sudo /opt/soilmaster/security/enterprise-security-hardening.sh
```

#### 3.2 Configure SSL Certificates

```bash
# Set up SSL certificates with Let's Encrypt
sudo /opt/soilmaster/security/enterprise-ssl-setup.sh yourdomain.com <EMAIL>
```

#### 3.3 Configure Nginx

```bash
# Copy Nginx configuration
sudo cp /opt/soilmaster/deployment/production/nginx/soil-master.conf /etc/nginx/sites-available/
sudo ln -sf /etc/nginx/sites-available/soil-master.conf /etc/nginx/sites-enabled/

# Test and reload Nginx
sudo nginx -t
sudo systemctl reload nginx
```

### Phase 4: Process Management (15-30 minutes)

#### 4.1 Configure PM2

```bash
# Copy PM2 ecosystem configuration
cp /opt/soilmaster/deployment/production/pm2/ecosystem.config.js /opt/soilmaster/

# Start applications with PM2
cd /opt/soilmaster
pm2 start ecosystem.config.js --env production

# Save PM2 configuration
pm2 save
```

#### 4.2 Verify Services

```bash
# Check PM2 status
pm2 status

# Check application logs
pm2 logs soil-master-backend --lines 50
pm2 logs soil-master-frontend --lines 50

# Test health endpoints
curl http://localhost:8000/health
curl http://localhost:3000
```

### Phase 5: Monitoring Setup (30-45 minutes)

#### 5.1 Install Monitoring Stack

```bash
# Run monitoring setup script
sudo /opt/soilmaster/monitoring/setup-monitoring.sh
```

#### 5.2 Configure Grafana

```bash
# Access Grafana at http://your-server:3001
# Default credentials: admin/admin (change immediately)

# Import dashboards from /opt/soilmaster/monitoring/grafana/dashboards/
```

#### 5.3 Set Up Alerting

```bash
# Configure Alertmanager
sudo systemctl start alertmanager
sudo systemctl enable alertmanager

# Test alert notifications
curl -X POST http://localhost:9093/api/v1/alerts
```

### Phase 6: Backup Configuration (15-30 minutes)

#### 6.1 Configure Automated Backups

```bash
# Set up backup system
sudo /opt/soilmaster/backup/automated-backup-system.sh config

# Edit backup configuration
sudo nano /opt/soilmaster/backup/backup-config.conf

# Test backup system
sudo /opt/soilmaster/backup/automated-backup-system.sh
```

#### 6.2 Schedule Automated Backups

```bash
# Add to crontab
sudo crontab -e

# Add the following lines:
# Daily backup at 2 AM
0 2 * * * /opt/soilmaster/backup/automated-backup-system.sh

# Weekly full backup on Sunday at 1 AM
0 1 * * 0 /opt/soilmaster/backup/automated-backup-system.sh
```

## Post-Deployment Verification

### 1. Functional Testing

```bash
# Test demo system
curl -f https://yourdomain.com/demo

# Test API endpoints
curl -f https://api.yourdomain.com/api/v1/demo/scenarios
curl -f https://api.yourdomain.com/api/v1/demo/heatmap?scenario_id=scenario-1&parameter=soil_nitrogen

# Test health checks
curl -f https://yourdomain.com/health
curl -f https://api.yourdomain.com/health
```

### 2. Performance Testing

```bash
# Run load tests
cd /opt/soilmaster/tests/demo/performance
k6 run demo_load_test.js

# Run sustained session tests
python /opt/soilmaster/tests/demo/reliability/sustained_session_test.py
```

### 3. Security Validation

```bash
# Run security tests
python /opt/soilmaster/tests/security/security_test_suite.py

# Run compliance validation
python /opt/soilmaster/tests/security/compliance_validator.py
```

### 4. Monitoring Validation

```bash
# Validate monitoring systems
python /opt/soilmaster/monitoring/production-monitoring-validation.py
```

## Troubleshooting

### Common Issues

#### 1. Database Connection Issues

```bash
# Check PostgreSQL status
sudo systemctl status postgresql

# Test database connection
psql -h localhost -U soil_master -d soil_master -c "SELECT version();"

# Check database logs
sudo tail -f /var/log/postgresql/postgresql-15-main.log
```

#### 2. Redis Connection Issues

```bash
# Check Redis status
sudo systemctl status redis-server

# Test Redis connection
redis-cli -a your_redis_password ping

# Check Redis logs
sudo tail -f /var/log/redis/redis-server.log
```

#### 3. Application Startup Issues

```bash
# Check PM2 logs
pm2 logs soil-master-backend
pm2 logs soil-master-frontend

# Restart applications
pm2 restart ecosystem.config.js

# Check system resources
htop
df -h
```

#### 4. SSL Certificate Issues

```bash
# Check certificate status
sudo certbot certificates

# Test SSL configuration
openssl s_client -connect yourdomain.com:443 -servername yourdomain.com

# Renew certificates manually
sudo certbot renew --dry-run
```

#### 5. Nginx Configuration Issues

```bash
# Test Nginx configuration
sudo nginx -t

# Check Nginx logs
sudo tail -f /var/log/nginx/error.log
sudo tail -f /var/log/nginx/soil_master_error.log

# Reload Nginx configuration
sudo systemctl reload nginx
```

## Maintenance Procedures

### Daily Tasks

- Monitor system health via Grafana dashboards
- Check application logs for errors
- Verify backup completion
- Review security alerts

### Weekly Tasks

- Update system packages
- Review performance metrics
- Test disaster recovery procedures
- Update documentation

### Monthly Tasks

- Security audit and penetration testing
- Performance optimization review
- Backup integrity validation
- SSL certificate renewal check

## Performance Optimization

### Database Optimization

```sql
-- Analyze database performance
ANALYZE;

-- Check slow queries
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;

-- Optimize indexes
REINDEX DATABASE soil_master;
```

### Application Optimization

```bash
# Monitor application performance
pm2 monit

# Optimize Node.js memory usage
pm2 restart soil-master-frontend --max-memory-restart 512M

# Enable application caching
redis-cli config set maxmemory 256mb
redis-cli config set maxmemory-policy allkeys-lru
```

### System Optimization

```bash
# Optimize system parameters
echo 'vm.swappiness=10' | sudo tee -a /etc/sysctl.conf
echo 'net.core.somaxconn=65535' | sudo tee -a /etc/sysctl.conf
sudo sysctl -p

# Optimize file descriptors
echo 'soilmaster soft nofile 65535' | sudo tee -a /etc/security/limits.conf
echo 'soilmaster hard nofile 65535' | sudo tee -a /etc/security/limits.conf
```

## Scaling Considerations

### Horizontal Scaling

- Load balancer configuration
- Database read replicas
- Redis clustering
- CDN integration

### Vertical Scaling

- CPU and memory upgrades
- Storage optimization
- Network bandwidth increase

## Support and Maintenance

### Log Locations

- Application logs: `/var/log/soilmaster/`
- Nginx logs: `/var/log/nginx/`
- PostgreSQL logs: `/var/log/postgresql/`
- System logs: `/var/log/syslog`

### Configuration Files

- PM2 configuration: `/opt/soilmaster/ecosystem.config.js`
- Nginx configuration: `/etc/nginx/sites-available/soil-master.conf`
- Database configuration: `/etc/postgresql/15/main/postgresql.conf`
- Redis configuration: `/etc/redis/redis.conf`

### Emergency Contacts

- Technical Support: <EMAIL>
- Security Issues: <EMAIL>
- Infrastructure: <EMAIL>

---

**Document Version**: 1.0.2  
**Last Updated**: $(date)  
**Next Review**: $(date -d "+1 month")  
**Maintained by**: Soil Master DevOps Team
