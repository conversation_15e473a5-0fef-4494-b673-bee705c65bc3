# Soil Master v1.0.2 API Documentation

Comprehensive API documentation for the Soil Master demo platform with enterprise-grade endpoints for stakeholder presentations and investor demonstrations.

## 🎯 Overview

The Soil Master API provides comprehensive endpoints for soil health analysis, demo scenario management, AI predictions, and performance monitoring. Designed for high-stakes presentations with sub-1-second response times and enterprise reliability.

### Base URL

```
Production: https://your-domain.com/api/v1
Development: http://localhost:8000/api/v1
```

### API Version

**Current Version**: v1.0.2  
**Release Date**: December 2024  
**Compatibility**: Backward compatible with v1.0.1

## 🔐 Authentication

### JWT Bearer Token

All API endpoints require authentication using JWT Bearer tokens.

```http
Authorization: Bearer <your-jwt-token>
```

### Obtaining Access Token

```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "your-password"
}
```

**Response:**
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "expires_in": 3600
}
```

## 📊 Demo Scenario Management

### List Demo Scenarios

Get all available demo scenarios for presentations.

```http
GET /api/v1/demo/scenarios
```

**Query Parameters:**
- `scenario_type` (optional): Filter by scenario type
- `is_active` (optional): Filter active scenarios
- `limit` (optional): Number of results (default: 50)
- `offset` (optional): Pagination offset

**Response:**
```json
{
  "scenarios": [
    {
      "id": "uuid",
      "name": "palm_oil_100ha_healthy",
      "title": "Healthy Palm Oil Estate (100ha)",
      "description": "Well-managed estate with optimal conditions",
      "scenario_type": "sample_estate",
      "estate_size_hectares": 100.0,
      "crop_type": "palm_oil",
      "region": "Malaysia",
      "is_active": true,
      "is_default": true,
      "display_order": 1,
      "created_at": "2024-12-01T10:00:00Z",
      "updated_at": "2024-12-01T10:00:00Z"
    }
  ],
  "total": 10,
  "limit": 50,
  "offset": 0
}
```

### Get Demo Scenario

Retrieve detailed information about a specific demo scenario.

```http
GET /api/v1/demo/scenarios/{scenario_id}
```

**Response:**
```json
{
  "id": "uuid",
  "name": "palm_oil_100ha_healthy",
  "title": "Healthy Palm Oil Estate (100ha)",
  "description": "Well-managed 100-hectare palm oil estate",
  "scenario_type": "sample_estate",
  "estate_size_hectares": 100.0,
  "crop_type": "palm_oil",
  "region": "Malaysia",
  "is_active": true,
  "is_default": true,
  "display_order": 1,
  "cache_duration_minutes": 60,
  "preload_data": true,
  "tags": {
    "demo_priority": "high",
    "stakeholder_favorite": true
  },
  "configuration": {
    "climate_zone": "tropical_humid",
    "soil_type": "ultisols"
  },
  "created_at": "2024-12-01T10:00:00Z",
  "updated_at": "2024-12-01T10:00:00Z"
}
```

### Switch Demo Scenario

Switch to a demo scenario with comprehensive preparation.

```http
POST /api/v1/demo/scenarios/{scenario_id}/switch
```

**Query Parameters:**
- `preload_data` (optional): Preload scenario data (default: true)
- `validate_consistency` (optional): Validate data consistency (default: true)

**Response:**
```json
{
  "message": "Demo scenario switched successfully",
  "switch_results": {
    "scenario_id": "uuid",
    "scenario_name": "palm_oil_100ha_healthy",
    "scenario_title": "Healthy Palm Oil Estate (100ha)",
    "switch_successful": true,
    "switch_time_seconds": 2.5,
    "data_preloaded": true,
    "consistency_validated": true,
    "health_status": {
      "overall_health": 95,
      "data_health": 100,
      "cache_health": 90,
      "performance_health": 95
    },
    "demo_ready": true,
    "switched_at": "2024-12-01T10:00:00Z"
  }
}
```

## 🗺️ Heatmap Data

### Get Optimized Heatmap

Retrieve optimized heatmap data for map visualization.

```http
GET /api/v1/demo/heatmap/optimized
```

**Query Parameters:**
- `scenario_id` (optional): Demo scenario ID
- `parameter` (required): Soil parameter (soil_nitrogen, soil_phosphorus, soil_potassium, soil_ph)
- `bounds` (optional): Map bounds as "lat_min,lng_min,lat_max,lng_max"
- `resolution` (optional): Grid resolution (default: 50)
- `color_scheme` (optional): Color scheme (dramatic, professional, high_contrast)

**Response:**
```json
{
  "points": [
    {
      "location": {
        "latitude": 3.1390,
        "longitude": 101.6869
      },
      "predictions": {
        "soil_nitrogen": 25.5
      },
      "visual_enhancements": {
        "color_rgb": [34, 139, 34],
        "color_hex": "#228B22",
        "intensity": 0.8,
        "health_status": "healthy",
        "visual_priority": "low"
      }
    }
  ],
  "color_legend": {
    "parameter": "soil_nitrogen",
    "steps": [
      {
        "value": 0,
        "color_hex": "#8B0000",
        "health_status": "severe",
        "label": "0.0 mg/kg"
      }
    ]
  },
  "problem_areas": {
    "parameter": "soil_nitrogen",
    "problem_areas": [
      {
        "location": {
          "latitude": 3.1395,
          "longitude": 101.6874
        },
        "severity": 0.8,
        "problem_type": "deficiency",
        "visual_marker": {
          "color": "#FF0000",
          "size": "large",
          "animation": "pulse"
        }
      }
    ],
    "summary": {
      "total_problems": 5,
      "critical_areas": 2,
      "warning_areas": 3
    }
  },
  "performance_metrics": {
    "data_points": 2500,
    "generation_time_ms": 450,
    "demo_optimized": true
  }
}
```

## 🤖 AI Predictions

### Generate Demo Predictions

Generate pre-computed AI predictions for demo scenarios.

```http
POST /api/v1/demo/scenarios/{scenario_id}/predictions/generate
```

**Query Parameters:**
- `parameters` (optional): Soil parameters to predict
- `grid_resolution` (optional): Spatial grid resolution (10-200, default: 50)

**Response:**
```json
{
  "message": "Demo predictions generated successfully",
  "scenario_id": "uuid",
  "prediction_summary": {
    "total_points": 2500,
    "parameters": ["soil_nitrogen", "soil_phosphorus", "soil_potassium", "soil_ph"],
    "grid_resolution": 50,
    "average_confidence": 0.87,
    "generation_time": 15.5
  },
  "demo_ready": true
}
```

### Get Demo Predictions

Retrieve pre-computed AI predictions for visualization.

```http
GET /api/v1/demo/scenarios/{scenario_id}/predictions
```

**Query Parameters:**
- `parameter` (optional): Specific soil parameter
- `bounds` (optional): Spatial bounds filter

**Response:**
```json
{
  "scenario_id": "uuid",
  "predictions": [
    {
      "location": {
        "latitude": 3.1390,
        "longitude": 101.6869
      },
      "predictions": {
        "soil_nitrogen": 25.5,
        "soil_phosphorus": 15.2,
        "soil_potassium": 120.8,
        "soil_ph": 6.5
      },
      "confidence_scores": {
        "soil_nitrogen": 0.92,
        "soil_phosphorus": 0.88,
        "soil_potassium": 0.85,
        "soil_ph": 0.90
      },
      "model_version": "xgboost_v1.0.2",
      "prediction_date": "2024-12-01T10:00:00Z"
    }
  ],
  "metadata": {
    "total_points": 2500,
    "average_confidence": 0.87,
    "generated_at": "2024-12-01T10:00:00Z",
    "demo_optimized": true
  },
  "demo_optimized": true
}
```

## 📈 Performance Monitoring

### Get Performance Snapshot

Collect comprehensive performance snapshot for analysis.

```http
GET /api/v1/demo/performance/snapshot
```

**Response:**
```json
{
  "performance_snapshot": {
    "snapshot_timestamp": "2024-12-01T10:00:00Z",
    "collection_time_ms": 125.5,
    "current_metrics": {
      "response_times": {
        "scenario_listing_ms": 85.2,
        "heatmap_generation_ms": 420.1,
        "data_retrieval_ms": 95.8
      },
      "system_metrics": {
        "cpu_usage_percent": 45.2,
        "memory_usage_percent": 62.8,
        "disk_usage_percent": 35.1
      },
      "cache_metrics": {
        "hit_rate_percent": 87.5,
        "memory_usage_mb": 256
      }
    },
    "overall_performance_score": 92,
    "demo_readiness": {
      "is_ready": true,
      "performance_score": 92,
      "readiness_level": "excellent"
    }
  }
}
```

### Execute Load Test

Run comprehensive load test for demo scenarios.

```http
POST /api/v1/demo/performance/load-test
```

**Query Parameters:**
- `test_scenario` (optional): Test scenario type (default: stakeholder_presentation)
- `concurrent_users` (optional): Override concurrent users (1-100)
- `duration_minutes` (optional): Override test duration (5-120)

**Response:**
```json
{
  "message": "Load test completed successfully",
  "test_results": {
    "test_execution": {
      "test_id": "load_test_20241201_100000",
      "test_scenario": "stakeholder_presentation",
      "status": "completed",
      "duration_minutes": 30,
      "analysis": {
        "success_rate_percent": 99.2,
        "error_rate_percent": 0.8,
        "response_time_stats": {
          "average_ms": 285.5,
          "p95_ms": 650.2,
          "p99_ms": 980.1
        },
        "test_passed": true,
        "performance_grade": "A"
      }
    },
    "test_successful": true
  }
}
```

## 📊 Sample Data Generation

### Generate Sample Estate Scenarios

Create curated sample estate datasets for demonstrations.

```http
POST /api/v1/demo/sample-data/generate
```

**Response:**
```json
{
  "message": "Sample estate scenarios generated successfully",
  "scenarios_created": 3,
  "scenarios": [
    {
      "id": "uuid",
      "name": "palm_oil_100ha_healthy",
      "title": "Healthy Palm Oil Estate (100ha)",
      "scenario_type": "sample_estate",
      "estate_size_hectares": 100.0,
      "crop_type": "palm_oil",
      "region": "Malaysia",
      "is_default": true
    }
  ],
  "demo_ready": true
}
```

### Generate Before/After Scenarios

Create compelling before/after transformation datasets.

```http
POST /api/v1/demo/sample-data/before-after
```

**Response:**
```json
{
  "message": "Before/after scenarios generated successfully",
  "scenarios_created": 4,
  "scenario_pairs": 2,
  "scenarios": [
    {
      "id": "uuid",
      "name": "degraded_to_optimal_before",
      "title": "Degraded Estate Transformation - Before Improvement",
      "phase": "before",
      "scenario_pair": "degraded_to_optimal"
    },
    {
      "id": "uuid",
      "name": "degraded_to_optimal_after",
      "title": "Degraded Estate Transformation - After Improvement",
      "phase": "after",
      "scenario_pair": "degraded_to_optimal"
    }
  ],
  "demo_ready": true
}
```

## 🔧 System Health

### Get Demo System Health

Retrieve comprehensive system health status.

```http
GET /api/v1/demo/health
```

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-12-01T10:00:00Z",
  "version": "1.0.2",
  "demo_metrics": {
    "active_scenarios": 5,
    "cached_scenarios": 3,
    "total_data_points": 12500,
    "demo_switches_today": 25
  },
  "system_metrics": {
    "cpu_usage_percent": 45.2,
    "memory_usage_percent": 62.8,
    "disk_usage_percent": 35.1,
    "uptime_hours": 168.5
  },
  "database_health": {
    "status": "healthy",
    "connection_count": 15,
    "query_performance_ms": 45.2
  },
  "cache_health": {
    "status": "healthy",
    "hit_rate_percent": 87.5,
    "memory_usage_mb": 256
  },
  "demo_ready": true
}
```

## 🚨 Error Handling

### Standard Error Response

```json
{
  "detail": "Error description",
  "error_code": "VALIDATION_ERROR",
  "timestamp": "2024-12-01T10:00:00Z",
  "request_id": "uuid"
}
```

### HTTP Status Codes

- **200 OK**: Successful request
- **201 Created**: Resource created successfully
- **400 Bad Request**: Invalid request parameters
- **401 Unauthorized**: Authentication required
- **403 Forbidden**: Insufficient permissions
- **404 Not Found**: Resource not found
- **422 Unprocessable Entity**: Validation error
- **429 Too Many Requests**: Rate limit exceeded
- **500 Internal Server Error**: Server error
- **503 Service Unavailable**: Service temporarily unavailable

## 📊 Rate Limiting

### Limits

- **General API**: 1000 requests per hour per user
- **Demo endpoints**: 500 requests per hour per user
- **Load testing**: 10 requests per hour per user
- **Health checks**: Unlimited

### Headers

```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: **********
```

## 🔍 Filtering and Pagination

### Query Parameters

- `limit`: Number of results (max: 100)
- `offset`: Pagination offset
- `sort`: Sort field
- `order`: Sort order (asc, desc)
- `filter`: JSON filter object

### Example

```http
GET /api/v1/demo/scenarios?limit=20&offset=40&sort=created_at&order=desc
```

## 📝 Changelog

### v1.0.2 (December 2024)

- ✅ Added comprehensive demo scenario management
- ✅ Implemented AI prediction endpoints
- ✅ Added performance monitoring and load testing
- ✅ Enhanced visual impact algorithms
- ✅ Added before/after scenario generation
- ✅ Implemented regional data variations
- ✅ Added reliability and health monitoring

### v1.0.1 (November 2024)

- ✅ Initial API implementation
- ✅ Basic soil data endpoints
- ✅ Authentication system
- ✅ Core heatmap functionality

---

For additional API support, please contact the development team or refer to the interactive API documentation at `/docs` when the server is running.
