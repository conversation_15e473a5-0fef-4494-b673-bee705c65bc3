# Soil Master v1.0.2 Component Design

## Overview

This document details the component-level design for the Soil Master system, focusing on interactive heatmap visualization, responsive layouts, and performance optimization strategies.

## Interactive Heatmap Component Design

### Core Heatmap Component Architecture

```typescript
// HeatmapVisualization.tsx
interface HeatmapVisualizationProps {
  scenarioId: string;
  parameter: SoilParameter;
  visualMode: VisualMode;
  onParameterChange: (parameter: SoilParameter) => void;
  onVisualModeChange: (mode: VisualMode) => void;
}

interface HeatmapState {
  data: HeatmapData | null;
  loading: boolean;
  error: string | null;
  renderingMode: 'canvas' | 'webgl';
  zoomLevel: number;
  center: [number, number];
}

const HeatmapVisualization: React.FC<HeatmapVisualizationProps> = ({
  scenarioId,
  parameter,
  visualMode,
  onParameterChange,
  onVisualModeChange
}) => {
  // Component implementation
};
```

### Heatmap Data Processing Pipeline

```typescript
// services/heatmapService.ts
class HeatmapService {
  private cache = new Map<string, HeatmapData>();
  private interpolator: SpatialInterpolator;
  private colorMapper: ColorMapper;

  async generateHeatmap(
    scenarioId: string,
    parameter: SoilParameter,
    visualMode: VisualMode,
    bounds: GeoBounds
  ): Promise<HeatmapData> {
    const cacheKey = `${scenarioId}-${parameter}-${visualMode}`;
    
    // Check cache first
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)!;
    }

    // Fetch raw soil data
    const rawData = await this.fetchSoilData(scenarioId, parameter, bounds);
    
    // Apply spatial interpolation
    const interpolatedData = await this.interpolator.interpolate(rawData, bounds);
    
    // Apply color mapping
    const colorMappedData = this.colorMapper.mapColors(
      interpolatedData,
      visualMode
    );
    
    // Cache result
    this.cache.set(cacheKey, colorMappedData);
    
    return colorMappedData;
  }

  private async fetchSoilData(
    scenarioId: string,
    parameter: SoilParameter,
    bounds: GeoBounds
  ): Promise<SoilDataPoint[]> {
    const response = await fetch(`/api/v1/soil-data`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ scenarioId, parameter, bounds })
    });
    
    if (!response.ok) {
      throw new Error(`Failed to fetch soil data: ${response.statusText}`);
    }
    
    return response.json();
  }
}
```

### Spatial Interpolation Algorithm

```typescript
// utils/spatialInterpolation.ts
class SpatialInterpolator {
  private method: 'idw' | 'kriging' | 'rbf' = 'idw';
  private power: number = 2;
  private searchRadius: number = 1000; // meters

  async interpolate(
    dataPoints: SoilDataPoint[],
    bounds: GeoBounds,
    resolution: number = 100
  ): Promise<InterpolatedPoint[]> {
    const grid = this.generateGrid(bounds, resolution);
    const interpolatedPoints: InterpolatedPoint[] = [];

    for (const gridPoint of grid) {
      const value = this.interpolatePoint(gridPoint, dataPoints);
      interpolatedPoints.push({
        x: gridPoint.x,
        y: gridPoint.y,
        value,
        confidence: this.calculateConfidence(gridPoint, dataPoints)
      });
    }

    return interpolatedPoints;
  }

  private interpolatePoint(
    point: Point,
    dataPoints: SoilDataPoint[]
  ): number {
    // Inverse Distance Weighting (IDW) implementation
    const nearbyPoints = this.findNearbyPoints(point, dataPoints);
    
    if (nearbyPoints.length === 0) {
      return 0;
    }

    let weightedSum = 0;
    let weightSum = 0;

    for (const dataPoint of nearbyPoints) {
      const distance = this.calculateDistance(point, dataPoint);
      const weight = 1 / Math.pow(distance, this.power);
      
      weightedSum += dataPoint.value * weight;
      weightSum += weight;
    }

    return weightedSum / weightSum;
  }

  private findNearbyPoints(
    point: Point,
    dataPoints: SoilDataPoint[]
  ): SoilDataPoint[] {
    return dataPoints.filter(dataPoint => {
      const distance = this.calculateDistance(point, dataPoint);
      return distance <= this.searchRadius;
    });
  }

  private calculateDistance(point1: Point, point2: Point): number {
    // Haversine formula for geographic distance
    const R = 6371000; // Earth's radius in meters
    const lat1Rad = (point1.y * Math.PI) / 180;
    const lat2Rad = (point2.y * Math.PI) / 180;
    const deltaLatRad = ((point2.y - point1.y) * Math.PI) / 180;
    const deltaLonRad = ((point2.x - point1.x) * Math.PI) / 180;

    const a = Math.sin(deltaLatRad / 2) * Math.sin(deltaLatRad / 2) +
              Math.cos(lat1Rad) * Math.cos(lat2Rad) *
              Math.sin(deltaLonRad / 2) * Math.sin(deltaLonRad / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    
    return R * c;
  }
}
```

### Color Mapping System

```typescript
// utils/colorMapper.ts
class ColorMapper {
  private colorSchemes: Record<VisualMode, ColorScheme> = {
    dramatic: {
      name: 'viridis',
      colors: ['#440154', '#31688e', '#35b779', '#fde725'],
      contrast: 1.2,
      saturation: 1.3
    },
    professional: {
      name: 'blues',
      colors: ['#f7fbff', '#deebf7', '#c6dbef', '#9ecae1', '#6baed6'],
      contrast: 1.0,
      saturation: 1.0
    },
    high_contrast: {
      name: 'binary',
      colors: ['#000000', '#ffffff'],
      contrast: 2.0,
      saturation: 0.8
    }
  };

  mapColors(
    data: InterpolatedPoint[],
    visualMode: VisualMode
  ): HeatmapPoint[] {
    const scheme = this.colorSchemes[visualMode];
    const { min, max } = this.findValueRange(data);
    
    return data.map(point => ({
      x: point.x,
      y: point.y,
      value: point.value,
      color: this.interpolateColor(point.value, min, max, scheme),
      intensity: this.calculateIntensity(point.value, min, max, scheme),
      confidence: point.confidence
    }));
  }

  private interpolateColor(
    value: number,
    min: number,
    max: number,
    scheme: ColorScheme
  ): string {
    const normalized = (value - min) / (max - min);
    const colorIndex = normalized * (scheme.colors.length - 1);
    const lowerIndex = Math.floor(colorIndex);
    const upperIndex = Math.ceil(colorIndex);
    const factor = colorIndex - lowerIndex;

    if (lowerIndex === upperIndex) {
      return scheme.colors[lowerIndex];
    }

    const lowerColor = this.hexToRgb(scheme.colors[lowerIndex]);
    const upperColor = this.hexToRgb(scheme.colors[upperIndex]);

    const interpolatedColor = {
      r: Math.round(lowerColor.r + (upperColor.r - lowerColor.r) * factor),
      g: Math.round(lowerColor.g + (upperColor.g - lowerColor.g) * factor),
      b: Math.round(lowerColor.b + (upperColor.b - lowerColor.b) * factor)
    };

    return this.rgbToHex(interpolatedColor);
  }
}
```

## Responsive Layout Components

### Demo Interface Layout

```typescript
// components/demo/DemoInterface.tsx
const DemoInterface: React.FC = () => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
      if (window.innerWidth < 768) {
        setSidebarCollapsed(true);
      }
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  return (
    <div className="demo-interface">
      <Header />
      <div className="demo-content">
        <Sidebar 
          collapsed={sidebarCollapsed}
          onToggle={() => setSidebarCollapsed(!sidebarCollapsed)}
          mobile={isMobile}
        />
        <MainContent 
          sidebarCollapsed={sidebarCollapsed}
          mobile={isMobile}
        />
      </div>
      <PerformanceMonitor />
    </div>
  );
};
```

### Responsive Grid System

```css
/* styles/grid.css */
.demo-grid {
  display: grid;
  grid-template-areas: 
    "header header header"
    "sidebar main performance"
    "footer footer footer";
  grid-template-columns: 300px 1fr 250px;
  grid-template-rows: auto 1fr auto;
  min-height: 100vh;
  gap: 1rem;
}

/* Tablet layout */
@media (max-width: 1024px) {
  .demo-grid {
    grid-template-areas: 
      "header header"
      "main performance"
      "sidebar sidebar"
      "footer footer";
    grid-template-columns: 1fr 250px;
  }
}

/* Mobile layout */
@media (max-width: 768px) {
  .demo-grid {
    grid-template-areas: 
      "header"
      "main"
      "sidebar"
      "performance"
      "footer";
    grid-template-columns: 1fr;
  }
}

/* Component-specific responsive styles */
.heatmap-container {
  position: relative;
  width: 100%;
  height: 60vh;
  min-height: 400px;
}

@media (max-width: 768px) {
  .heatmap-container {
    height: 50vh;
    min-height: 300px;
  }
}

.controls-panel {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1rem;
}

@media (max-width: 768px) {
  .controls-panel {
    flex-direction: row;
    overflow-x: auto;
    padding: 0.5rem;
  }
}
```

### Adaptive Component Behavior

```typescript
// hooks/useResponsive.ts
export const useResponsive = () => {
  const [breakpoint, setBreakpoint] = useState<Breakpoint>('desktop');
  const [dimensions, setDimensions] = useState({
    width: window.innerWidth,
    height: window.innerHeight
  });

  useEffect(() => {
    const updateBreakpoint = () => {
      const width = window.innerWidth;
      setDimensions({ width, height: window.innerHeight });

      if (width < 768) {
        setBreakpoint('mobile');
      } else if (width < 1024) {
        setBreakpoint('tablet');
      } else if (width < 1440) {
        setBreakpoint('desktop');
      } else {
        setBreakpoint('large');
      }
    };

    updateBreakpoint();
    window.addEventListener('resize', updateBreakpoint);
    return () => window.removeEventListener('resize', updateBreakpoint);
  }, []);

  return {
    breakpoint,
    dimensions,
    isMobile: breakpoint === 'mobile',
    isTablet: breakpoint === 'tablet',
    isDesktop: breakpoint === 'desktop' || breakpoint === 'large',
    isLarge: breakpoint === 'large'
  };
};

// components/demo/AdaptiveHeatmap.tsx
const AdaptiveHeatmap: React.FC<HeatmapProps> = (props) => {
  const { breakpoint, dimensions } = useResponsive();
  
  const adaptiveProps = useMemo(() => {
    switch (breakpoint) {
      case 'mobile':
        return {
          ...props,
          pointDensity: 'low',
          renderingMode: 'canvas',
          showLegend: false,
          controls: 'overlay'
        };
      case 'tablet':
        return {
          ...props,
          pointDensity: 'medium',
          renderingMode: 'canvas',
          showLegend: true,
          controls: 'sidebar'
        };
      default:
        return {
          ...props,
          pointDensity: 'high',
          renderingMode: 'webgl',
          showLegend: true,
          controls: 'sidebar'
        };
    }
  }, [breakpoint, props]);

  return <HeatmapVisualization {...adaptiveProps} />;
};
```

## Performance Optimization Strategies

### Rendering Optimization

```typescript
// utils/renderingOptimizer.ts
class RenderingOptimizer {
  private canvas: HTMLCanvasElement;
  private context: CanvasRenderingContext2D;
  private webglContext: WebGLRenderingContext | null = null;
  private pointBuffer: Float32Array;
  private colorBuffer: Uint8Array;

  constructor(canvas: HTMLCanvasElement) {
    this.canvas = canvas;
    this.context = canvas.getContext('2d')!;
    
    // Try to get WebGL context for GPU acceleration
    try {
      this.webglContext = canvas.getContext('webgl');
    } catch (e) {
      console.warn('WebGL not available, falling back to Canvas 2D');
    }
  }

  renderHeatmap(
    points: HeatmapPoint[],
    viewport: Viewport,
    options: RenderOptions
  ): void {
    // Choose rendering method based on point count and device capabilities
    if (this.webglContext && points.length > 1000) {
      this.renderWithWebGL(points, viewport, options);
    } else {
      this.renderWithCanvas2D(points, viewport, options);
    }
  }

  private renderWithWebGL(
    points: HeatmapPoint[],
    viewport: Viewport,
    options: RenderOptions
  ): void {
    // WebGL shader-based rendering for large datasets
    const gl = this.webglContext!;
    
    // Prepare vertex data
    this.prepareVertexData(points);
    
    // Use shaders for efficient GPU rendering
    const program = this.createShaderProgram(gl);
    gl.useProgram(program);
    
    // Bind vertex buffers
    this.bindVertexBuffers(gl, program);
    
    // Render points
    gl.drawArrays(gl.POINTS, 0, points.length);
  }

  private renderWithCanvas2D(
    points: HeatmapPoint[],
    viewport: Viewport,
    options: RenderOptions
  ): void {
    // Canvas 2D rendering with optimizations
    const ctx = this.context;
    
    // Clear canvas
    ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
    
    // Apply level-of-detail optimization
    const visiblePoints = this.cullInvisiblePoints(points, viewport);
    const lodPoints = this.applyLevelOfDetail(visiblePoints, viewport.zoom);
    
    // Batch render points
    this.batchRenderPoints(ctx, lodPoints, options);
  }

  private applyLevelOfDetail(
    points: HeatmapPoint[],
    zoomLevel: number
  ): HeatmapPoint[] {
    // Reduce point density at lower zoom levels
    const densityFactor = Math.max(0.1, Math.min(1.0, zoomLevel / 10));
    const targetCount = Math.floor(points.length * densityFactor);
    
    if (points.length <= targetCount) {
      return points;
    }
    
    // Use spatial sampling to maintain visual quality
    return this.spatialSample(points, targetCount);
  }

  private spatialSample(
    points: HeatmapPoint[],
    targetCount: number
  ): HeatmapPoint[] {
    // Implement Poisson disk sampling for even distribution
    const sampledPoints: HeatmapPoint[] = [];
    const minDistance = this.calculateMinDistance(points, targetCount);
    
    for (const point of points) {
      if (this.isValidSamplePoint(point, sampledPoints, minDistance)) {
        sampledPoints.push(point);
        if (sampledPoints.length >= targetCount) {
          break;
        }
      }
    }
    
    return sampledPoints;
  }
}
```

### Caching Strategy

```typescript
// services/cacheService.ts
class CacheService {
  private memoryCache = new Map<string, CacheEntry>();
  private indexedDBCache: IDBDatabase | null = null;
  private maxMemoryCacheSize = 50; // MB
  private currentMemoryUsage = 0;

  async initialize(): Promise<void> {
    // Initialize IndexedDB for persistent caching
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('SoilMasterCache', 1);
      
      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        this.indexedDBCache = request.result;
        resolve();
      };
      
      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        if (!db.objectStoreNames.contains('heatmaps')) {
          db.createObjectStore('heatmaps', { keyPath: 'key' });
        }
      };
    });
  }

  async get<T>(key: string): Promise<T | null> {
    // Check memory cache first
    const memoryEntry = this.memoryCache.get(key);
    if (memoryEntry && !this.isExpired(memoryEntry)) {
      return memoryEntry.data as T;
    }

    // Check IndexedDB cache
    if (this.indexedDBCache) {
      const persistentEntry = await this.getFromIndexedDB(key);
      if (persistentEntry && !this.isExpired(persistentEntry)) {
        // Promote to memory cache
        this.setInMemoryCache(key, persistentEntry.data, persistentEntry.ttl);
        return persistentEntry.data as T;
      }
    }

    return null;
  }

  async set<T>(
    key: string,
    data: T,
    ttl: number = 3600000 // 1 hour default
  ): Promise<void> {
    const entry: CacheEntry = {
      key,
      data,
      timestamp: Date.now(),
      ttl,
      size: this.estimateSize(data)
    };

    // Store in memory cache
    this.setInMemoryCache(key, data, ttl);

    // Store in IndexedDB for persistence
    if (this.indexedDBCache) {
      await this.setInIndexedDB(entry);
    }
  }

  private setInMemoryCache<T>(key: string, data: T, ttl: number): void {
    const entry: CacheEntry = {
      key,
      data,
      timestamp: Date.now(),
      ttl,
      size: this.estimateSize(data)
    };

    // Check if we need to evict entries
    while (this.currentMemoryUsage + entry.size > this.maxMemoryCacheSize * 1024 * 1024) {
      this.evictLeastRecentlyUsed();
    }

    this.memoryCache.set(key, entry);
    this.currentMemoryUsage += entry.size;
  }

  private evictLeastRecentlyUsed(): void {
    let oldestKey = '';
    let oldestTimestamp = Date.now();

    for (const [key, entry] of this.memoryCache.entries()) {
      if (entry.timestamp < oldestTimestamp) {
        oldestTimestamp = entry.timestamp;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      const entry = this.memoryCache.get(oldestKey)!;
      this.memoryCache.delete(oldestKey);
      this.currentMemoryUsage -= entry.size;
    }
  }
}
```

### Performance Monitoring

```typescript
// utils/performanceMonitor.ts
class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private observers: PerformanceObserver[] = [];

  startMonitoring(): void {
    // Monitor rendering performance
    this.observeRenderingMetrics();
    
    // Monitor API performance
    this.observeNetworkMetrics();
    
    // Monitor memory usage
    this.observeMemoryMetrics();
    
    // Monitor user interactions
    this.observeInteractionMetrics();
  }

  private observeRenderingMetrics(): void {
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.name.includes('heatmap-render')) {
          this.recordMetric({
            name: 'heatmap_render_time',
            value: entry.duration,
            timestamp: Date.now(),
            tags: { type: 'rendering' }
          });
        }
      }
    });

    observer.observe({ entryTypes: ['measure'] });
    this.observers.push(observer);
  }

  measureHeatmapRender<T>(fn: () => T): T {
    const startMark = `heatmap-render-start-${Date.now()}`;
    const endMark = `heatmap-render-end-${Date.now()}`;
    const measureName = `heatmap-render-${Date.now()}`;

    performance.mark(startMark);
    const result = fn();
    performance.mark(endMark);
    performance.measure(measureName, startMark, endMark);

    return result;
  }

  async measureAsyncOperation<T>(
    name: string,
    operation: () => Promise<T>
  ): Promise<T> {
    const startTime = performance.now();
    
    try {
      const result = await operation();
      const duration = performance.now() - startTime;
      
      this.recordMetric({
        name,
        value: duration,
        timestamp: Date.now(),
        tags: { status: 'success' }
      });
      
      return result;
    } catch (error) {
      const duration = performance.now() - startTime;
      
      this.recordMetric({
        name,
        value: duration,
        timestamp: Date.now(),
        tags: { status: 'error' }
      });
      
      throw error;
    }
  }

  getMetrics(): PerformanceMetric[] {
    return [...this.metrics];
  }

  getAverageMetric(name: string, timeWindow: number = 60000): number {
    const cutoff = Date.now() - timeWindow;
    const relevantMetrics = this.metrics.filter(
      m => m.name === name && m.timestamp > cutoff
    );
    
    if (relevantMetrics.length === 0) {
      return 0;
    }
    
    const sum = relevantMetrics.reduce((acc, m) => acc + m.value, 0);
    return sum / relevantMetrics.length;
  }
}
```

## Implementation Timeline

### Week 1-2: Foundation Components
- [x] Basic heatmap visualization component
- [x] Responsive layout framework
- [x] Performance monitoring setup
- [x] Caching service implementation

### Week 3-4: Advanced Features
- [x] Spatial interpolation algorithms
- [x] Color mapping system
- [x] WebGL rendering optimization
- [x] Adaptive component behavior

### Week 5-6: Performance Optimization
- [x] Level-of-detail rendering
- [x] Memory management optimization
- [x] Network request optimization
- [x] Cache strategy refinement

### Week 7-8: Testing and Validation
- [x] Performance benchmarking
- [x] Cross-device testing
- [x] Accessibility validation
- [x] User experience testing

---

**Document Version**: 1.0.2  
**Last Updated**: $(date)  
**Component Review**: Monthly  
**Maintained by**: Soil Master Frontend Team
