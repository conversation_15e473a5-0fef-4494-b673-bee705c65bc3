# Soil Master v1.0.2 System Architecture

Enterprise-grade system architecture documentation for the Soil Master demo platform, designed for high-stakes presentations with sub-1-second performance and 99.9% reliability.

## 🏗️ Architecture Overview

### System Design Principles

- **Performance First**: Sub-1-second response times for all demo operations
- **Enterprise Reliability**: 99.9% uptime with comprehensive monitoring
- **Scalability**: Support for 50+ concurrent demo users
- **Security**: Enterprise-grade security with encryption and access controls
- **Maintainability**: Modular design with clear separation of concerns

### High-Level Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend      │    │   Soil-AI       │
│   (React/Vue)   │◄──►│   (FastAPI)     │◄──►│   (XGBoost)     │
│                 │    │                 │    │                 │
│ • Demo UI       │    │ • REST API      │    │ • ML Models     │
│ • Interactive   │    │ • Demo Logic    │    │ • Predictions   │
│   Maps          │    │ • Data Mgmt     │    │ • GPU/CPU       │
│ • Visualizations│    │ • Caching       │    │   Fallback      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         │              │   Data Layer    │              │
         │              │                 │              │
         │              │ • PostgreSQL    │              │
         │              │ • PostGIS       │              │
         │              │ • Redis Cache   │              │
         │              │ • File Storage  │              │
         │              └─────────────────┘              │
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │ Infrastructure  │
                    │                 │
                    │ • Nginx         │
                    │ • PM2           │
                    │ • Ubuntu 24.04  │
                    │ • Monitoring    │
                    └─────────────────┘
```

## 🎯 Component Architecture

### Frontend Layer

#### Technology Stack
- **Framework**: React 18+ with TypeScript
- **State Management**: Redux Toolkit
- **Mapping**: Leaflet with custom plugins
- **UI Components**: Material-UI or Ant Design
- **Build Tool**: Vite for fast development
- **Deployment**: Static files served by Nginx

#### Key Components

**Demo Dashboard**
```typescript
interface DemoState {
  currentScenario: DemoScenario | null;
  scenarios: DemoScenario[];
  heatmapData: HeatmapData | null;
  predictions: PredictionData[];
  performance: PerformanceMetrics;
  loading: boolean;
  error: string | null;
}
```

**Interactive Map**
- **Heatmap Layers**: Dynamic soil parameter visualization
- **Problem Area Markers**: Highlighted critical zones
- **Smooth Animations**: Professional transitions
- **Performance Optimization**: Canvas rendering for large datasets

**Visual Impact System**
- **Color Schemes**: Dramatic, Professional, High-Contrast
- **Dynamic Legends**: Parameter-specific color coding
- **Problem Highlighting**: Attention-grabbing markers
- **Smooth Transitions**: Seamless scenario switching

### Backend Layer

#### Technology Stack
- **Framework**: FastAPI with Python 3.12
- **Database ORM**: SQLAlchemy 2.0 with async support
- **Caching**: Redis with async client
- **Task Queue**: Celery for background processing
- **API Documentation**: OpenAPI/Swagger
- **Process Management**: PM2 for production

#### Core Services

**Demo Service**
```python
class DemoService:
    def __init__(self, cache_service: CacheService):
        self.cache_service = cache_service
        self.monitoring_service = MonitoringService()
        self.visual_impact_service = VisualImpactService()
    
    async def get_optimized_heatmap(
        self, 
        scenario_id: UUID, 
        parameter: str,
        **kwargs
    ) -> Dict[str, Any]:
        # Optimized heatmap generation with caching
        pass
```

**AI Demo Service**
```python
class AIDemoService:
    def __init__(self, cache_service: CacheService):
        self.cache_service = cache_service
        self.model_manager = ModelManager()
    
    async def generate_demo_predictions(
        self,
        session: AsyncSession,
        scenario_id: UUID,
        parameters: List[str],
        grid_resolution: int
    ) -> Dict[str, Any]:
        # Pre-computed predictions for instant demo response
        pass
```

**Performance Monitoring**
```python
class DemoPerformanceMonitor:
    def __init__(self):
        self.performance_thresholds = {
            "map_loading_ms": 1000,
            "heatmap_generation_ms": 2000,
            "api_response_ms": 500
        }
    
    async def collect_performance_snapshot(
        self, 
        session: AsyncSession
    ) -> Dict[str, Any]:
        # Real-time performance monitoring
        pass
```

### Soil-AI Layer

#### Technology Stack
- **ML Framework**: XGBoost with GPU support
- **Compute Strategy**: GPU-first with CPU fallback
- **Model Management**: Versioned model storage
- **Prediction Pipeline**: Async batch processing
- **Performance Optimization**: Model caching and pre-computation

#### Model Architecture

**XGBoost Configuration**
```python
class SoilHealthPredictor:
    def __init__(self):
        self.model_config = {
            'tree_method': 'hist',
            'device': 'cuda',  # GPU-first
            'n_estimators': 100,
            'max_depth': 6,
            'learning_rate': 0.1,
            'subsample': 0.8,
            'colsample_bytree': 0.8
        }
    
    def predict_with_fallback(self, features: np.ndarray) -> np.ndarray:
        try:
            # GPU prediction
            return self.gpu_model.predict(features)
        except Exception:
            # CPU fallback
            return self.cpu_model.predict(features)
```

**Prediction Pipeline**
- **Feature Engineering**: Automated feature extraction
- **Model Ensemble**: Multiple models for robustness
- **Confidence Scoring**: Prediction uncertainty quantification
- **Spatial Interpolation**: Grid-based prediction mapping

### Data Layer

#### Database Design

**PostgreSQL with PostGIS**
```sql
-- Demo scenarios table
CREATE TABLE demo_scenarios (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL UNIQUE,
    title VARCHAR(500) NOT NULL,
    description TEXT,
    scenario_type VARCHAR(100) NOT NULL,
    estate_size_hectares DECIMAL(10,2) NOT NULL,
    crop_type VARCHAR(100) NOT NULL,
    region VARCHAR(100) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    is_default BOOLEAN DEFAULT false,
    display_order INTEGER DEFAULT 0,
    cache_duration_minutes INTEGER DEFAULT 60,
    preload_data BOOLEAN DEFAULT false,
    tags JSONB DEFAULT '{}',
    configuration JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Demo data table with spatial indexing
CREATE TABLE demo_data (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    scenario_id UUID NOT NULL REFERENCES demo_scenarios(id) ON DELETE CASCADE,
    data_type VARCHAR(100) NOT NULL,
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    data_payload JSONB NOT NULL,
    display_name VARCHAR(255),
    display_order INTEGER DEFAULT 0,
    is_highlighted BOOLEAN DEFAULT false,
    is_cached BOOLEAN DEFAULT false,
    cache_key VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Spatial index for fast map queries
CREATE INDEX idx_demo_data_spatial 
ON demo_data USING GIST (ST_Point(longitude, latitude))
WHERE latitude IS NOT NULL AND longitude IS NOT NULL;

-- Composite index for scenario queries
CREATE INDEX idx_demo_data_scenario_spatial 
ON demo_data (scenario_id, latitude, longitude)
WHERE latitude IS NOT NULL AND longitude IS NOT NULL;
```

**Redis Caching Strategy**
```python
class CacheService:
    def __init__(self):
        self.redis = aioredis.from_url("redis://localhost:6379")
        self.default_ttl = 3600  # 1 hour
    
    async def cache_heatmap_data(
        self, 
        scenario_id: UUID, 
        parameter: str, 
        data: Dict[str, Any]
    ) -> None:
        cache_key = f"heatmap:{scenario_id}:{parameter}"
        await self.redis.setex(
            cache_key, 
            self.default_ttl, 
            json.dumps(data)
        )
```

## 🚀 Performance Architecture

### Response Time Optimization

#### Target Performance Metrics
- **Map Loading**: <1 second
- **Heatmap Generation**: <2 seconds
- **API Response**: <500ms
- **Scenario Switching**: <3 seconds
- **Prediction Generation**: <5 seconds

#### Optimization Strategies

**Database Optimization**
- **Spatial Indexing**: PostGIS GIST indexes for fast spatial queries
- **Materialized Views**: Pre-computed aggregations
- **Connection Pooling**: 20 connections with overflow
- **Query Optimization**: Optimized SQL with proper indexing

**Caching Strategy**
- **Redis Cache**: 80%+ hit rate for demo data
- **Application Cache**: In-memory caching for frequently accessed data
- **CDN**: Static asset caching with Nginx
- **Browser Cache**: Optimized cache headers

**Frontend Optimization**
- **Code Splitting**: Lazy loading of components
- **Bundle Optimization**: Tree shaking and minification
- **Image Optimization**: WebP format with fallbacks
- **Virtual Scrolling**: Efficient large dataset rendering

### Scalability Architecture

#### Horizontal Scaling
- **Load Balancing**: Nginx upstream configuration
- **Database Replication**: Read replicas for scaling
- **Cache Clustering**: Redis cluster for high availability
- **CDN Integration**: Global content distribution

#### Vertical Scaling
- **Resource Optimization**: CPU and memory tuning
- **Database Tuning**: PostgreSQL configuration optimization
- **Process Management**: PM2 cluster mode
- **System Optimization**: Ubuntu kernel tuning

## 🔒 Security Architecture

### Authentication & Authorization

**JWT Token System**
```python
class AuthService:
    def __init__(self):
        self.secret_key = os.getenv("JWT_SECRET_KEY")
        self.algorithm = "HS256"
        self.access_token_expire_minutes = 60
    
    def create_access_token(self, data: dict) -> str:
        to_encode = data.copy()
        expire = datetime.utcnow() + timedelta(
            minutes=self.access_token_expire_minutes
        )
        to_encode.update({"exp": expire})
        return jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
```

**Role-Based Access Control**
- **Admin**: Full system access
- **Demo User**: Demo scenarios and presentations
- **Viewer**: Read-only access
- **API User**: Programmatic access

### Data Security

**Encryption**
- **Data at Rest**: PostgreSQL encryption
- **Data in Transit**: TLS 1.3 encryption
- **API Security**: HTTPS only with HSTS
- **Session Security**: Secure cookie configuration

**Network Security**
- **Firewall**: UFW with restrictive rules
- **Intrusion Detection**: Fail2ban for brute force protection
- **SSL/TLS**: Let's Encrypt certificates with auto-renewal
- **Security Headers**: OWASP recommended headers

## 📊 Monitoring Architecture

### Application Monitoring

**Performance Metrics**
```python
class MonitoringService:
    def __init__(self):
        self.metrics = {
            "response_times": [],
            "error_rates": [],
            "cache_hit_rates": [],
            "concurrent_users": 0
        }
    
    async def track_api_performance(
        self, 
        endpoint: str, 
        response_time: float, 
        status_code: int
    ) -> None:
        # Track API performance metrics
        pass
```

**Health Checks**
- **Application Health**: API endpoint monitoring
- **Database Health**: Connection and query performance
- **Cache Health**: Redis connectivity and performance
- **System Health**: CPU, memory, disk usage

### Alerting System

**Alert Channels**
- **Email**: Critical system alerts
- **Slack**: Team notifications
- **SMS**: Emergency alerts
- **Dashboard**: Real-time status display

**Alert Thresholds**
- **Response Time**: >2 seconds
- **Error Rate**: >1%
- **CPU Usage**: >80%
- **Memory Usage**: >85%
- **Disk Usage**: >90%

## 🔄 Deployment Architecture

### Production Environment

**Ubuntu Server 24.04 LTS**
```bash
# System architecture
/opt/soilmaster/
├── backend/           # FastAPI application
├── frontend/          # React build files
├── soil-ai/          # ML models and pipeline
├── backups/          # Automated backups
└── logs/             # Application logs

# Service management
systemctl status postgresql  # Database
systemctl status redis       # Cache
systemctl status nginx       # Web server
pm2 status                   # Application processes
```

**Process Management**
- **PM2**: Node.js process manager for backend
- **Systemd**: System service management
- **Nginx**: Reverse proxy and static file serving
- **Supervisor**: Process monitoring and restart

### CI/CD Pipeline

**Deployment Stages**
1. **Code Validation**: Linting, testing, security scanning
2. **Build Process**: Application compilation and optimization
3. **Staging Deployment**: Pre-production testing
4. **Production Deployment**: Zero-downtime deployment
5. **Health Verification**: Post-deployment validation
6. **Rollback Capability**: Automatic rollback on failure

## 📈 Capacity Planning

### Resource Requirements

**Minimum Production Setup**
- **CPU**: 4 cores (2.4GHz+)
- **RAM**: 8GB
- **Storage**: 100GB SSD
- **Network**: 1Gbps connection

**Recommended Production Setup**
- **CPU**: 8 cores (3.0GHz+)
- **RAM**: 16GB
- **Storage**: 500GB NVMe SSD
- **Network**: 10Gbps connection

### Scaling Thresholds

**Scale Up Triggers**
- **CPU Usage**: >70% sustained
- **Memory Usage**: >80% sustained
- **Response Time**: >1.5 seconds average
- **Concurrent Users**: >40 simultaneous

**Scale Down Triggers**
- **CPU Usage**: <30% sustained
- **Memory Usage**: <50% sustained
- **Response Time**: <500ms average
- **Concurrent Users**: <10 simultaneous

---

**🏗️ Architecture Principles:**

1. **Performance First**: Every component optimized for speed
2. **Reliability**: Comprehensive monitoring and failover
3. **Scalability**: Horizontal and vertical scaling capabilities
4. **Security**: Defense in depth with multiple layers
5. **Maintainability**: Clear separation of concerns and documentation

For detailed implementation guides, refer to the deployment documentation and API specifications.
