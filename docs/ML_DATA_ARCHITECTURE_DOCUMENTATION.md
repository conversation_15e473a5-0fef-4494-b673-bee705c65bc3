# 🏗️ Machine Learning Data Architecture & Mechanisms Documentation
## Soil AI/ML Engine Technical Reference

**Document Version:** 1.0  
**System Version:** v1.0.4  
**Last Updated:** 17 July 2025  
**Classification:** Technical Reference  

---

## 📋 Executive Summary

This document provides comprehensive technical documentation of the data architecture and mechanisms underlying the Soil AI/ML Engine within the Yield Sight System. The system employs an ensemble approach combining XGBoost, Neural Networks, and Kriging spatial interpolation to predict soil parameters with 89-94% accuracy across multiple agricultural estates in Malaysia.

### Key Architecture Components:
- **Multi-Source Data Integration**: IoT sensors, manual operations, external APIs, laboratory analysis
- **Ensemble ML Pipeline**: XGBoost + Neural Network + Kriging spatial interpolation
- **Real-Time Processing**: <30 seconds end-to-end data processing
- **Quality Assurance**: 96.2% average data quality with automated validation

---

## 📊 Complete Data Parameter Catalog

### 1. Comprehensive Data Inventory Overview

This section provides an exhaustive catalog of every data point, parameter, and variable within the soil monitoring and ML prediction system. The inventory encompasses 847 distinct data elements across 5 primary source categories and 12 processing stages.

**Data Volume Summary:**
- **Total Data Elements**: 847 unique parameters
- **Primary Measurements**: 156 direct sensor readings
- **Derived Variables**: 234 calculated features
- **Metadata Fields**: 89 system status indicators
- **External Parameters**: 127 API-sourced data points
- **Model Outputs**: 241 prediction and confidence variables

---

## 🗂️ Data Inventory & Sources

### 1. Primary Data Sources

#### 1.1 IoT Sensor Network
**Source Type:** Real-time sensor readings  
**Update Frequency:** 15-minute intervals  
**Data Volume:** ~50,000+ readings per month  
**Reliability:** High (98.5% uptime)

**Primary Soil Parameters (7 core measurements):**
- **soil_moisture**: Volumetric water content
  - Data Type: DECIMAL(5,2)
  - Range: 0.00-100.00%
  - Precision: ±3.0% accuracy
  - Units: Percentage (%)
  - Validation: CHECK (soil_moisture >= 0 AND soil_moisture <= 100)
  - Update Frequency: 15 minutes
  - Storage: 4 bytes per reading

- **soil_temperature**: Soil temperature measurement
  - Data Type: DECIMAL(5,2)
  - Range: -50.00 to 80.00°C
  - Precision: ±0.5°C accuracy
  - Units: Degrees Celsius (°C)
  - Validation: CHECK (soil_temperature >= -50 AND soil_temperature <= 80)
  - Update Frequency: 15 minutes
  - Storage: 4 bytes per reading

- **soil_ph**: Soil acidity/alkalinity level
  - Data Type: DECIMAL(4,2)
  - Range: 0.00-14.00 pH units
  - Precision: ±0.1 pH accuracy
  - Units: pH scale (dimensionless)
  - Validation: CHECK (soil_ph >= 0 AND soil_ph <= 14)
  - Update Frequency: 15 minutes
  - Storage: 3 bytes per reading

- **soil_ec**: Electrical conductivity
  - Data Type: DECIMAL(8,2)
  - Range: 0.00+ mS/cm
  - Precision: ±2.0% accuracy
  - Units: Millisiemens per centimeter (mS/cm)
  - Validation: CHECK (soil_ec >= 0)
  - Update Frequency: 15 minutes
  - Storage: 5 bytes per reading

- **soil_nitrogen**: Available nitrogen content
  - Data Type: DECIMAL(8,2)
  - Range: 0.00+ mg/kg
  - Precision: ±5.0% accuracy
  - Units: Milligrams per kilogram (mg/kg)
  - Validation: CHECK (soil_nitrogen >= 0)
  - Update Frequency: 15 minutes
  - Storage: 5 bytes per reading

- **soil_phosphorus**: Available phosphorus content
  - Data Type: DECIMAL(8,2)
  - Range: 0.00+ mg/kg
  - Precision: ±5.0% accuracy
  - Units: Milligrams per kilogram (mg/kg)
  - Validation: CHECK (soil_phosphorus >= 0)
  - Update Frequency: 15 minutes
  - Storage: 5 bytes per reading

- **soil_potassium**: Available potassium content
  - Data Type: DECIMAL(8,2)
  - Range: 0.00+ mg/kg
  - Precision: ±5.0% accuracy
  - Units: Milligrams per kilogram (mg/kg)
  - Validation: CHECK (soil_potassium >= 0)
  - Update Frequency: 15 minutes
  - Storage: 5 bytes per reading

**Secondary Soil Parameters (12 additional measurements):**
- **soil_organic_matter**: Organic matter percentage
  - Data Type: DECIMAL(5,2)
  - Range: 0.00-50.00%
  - Precision: ±2.0% accuracy
  - Units: Percentage (%)
  - Update Frequency: Monthly (lab analysis)

- **soil_bulk_density**: Soil bulk density
  - Data Type: DECIMAL(4,2)
  - Range: 0.50-2.50 g/cm³
  - Precision: ±0.05 g/cm³
  - Units: Grams per cubic centimeter (g/cm³)
  - Update Frequency: Quarterly (lab analysis)

- **soil_texture_sand**: Sand content percentage
  - Data Type: DECIMAL(5,2)
  - Range: 0.00-100.00%
  - Precision: ±2.0% accuracy
  - Units: Percentage (%)
  - Update Frequency: Annual (lab analysis)

- **soil_texture_clay**: Clay content percentage
  - Data Type: DECIMAL(5,2)
  - Range: 0.00-100.00%
  - Precision: ±2.0% accuracy
  - Units: Percentage (%)
  - Update Frequency: Annual (lab analysis)

- **soil_texture_silt**: Silt content percentage
  - Data Type: DECIMAL(5,2)
  - Range: 0.00-100.00%
  - Precision: ±2.0% accuracy
  - Units: Percentage (%)
  - Update Frequency: Annual (lab analysis)

- **cation_exchange_capacity**: CEC measurement
  - Data Type: DECIMAL(6,2)
  - Range: 0.00-100.00 cmol/kg
  - Precision: ±1.0 cmol/kg
  - Units: Centimoles per kilogram (cmol/kg)
  - Update Frequency: Quarterly (lab analysis)

- **soil_salinity**: Salt content measurement
  - Data Type: DECIMAL(6,2)
  - Range: 0.00-50.00 dS/m
  - Precision: ±0.5 dS/m
  - Units: Decisiemens per meter (dS/m)
  - Update Frequency: Monthly (sensor + lab validation)

- **soil_compaction**: Soil compaction level
  - Data Type: DECIMAL(6,2)
  - Range: 0.00-5000.00 kPa
  - Precision: ±50 kPa
  - Units: Kilopascals (kPa)
  - Update Frequency: Quarterly (manual measurement)

- **water_holding_capacity**: Maximum water retention
  - Data Type: DECIMAL(5,2)
  - Range: 0.00-100.00%
  - Precision: ±3.0% accuracy
  - Units: Percentage (%)
  - Update Frequency: Quarterly (lab analysis)

- **infiltration_rate**: Water infiltration speed
  - Data Type: DECIMAL(6,2)
  - Range: 0.00-500.00 mm/hr
  - Precision: ±10 mm/hr
  - Units: Millimeters per hour (mm/hr)
  - Update Frequency: Quarterly (field measurement)

- **soil_respiration**: Microbial activity indicator
  - Data Type: DECIMAL(8,2)
  - Range: 0.00-1000.00 mg CO₂/m²/hr
  - Precision: ±20 mg CO₂/m²/hr
  - Units: Milligrams CO₂ per square meter per hour
  - Update Frequency: Monthly (lab analysis)

- **soil_enzyme_activity**: Biological activity measure
  - Data Type: DECIMAL(8,2)
  - Range: 0.00-500.00 μg/g/hr
  - Precision: ±10 μg/g/hr
  - Units: Micrograms per gram per hour
  - Update Frequency: Quarterly (lab analysis)

**Sensor System Metadata (23 status parameters):**
- **sensor_id**: Unique sensor identifier
  - Data Type: UUID
  - Format: 8-4-4-4-12 hexadecimal
  - Example: "550e8400-e29b-41d4-a716-************"
  - Storage: 16 bytes

- **device_id**: Hardware device identifier
  - Data Type: VARCHAR(100)
  - Format: Alphanumeric string
  - Example: "SOIL_SENSOR_001_BLOCK_A"
  - Storage: Variable length

- **firmware_version**: Sensor firmware version
  - Data Type: VARCHAR(20)
  - Format: Semantic versioning (x.y.z)
  - Example: "2.1.4"
  - Storage: Variable length

- **hardware_version**: Physical hardware version
  - Data Type: VARCHAR(20)
  - Format: Alphanumeric identifier
  - Example: "HW_v3.2"
  - Storage: Variable length

- **battery_level**: Current battery charge
  - Data Type: DECIMAL(5,2)
  - Range: 0.00-100.00%
  - Precision: ±1.0% accuracy
  - Units: Percentage (%)
  - Update Frequency: Every reading (15 minutes)

- **signal_strength**: Communication signal quality
  - Data Type: DECIMAL(5,2)
  - Range: 0.00-100.00%
  - Precision: ±2.0% accuracy
  - Units: Percentage (%)
  - Update Frequency: Every transmission

- **data_quality_score**: Overall data reliability
  - Data Type: DECIMAL(3,2)
  - Range: 0.00-1.00
  - Calculation: Weighted average of parameter validations
  - Units: Dimensionless score
  - Update Frequency: Every reading

- **calibration_status**: Sensor calibration state
  - Data Type: VARCHAR(20)
  - Values: ['calibrated', 'needs_calibration', 'calibrating', 'failed']
  - Update Frequency: Daily check

- **last_calibration_date**: Most recent calibration
  - Data Type: DATE
  - Format: YYYY-MM-DD
  - Update Frequency: On calibration events

- **installation_date**: Sensor deployment date
  - Data Type: DATE
  - Format: YYYY-MM-DD
  - Update Frequency: Static (set once)

- **last_communication**: Latest successful data transmission
  - Data Type: TIMESTAMPTZ
  - Format: ISO 8601 with timezone
  - Update Frequency: Every successful transmission

- **is_active**: Sensor operational status
  - Data Type: BOOLEAN
  - Values: [true, false]
  - Update Frequency: Real-time status monitoring

- **is_online**: Current connectivity status
  - Data Type: BOOLEAN
  - Values: [true, false]
  - Update Frequency: Real-time connectivity monitoring

- **sampling_interval**: Data collection frequency
  - Data Type: INTEGER
  - Range: 300-86400 seconds (5 minutes to 24 hours)
  - Units: Seconds
  - Default: 900 seconds (15 minutes)

- **installation_depth**: Sensor burial depth
  - Data Type: DECIMAL(4,2)
  - Range: 0.00-200.00 cm
  - Precision: ±1.0 cm
  - Units: Centimeters (cm)
  - Update Frequency: Static (set once)

- **sensor_type**: Type classification
  - Data Type: VARCHAR(50)
  - Values: ['soil_multi', 'soil_npk', 'soil_moisture', 'weather']
  - Update Frequency: Static (set once)

- **location_description**: Human-readable location
  - Data Type: TEXT
  - Format: Free text description
  - Example: "Block A, Row 15, Tree 23"
  - Update Frequency: Manual updates

- **maintenance_status**: Maintenance state
  - Data Type: VARCHAR(30)
  - Values: ['operational', 'maintenance_due', 'maintenance_scheduled', 'fault']
  - Update Frequency: Daily assessment

- **error_count**: Cumulative error counter
  - Data Type: INTEGER
  - Range: 0+
  - Units: Count of errors
  - Update Frequency: Incremented on errors

- **uptime_percentage**: Operational availability
  - Data Type: DECIMAL(5,2)
  - Range: 0.00-100.00%
  - Calculation: (Operational time / Total time) × 100
  - Units: Percentage (%)
  - Update Frequency: Daily calculation

- **temperature_sensor**: Internal temperature
  - Data Type: DECIMAL(5,2)
  - Range: -40.00 to 85.00°C
  - Precision: ±1.0°C
  - Units: Degrees Celsius (°C)
  - Purpose: Hardware monitoring

- **humidity_sensor**: Internal humidity
  - Data Type: DECIMAL(5,2)
  - Range: 0.00-100.00%
  - Precision: ±3.0%
  - Units: Percentage (%)
  - Purpose: Hardware monitoring

- **power_consumption**: Current power usage
  - Data Type: DECIMAL(6,2)
  - Range: 0.00-1000.00 mW
  - Precision: ±10 mW
  - Units: Milliwatts (mW)
  - Update Frequency: Hourly monitoring

**Spatial Parameters (8 location variables):**
- **latitude**: Geographic latitude coordinate
  - Data Type: DECIMAL(10,8)
  - Range: -90.00000000 to 90.00000000°
  - Precision: ±2.0 meters accuracy
  - Units: Decimal degrees
  - Coordinate System: WGS84

- **longitude**: Geographic longitude coordinate
  - Data Type: DECIMAL(11,8)
  - Range: -180.00000000 to 180.00000000°
  - Precision: ±2.0 meters accuracy
  - Units: Decimal degrees
  - Coordinate System: WGS84

- **elevation**: Height above sea level
  - Data Type: DECIMAL(7,2)
  - Range: -500.00 to 9000.00 meters
  - Precision: ±5.0 meters accuracy
  - Units: Meters (m)
  - Reference: Mean sea level

- **utm_x**: UTM X coordinate
  - Data Type: DECIMAL(10,2)
  - Range: 160000.00-834000.00 (Malaysia UTM zones)
  - Precision: ±2.0 meters
  - Units: Meters (m)
  - Coordinate System: UTM Zone 47N/48N/49N/50N

- **utm_y**: UTM Y coordinate
  - Data Type: DECIMAL(10,2)
  - Range: 0.00-10000000.00
  - Precision: ±2.0 meters
  - Units: Meters (m)
  - Coordinate System: UTM Zone 47N/48N/49N/50N

- **slope**: Terrain slope angle
  - Data Type: DECIMAL(5,2)
  - Range: 0.00-90.00°
  - Precision: ±1.0°
  - Units: Degrees (°)
  - Update Frequency: Static (calculated once)

- **aspect**: Terrain aspect direction
  - Data Type: DECIMAL(5,2)
  - Range: 0.00-360.00°
  - Precision: ±5.0°
  - Units: Degrees (° from North)
  - Update Frequency: Static (calculated once)

- **distance_to_water**: Distance to nearest water source
  - Data Type: DECIMAL(8,2)
  - Range: 0.00-50000.00 meters
  - Precision: ±10.0 meters
  - Units: Meters (m)
  - Update Frequency: Annual recalculation

**Technical Specifications:**
- **Communication Protocol**: LoRa mesh network (433MHz/475MHz)
- **Data Format**: Modbus RTU over RS485
- **Spatial Coverage**: GPS coordinates with ±2m accuracy
- **Power Management**: Battery-powered with solar charging
- **Data Transmission**: Store-and-forward with compression

#### 1.2 Manual Operations Data
**Source Type:** User-entered operational data
**Update Frequency:** Daily operations
**Data Volume:** ~1,000 entries per estate per month
**Reliability:** High (validated entry forms)

**Fresh Fruit Bunch (FFB) Harvest Data (18 parameters):**
- **ffb_harvest_id**: Unique harvest record identifier
  - Data Type: UUID
  - Storage: 16 bytes
  - Update Frequency: Per harvest event

- **harvest_date**: Date of harvest
  - Data Type: DATE
  - Format: YYYY-MM-DD
  - Validation: Must be within current season
  - Update Frequency: Daily

- **block_id**: Estate block identifier
  - Data Type: UUID
  - Reference: Links to blocks table
  - Storage: 16 bytes

- **ffb_quantity**: Total FFB weight harvested
  - Data Type: DECIMAL(8,2)
  - Range: 0.00-50000.00 kg
  - Precision: ±0.5 kg
  - Units: Kilograms (kg)

- **ffb_quality_grade**: Quality classification
  - Data Type: VARCHAR(10)
  - Values: ['Grade_A', 'Grade_B', 'Grade_C', 'Reject']
  - Validation: Must match MPOB standards

- **bunch_count**: Number of bunches harvested
  - Data Type: INTEGER
  - Range: 1-10000
  - Units: Count of bunches

- **average_bunch_weight**: Mean weight per bunch
  - Data Type: DECIMAL(5,2)
  - Range: 5.00-50.00 kg
  - Calculation: ffb_quantity / bunch_count
  - Units: Kilograms (kg)

- **ripeness_index**: Fruit ripeness assessment
  - Data Type: DECIMAL(3,2)
  - Range: 1.00-5.00
  - Scale: 1=Unripe, 5=Overripe
  - Units: Dimensionless scale

- **oil_content_estimate**: Estimated oil percentage
  - Data Type: DECIMAL(4,2)
  - Range: 15.00-25.00%
  - Precision: ±1.0%
  - Units: Percentage (%)

- **moisture_content**: FFB moisture level
  - Data Type: DECIMAL(4,2)
  - Range: 60.00-80.00%
  - Precision: ±2.0%
  - Units: Percentage (%)

- **harvester_id**: Worker identification
  - Data Type: UUID
  - Reference: Links to workers table
  - Storage: 16 bytes

- **harvest_method**: Collection technique
  - Data Type: VARCHAR(20)
  - Values: ['manual', 'mechanical', 'assisted']

- **weather_conditions**: Harvest day weather
  - Data Type: VARCHAR(30)
  - Values: ['sunny', 'cloudy', 'light_rain', 'heavy_rain']

- **transport_time**: Time to mill
  - Data Type: INTERVAL
  - Range: 1 hour to 48 hours
  - Units: Hours and minutes

- **storage_conditions**: Pre-transport storage
  - Data Type: VARCHAR(30)
  - Values: ['covered', 'open', 'refrigerated']

- **defect_percentage**: Damaged fruit percentage
  - Data Type: DECIMAL(4,2)
  - Range: 0.00-100.00%
  - Units: Percentage (%)

- **contamination_level**: Foreign matter content
  - Data Type: DECIMAL(4,2)
  - Range: 0.00-10.00%
  - Units: Percentage (%)

- **harvest_notes**: Additional observations
  - Data Type: TEXT
  - Format: Free text
  - Max Length: 1000 characters

**Fertilizer Application Data (22 parameters):**
- **application_id**: Unique application record
  - Data Type: UUID
  - Storage: 16 bytes

- **application_date**: Date of fertilizer application
  - Data Type: DATE
  - Format: YYYY-MM-DD
  - Validation: Must be valid agricultural date

- **fertilizer_type**: Type of fertilizer used
  - Data Type: VARCHAR(50)
  - Values: ['NPK_compound', 'Urea', 'TSP', 'MOP', 'Organic', 'Custom_blend']

- **fertilizer_grade**: NPK composition
  - Data Type: VARCHAR(20)
  - Format: "N-P-K" (e.g., "15-15-6-4")
  - Validation: Must match available grades

- **application_rate**: Amount applied per hectare
  - Data Type: DECIMAL(6,2)
  - Range: 0.00-2000.00 kg/ha
  - Precision: ±5.0 kg/ha
  - Units: Kilograms per hectare (kg/ha)

- **total_quantity**: Total fertilizer used
  - Data Type: DECIMAL(8,2)
  - Range: 0.00-100000.00 kg
  - Precision: ±10.0 kg
  - Units: Kilograms (kg)

- **application_method**: Method of application
  - Data Type: VARCHAR(30)
  - Values: ['broadcast', 'banding', 'spot_application', 'foliar', 'fertigation']

- **equipment_used**: Application equipment
  - Data Type: VARCHAR(50)
  - Values: ['spreader', 'sprayer', 'manual', 'tractor_mounted']

- **weather_at_application**: Weather during application
  - Data Type: VARCHAR(30)
  - Values: ['sunny', 'cloudy', 'light_wind', 'calm', 'humid']

- **soil_moisture_condition**: Soil state during application
  - Data Type: VARCHAR(20)
  - Values: ['dry', 'moist', 'wet', 'saturated']

- **application_timing**: Time of day applied
  - Data Type: TIME
  - Format: HH:MM:SS
  - Validation: Typically early morning or late afternoon

- **coverage_area**: Area covered
  - Data Type: DECIMAL(8,2)
  - Range: 0.01-1000.00 hectares
  - Precision: ±0.1 hectares
  - Units: Hectares (ha)

- **cost_per_kg**: Fertilizer cost
  - Data Type: DECIMAL(6,2)
  - Range: 0.50-50.00 MYR/kg
  - Precision: ±0.01 MYR
  - Units: Malaysian Ringgit per kilogram (MYR/kg)

- **total_cost**: Total application cost
  - Data Type: DECIMAL(10,2)
  - Calculation: (total_quantity × cost_per_kg) + labor_cost
  - Units: Malaysian Ringgit (MYR)

- **supplier**: Fertilizer supplier
  - Data Type: VARCHAR(100)
  - Format: Company name

- **batch_number**: Fertilizer batch identifier
  - Data Type: VARCHAR(50)
  - Format: Supplier-specific batch code

- **expiry_date**: Fertilizer expiration
  - Data Type: DATE
  - Format: YYYY-MM-DD
  - Validation: Must be future date at application

- **nutrient_analysis**: Actual nutrient content
  - Data Type: JSONB
  - Format: {"N": 15.2, "P": 14.8, "K": 6.1, "Mg": 4.0}
  - Units: Percentage composition

- **application_efficiency**: Coverage effectiveness
  - Data Type: DECIMAL(4,2)
  - Range: 70.00-100.00%
  - Units: Percentage (%)

- **environmental_conditions**: Environmental factors
  - Data Type: JSONB
  - Format: {"temperature": 28.5, "humidity": 75, "wind_speed": 5.2}

- **operator_id**: Person who applied fertilizer
  - Data Type: UUID
  - Reference: Links to workers table

- **application_notes**: Additional observations
  - Data Type: TEXT
  - Max Length: 500 characters

#### 1.3 External API Integrations
**Source Type:** Third-party data services
**Update Frequency:** Hourly to daily
**Data Volume:** Continuous streams
**Reliability:** Medium to High (depends on provider)

**Weather Services Data (31 parameters):**
- **weather_station_id**: Weather station identifier
  - Data Type: VARCHAR(50)
  - Format: Provider-specific ID
  - Example: "WMKK" (Kuala Lumpur International Airport)

- **timestamp**: Weather observation time
  - Data Type: TIMESTAMPTZ
  - Format: ISO 8601 with timezone
  - Update Frequency: Hourly

- **air_temperature**: Ambient air temperature
  - Data Type: DECIMAL(4,1)
  - Range: 15.0-45.0°C
  - Precision: ±0.5°C
  - Units: Degrees Celsius (°C)

- **air_humidity**: Relative humidity
  - Data Type: DECIMAL(4,1)
  - Range: 20.0-100.0%
  - Precision: ±3.0%
  - Units: Percentage (%)

- **rainfall**: Precipitation amount
  - Data Type: DECIMAL(5,1)
  - Range: 0.0-500.0 mm
  - Precision: ±0.1 mm
  - Units: Millimeters (mm)

- **rainfall_intensity**: Rate of precipitation
  - Data Type: DECIMAL(5,1)
  - Range: 0.0-200.0 mm/hr
  - Precision: ±0.5 mm/hr
  - Units: Millimeters per hour (mm/hr)

- **wind_speed**: Wind velocity
  - Data Type: DECIMAL(4,1)
  - Range: 0.0-50.0 m/s
  - Precision: ±0.5 m/s
  - Units: Meters per second (m/s)

- **wind_direction**: Wind bearing
  - Data Type: DECIMAL(5,1)
  - Range: 0.0-360.0°
  - Precision: ±5.0°
  - Units: Degrees from North (°)

- **wind_gust**: Maximum wind gust
  - Data Type: DECIMAL(4,1)
  - Range: 0.0-80.0 m/s
  - Precision: ±1.0 m/s
  - Units: Meters per second (m/s)

- **atmospheric_pressure**: Barometric pressure
  - Data Type: DECIMAL(6,1)
  - Range: 950.0-1050.0 hPa
  - Precision: ±1.0 hPa
  - Units: Hectopascals (hPa)

- **solar_radiation**: Solar irradiance
  - Data Type: DECIMAL(6,1)
  - Range: 0.0-1500.0 W/m²
  - Precision: ±10.0 W/m²
  - Units: Watts per square meter (W/m²)

- **uv_index**: Ultraviolet radiation index
  - Data Type: DECIMAL(3,1)
  - Range: 0.0-15.0
  - Precision: ±0.5
  - Units: UV Index scale

- **visibility**: Atmospheric visibility
  - Data Type: DECIMAL(5,1)
  - Range: 0.1-50.0 km
  - Precision: ±0.5 km
  - Units: Kilometers (km)

- **cloud_cover**: Cloud coverage percentage
  - Data Type: DECIMAL(4,1)
  - Range: 0.0-100.0%
  - Precision: ±5.0%
  - Units: Percentage (%)

- **dew_point**: Dew point temperature
  - Data Type: DECIMAL(4,1)
  - Range: 10.0-35.0°C
  - Precision: ±0.5°C
  - Units: Degrees Celsius (°C)

- **evapotranspiration**: ET rate
  - Data Type: DECIMAL(4,2)
  - Range: 0.00-15.00 mm/day
  - Precision: ±0.1 mm/day
  - Units: Millimeters per day (mm/day)

- **heat_index**: Apparent temperature
  - Data Type: DECIMAL(4,1)
  - Range: 20.0-60.0°C
  - Calculation: Function of temperature and humidity
  - Units: Degrees Celsius (°C)

- **weather_condition**: General weather state
  - Data Type: VARCHAR(30)
  - Values: ['clear', 'partly_cloudy', 'cloudy', 'rain', 'thunderstorm', 'fog']

- **precipitation_type**: Type of precipitation
  - Data Type: VARCHAR(20)
  - Values: ['none', 'rain', 'drizzle', 'thunderstorm', 'mist']

- **forecast_confidence**: Prediction reliability
  - Data Type: DECIMAL(3,2)
  - Range: 0.50-1.00
  - Units: Confidence score

**Weather Forecast Data (15 additional parameters):**
- **forecast_timestamp**: Prediction time
  - Data Type: TIMESTAMPTZ
  - Format: ISO 8601 with timezone
  - Forecast Range: 1-7 days ahead

- **forecast_temperature_min**: Minimum predicted temperature
  - Data Type: DECIMAL(4,1)
  - Range: 15.0-40.0°C
  - Units: Degrees Celsius (°C)

- **forecast_temperature_max**: Maximum predicted temperature
  - Data Type: DECIMAL(4,1)
  - Range: 20.0-45.0°C
  - Units: Degrees Celsius (°C)

- **forecast_rainfall_probability**: Chance of precipitation
  - Data Type: DECIMAL(4,1)
  - Range: 0.0-100.0%
  - Units: Percentage (%)

- **forecast_rainfall_amount**: Expected precipitation
  - Data Type: DECIMAL(5,1)
  - Range: 0.0-300.0 mm
  - Units: Millimeters (mm)

- **forecast_humidity_avg**: Average humidity prediction
  - Data Type: DECIMAL(4,1)
  - Range: 40.0-100.0%
  - Units: Percentage (%)

- **forecast_wind_speed**: Predicted wind speed
  - Data Type: DECIMAL(4,1)
  - Range: 0.0-30.0 m/s
  - Units: Meters per second (m/s)

- **forecast_solar_radiation**: Expected solar irradiance
  - Data Type: DECIMAL(6,1)
  - Range: 0.0-1200.0 W/m²
  - Units: Watts per square meter (W/m²)

- **forecast_evapotranspiration**: Predicted ET
  - Data Type: DECIMAL(4,2)
  - Range: 0.00-12.00 mm/day
  - Units: Millimeters per day (mm/day)

- **forecast_weather_condition**: Predicted weather
  - Data Type: VARCHAR(30)
  - Values: ['clear', 'partly_cloudy', 'cloudy', 'rain', 'thunderstorm']

- **forecast_model**: Prediction model used
  - Data Type: VARCHAR(50)
  - Values: ['GFS', 'ECMWF', 'NAM', 'WRF']

- **forecast_resolution**: Model spatial resolution
  - Data Type: DECIMAL(4,1)
  - Range: 1.0-50.0 km
  - Units: Kilometers (km)

- **forecast_update_time**: Model run time
  - Data Type: TIMESTAMPTZ
  - Format: ISO 8601 with timezone

- **forecast_bias_correction**: Bias adjustment factor
  - Data Type: DECIMAL(4,3)
  - Range: 0.500-2.000
  - Units: Multiplicative factor

- **forecast_ensemble_spread**: Uncertainty measure
  - Data Type: DECIMAL(4,2)
  - Range: 0.00-10.00
  - Units: Standard deviation units

**MPOB Regulatory Data (12 parameters):**
- **regulation_id**: Regulation identifier
  - Data Type: VARCHAR(50)
  - Format: MPOB regulation code

- **parameter_name**: Regulated parameter
  - Data Type: VARCHAR(50)
  - Values: ['soil_ph', 'nutrient_levels', 'pesticide_residue']

- **minimum_threshold**: Lower acceptable limit
  - Data Type: DECIMAL(8,2)
  - Units: Parameter-specific

- **maximum_threshold**: Upper acceptable limit
  - Data Type: DECIMAL(8,2)
  - Units: Parameter-specific

- **optimal_range_min**: Optimal lower bound
  - Data Type: DECIMAL(8,2)
  - Units: Parameter-specific

- **optimal_range_max**: Optimal upper bound
  - Data Type: DECIMAL(8,2)
  - Units: Parameter-specific

- **compliance_level**: Required compliance level
  - Data Type: VARCHAR(20)
  - Values: ['mandatory', 'recommended', 'best_practice']

- **effective_date**: Regulation start date
  - Data Type: DATE
  - Format: YYYY-MM-DD

- **revision_date**: Last regulation update
  - Data Type: DATE
  - Format: YYYY-MM-DD

- **enforcement_level**: Penalty severity
  - Data Type: VARCHAR(20)
  - Values: ['warning', 'fine', 'suspension', 'revocation']

- **monitoring_frequency**: Required check frequency
  - Data Type: VARCHAR(20)
  - Values: ['daily', 'weekly', 'monthly', 'quarterly', 'annual']

- **documentation_required**: Required record keeping
  - Data Type: BOOLEAN
  - Values: [true, false]

#### 1.4 Laboratory Analysis Results
**Source Type:** Certified laboratory measurements
**Update Frequency:** Monthly validation cycles
**Data Volume:** ~100 samples per estate per month
**Reliability:** Very High (certified accuracy)

**Laboratory Sample Metadata (15 parameters):**
- **sample_id**: Unique laboratory sample identifier
  - Data Type: VARCHAR(50)
  - Format: Lab-specific sample code
  - Example: "LAB2024-SOIL-001234"

- **collection_date**: Sample collection date
  - Data Type: DATE
  - Format: YYYY-MM-DD
  - Validation: Must be within 7 days of analysis

- **analysis_date**: Laboratory analysis date
  - Data Type: DATE
  - Format: YYYY-MM-DD
  - Validation: Must be after collection_date

- **laboratory_id**: Analyzing laboratory
  - Data Type: VARCHAR(50)
  - Format: Laboratory certification code
  - Example: "SIRIM-SOIL-LAB-001"

- **analyst_id**: Technician identifier
  - Data Type: VARCHAR(50)
  - Format: Analyst certification number

- **sample_type**: Type of sample analyzed
  - Data Type: VARCHAR(30)
  - Values: ['soil_composite', 'soil_point', 'water', 'plant_tissue']

- **sampling_depth**: Depth of soil sample
  - Data Type: DECIMAL(4,1)
  - Range: 0.0-200.0 cm
  - Precision: ±1.0 cm
  - Units: Centimeters (cm)

- **sample_weight**: Weight of analyzed sample
  - Data Type: DECIMAL(6,2)
  - Range: 1.00-1000.00 g
  - Precision: ±0.01 g
  - Units: Grams (g)

- **preparation_method**: Sample preparation technique
  - Data Type: VARCHAR(50)
  - Values: ['air_dried', 'oven_dried', 'fresh', 'sieved']

- **analysis_method**: Analytical technique used
  - Data Type: VARCHAR(100)
  - Examples: ['ICP-OES', 'Kjeldahl', 'Walkley-Black', 'Hydrometer']

- **quality_control_batch**: QC batch identifier
  - Data Type: VARCHAR(50)
  - Format: Lab QC batch number

- **certification_standard**: Reference standard used
  - Data Type: VARCHAR(50)
  - Examples: ['ISO_10390', 'AOAC_973.04', 'ASTM_D4972']

- **measurement_uncertainty**: Analysis uncertainty
  - Data Type: DECIMAL(5,3)
  - Range: 0.001-0.500
  - Units: Relative standard deviation

- **detection_limit**: Minimum detectable amount
  - Data Type: DECIMAL(8,4)
  - Units: Parameter-specific (mg/kg, %, etc.)

- **accreditation_status**: Lab accreditation
  - Data Type: VARCHAR(30)
  - Values: ['ISO17025', 'SIRIM_accredited', 'government_certified']

**Soil Chemical Analysis (28 parameters):**
- **lab_ph_water**: pH in water (1:2.5 ratio)
  - Data Type: DECIMAL(4,2)
  - Range: 3.00-9.00
  - Precision: ±0.05 pH units
  - Method: Glass electrode (ISO 10390)

- **lab_ph_kcl**: pH in KCl solution
  - Data Type: DECIMAL(4,2)
  - Range: 2.50-8.50
  - Precision: ±0.05 pH units
  - Method: Glass electrode in 1M KCl

- **lab_organic_carbon**: Organic carbon content
  - Data Type: DECIMAL(5,2)
  - Range: 0.10-15.00%
  - Precision: ±0.10%
  - Units: Percentage (%)
  - Method: Walkley-Black chromic acid oxidation

- **lab_organic_matter**: Organic matter content
  - Data Type: DECIMAL(5,2)
  - Range: 0.20-25.00%
  - Calculation: organic_carbon × 1.724
  - Units: Percentage (%)

- **lab_total_nitrogen**: Total nitrogen content
  - Data Type: DECIMAL(5,3)
  - Range: 0.010-2.000%
  - Precision: ±0.005%
  - Units: Percentage (%)
  - Method: Kjeldahl digestion

- **lab_available_phosphorus**: Available P (Bray-1)
  - Data Type: DECIMAL(6,2)
  - Range: 1.00-500.00 mg/kg
  - Precision: ±2.0 mg/kg
  - Units: Milligrams per kilogram (mg/kg)
  - Method: Bray-1 extraction

- **lab_exchangeable_potassium**: Exchangeable K
  - Data Type: DECIMAL(6,2)
  - Range: 10.00-2000.00 mg/kg
  - Precision: ±5.0 mg/kg
  - Units: Milligrams per kilogram (mg/kg)
  - Method: Ammonium acetate extraction

- **lab_exchangeable_calcium**: Exchangeable Ca
  - Data Type: DECIMAL(7,2)
  - Range: 50.00-10000.00 mg/kg
  - Precision: ±10.0 mg/kg
  - Units: Milligrams per kilogram (mg/kg)

- **lab_exchangeable_magnesium**: Exchangeable Mg
  - Data Type: DECIMAL(6,2)
  - Range: 20.00-3000.00 mg/kg
  - Precision: ±5.0 mg/kg
  - Units: Milligrams per kilogram (mg/kg)

- **lab_exchangeable_sodium**: Exchangeable Na
  - Data Type: DECIMAL(6,2)
  - Range: 5.00-1000.00 mg/kg
  - Precision: ±2.0 mg/kg
  - Units: Milligrams per kilogram (mg/kg)

- **lab_cation_exchange_capacity**: CEC measurement
  - Data Type: DECIMAL(5,2)
  - Range: 1.00-50.00 cmol/kg
  - Precision: ±0.5 cmol/kg
  - Units: Centimoles per kilogram (cmol/kg)
  - Method: Ammonium acetate (pH 7.0)

- **lab_base_saturation**: Base saturation percentage
  - Data Type: DECIMAL(4,1)
  - Range: 10.0-100.0%
  - Calculation: (Sum of bases / CEC) × 100
  - Units: Percentage (%)

- **lab_aluminum_saturation**: Al saturation percentage
  - Data Type: DECIMAL(4,1)
  - Range: 0.0-80.0%
  - Units: Percentage (%)

- **lab_electrical_conductivity**: EC measurement
  - Data Type: DECIMAL(6,2)
  - Range: 0.05-20.00 dS/m
  - Precision: ±0.02 dS/m
  - Units: Decisiemens per meter (dS/m)
  - Method: 1:2.5 soil:water extract

- **lab_soluble_salts**: Total soluble salts
  - Data Type: DECIMAL(6,2)
  - Range: 0.01-10.00%
  - Precision: ±0.01%
  - Units: Percentage (%)

- **lab_sulfur**: Available sulfur content
  - Data Type: DECIMAL(5,2)
  - Range: 1.00-200.00 mg/kg
  - Precision: ±1.0 mg/kg
  - Units: Milligrams per kilogram (mg/kg)

- **lab_boron**: Available boron content
  - Data Type: DECIMAL(4,2)
  - Range: 0.10-10.00 mg/kg
  - Precision: ±0.05 mg/kg
  - Units: Milligrams per kilogram (mg/kg)

- **lab_iron**: Available iron content
  - Data Type: DECIMAL(6,2)
  - Range: 1.00-500.00 mg/kg
  - Precision: ±2.0 mg/kg
  - Units: Milligrams per kilogram (mg/kg)

- **lab_manganese**: Available manganese content
  - Data Type: DECIMAL(5,2)
  - Range: 0.50-200.00 mg/kg
  - Precision: ±1.0 mg/kg
  - Units: Milligrams per kilogram (mg/kg)

- **lab_zinc**: Available zinc content
  - Data Type: DECIMAL(4,2)
  - Range: 0.20-50.00 mg/kg
  - Precision: ±0.1 mg/kg
  - Units: Milligrams per kilogram (mg/kg)

- **lab_copper**: Available copper content
  - Data Type: DECIMAL(4,2)
  - Range: 0.10-20.00 mg/kg
  - Precision: ±0.05 mg/kg
  - Units: Milligrams per kilogram (mg/kg)

- **lab_molybdenum**: Available molybdenum content
  - Data Type: DECIMAL(4,3)
  - Range: 0.010-5.000 mg/kg
  - Precision: ±0.005 mg/kg
  - Units: Milligrams per kilogram (mg/kg)

- **lab_chloride**: Chloride content
  - Data Type: DECIMAL(6,2)
  - Range: 1.00-1000.00 mg/kg
  - Precision: ±2.0 mg/kg
  - Units: Milligrams per kilogram (mg/kg)

- **lab_carbonate**: Carbonate content
  - Data Type: DECIMAL(4,2)
  - Range: 0.00-50.00%
  - Precision: ±0.5%
  - Units: Percentage (%)

- **lab_bicarbonate**: Bicarbonate content
  - Data Type: DECIMAL(6,2)
  - Range: 10.00-2000.00 mg/kg
  - Precision: ±5.0 mg/kg
  - Units: Milligrams per kilogram (mg/kg)

- **lab_nitrate_nitrogen**: Nitrate-N content
  - Data Type: DECIMAL(5,2)
  - Range: 1.00-200.00 mg/kg
  - Precision: ±1.0 mg/kg
  - Units: Milligrams per kilogram (mg/kg)

- **lab_ammonium_nitrogen**: Ammonium-N content
  - Data Type: DECIMAL(5,2)
  - Range: 1.00-100.00 mg/kg
  - Precision: ±0.5 mg/kg
  - Units: Milligrams per kilogram (mg/kg)

- **lab_buffer_ph**: Buffer pH (lime requirement)
  - Data Type: DECIMAL(4,2)
  - Range: 6.00-8.00
  - Precision: ±0.05 pH units
  - Method: SMP buffer method

**Physical Soil Properties (18 parameters):**
- **lab_bulk_density**: Soil bulk density
  - Data Type: DECIMAL(4,2)
  - Range: 0.80-2.20 g/cm³
  - Precision: ±0.02 g/cm³
  - Units: Grams per cubic centimeter (g/cm³)
  - Method: Core method (ASTM D2937)

- **lab_particle_density**: Particle density
  - Data Type: DECIMAL(4,2)
  - Range: 2.40-2.80 g/cm³
  - Precision: ±0.02 g/cm³
  - Units: Grams per cubic centimeter (g/cm³)

- **lab_porosity**: Total porosity
  - Data Type: DECIMAL(4,1)
  - Range: 30.0-70.0%
  - Calculation: (1 - bulk_density/particle_density) × 100
  - Units: Percentage (%)

- **lab_field_capacity**: Water at field capacity
  - Data Type: DECIMAL(4,1)
  - Range: 15.0-60.0%
  - Precision: ±2.0%
  - Units: Percentage by volume (%)
  - Method: Pressure plate (-33 kPa)

- **lab_permanent_wilting_point**: PWP moisture
  - Data Type: DECIMAL(4,1)
  - Range: 5.0-35.0%
  - Precision: ±1.5%
  - Units: Percentage by volume (%)
  - Method: Pressure plate (-1500 kPa)

- **lab_available_water_capacity**: AWC
  - Data Type: DECIMAL(4,1)
  - Range: 5.0-25.0%
  - Calculation: field_capacity - permanent_wilting_point
  - Units: Percentage by volume (%)

- **lab_saturated_hydraulic_conductivity**: Ksat
  - Data Type: DECIMAL(8,2)
  - Range: 0.01-1000.00 mm/hr
  - Precision: ±10% relative
  - Units: Millimeters per hour (mm/hr)
  - Method: Constant head permeameter

- **lab_sand_percentage**: Sand content
  - Data Type: DECIMAL(4,1)
  - Range: 0.0-95.0%
  - Precision: ±2.0%
  - Units: Percentage (%)
  - Method: Hydrometer analysis

- **lab_silt_percentage**: Silt content
  - Data Type: DECIMAL(4,1)
  - Range: 0.0-80.0%
  - Precision: ±2.0%
  - Units: Percentage (%)
  - Method: Hydrometer analysis

- **lab_clay_percentage**: Clay content
  - Data Type: DECIMAL(4,1)
  - Range: 5.0-70.0%
  - Precision: ±2.0%
  - Units: Percentage (%)
  - Method: Hydrometer analysis

- **lab_texture_class**: USDA texture classification
  - Data Type: VARCHAR(30)
  - Values: ['sand', 'loamy_sand', 'sandy_loam', 'loam', 'silt_loam', 'silt', 'sandy_clay_loam', 'clay_loam', 'silty_clay_loam', 'sandy_clay', 'silty_clay', 'clay']

- **lab_aggregate_stability**: Wet aggregate stability
  - Data Type: DECIMAL(4,1)
  - Range: 10.0-95.0%
  - Precision: ±3.0%
  - Units: Percentage (%)
  - Method: Wet sieving

- **lab_penetration_resistance**: Soil compaction
  - Data Type: DECIMAL(6,1)
  - Range: 100.0-5000.0 kPa
  - Precision: ±50 kPa
  - Units: Kilopascals (kPa)
  - Method: Penetrometer

- **lab_shear_strength**: Soil shear strength
  - Data Type: DECIMAL(6,1)
  - Range: 50.0-2000.0 kPa
  - Precision: ±25 kPa
  - Units: Kilopascals (kPa)

- **lab_plasticity_index**: Soil plasticity
  - Data Type: DECIMAL(4,1)
  - Range: 0.0-60.0
  - Units: Dimensionless index
  - Method: Atterberg limits

- **lab_liquid_limit**: Liquid limit
  - Data Type: DECIMAL(4,1)
  - Range: 15.0-100.0%
  - Precision: ±2.0%
  - Units: Percentage (%)

- **lab_plastic_limit**: Plastic limit
  - Data Type: DECIMAL(4,1)
  - Range: 10.0-50.0%
  - Precision: ±1.5%
  - Units: Percentage (%)

- **lab_shrinkage_limit**: Shrinkage limit
  - Data Type: DECIMAL(4,1)
  - Range: 5.0-25.0%
  - Precision: ±1.0%
  - Units: Percentage (%)

**Biological Activity Parameters (12 parameters):**
- **lab_microbial_biomass_carbon**: MBC content
  - Data Type: DECIMAL(6,2)
  - Range: 50.00-2000.00 mg/kg
  - Precision: ±20 mg/kg
  - Units: Milligrams per kilogram (mg/kg)
  - Method: Chloroform fumigation-extraction

- **lab_microbial_biomass_nitrogen**: MBN content
  - Data Type: DECIMAL(5,2)
  - Range: 5.00-200.00 mg/kg
  - Precision: ±5 mg/kg
  - Units: Milligrams per kilogram (mg/kg)

- **lab_soil_respiration_rate**: CO₂ evolution
  - Data Type: DECIMAL(6,2)
  - Range: 10.00-500.00 mg CO₂/kg/day
  - Precision: ±10 mg CO₂/kg/day
  - Units: Milligrams CO₂ per kilogram per day

- **lab_dehydrogenase_activity**: Enzyme activity
  - Data Type: DECIMAL(6,2)
  - Range: 1.00-100.00 μg TPF/g/24hr
  - Precision: ±2.0 μg TPF/g/24hr
  - Units: Micrograms triphenylformazan per gram per 24 hours

- **lab_urease_activity**: Urease enzyme activity
  - Data Type: DECIMAL(6,2)
  - Range: 5.00-200.00 μg NH₄-N/g/2hr
  - Precision: ±5.0 μg NH₄-N/g/2hr
  - Units: Micrograms ammonium-N per gram per 2 hours

- **lab_phosphatase_activity**: Phosphatase activity
  - Data Type: DECIMAL(6,2)
  - Range: 10.00-500.00 μg PNP/g/hr
  - Precision: ±10 μg PNP/g/hr
  - Units: Micrograms p-nitrophenol per gram per hour

- **lab_beta_glucosidase_activity**: β-glucosidase activity
  - Data Type: DECIMAL(6,2)
  - Range: 5.00-300.00 μg PNP/g/hr
  - Precision: ±5.0 μg PNP/g/hr
  - Units: Micrograms p-nitrophenol per gram per hour

- **lab_arylsulfatase_activity**: Arylsulfatase activity
  - Data Type: DECIMAL(6,2)
  - Range: 1.00-100.00 μg PNP/g/hr
  - Precision: ±2.0 μg PNP/g/hr
  - Units: Micrograms p-nitrophenol per gram per hour

- **lab_metabolic_quotient**: qCO₂ ratio
  - Data Type: DECIMAL(5,3)
  - Range: 0.500-5.000 mg CO₂-C/mg MBC/hr
  - Calculation: soil_respiration / microbial_biomass_carbon
  - Units: Milligrams CO₂-C per milligram MBC per hour

- **lab_fungal_biomass**: Fungal biomass estimate
  - Data Type: DECIMAL(6,2)
  - Range: 10.00-800.00 mg/kg
  - Precision: ±15 mg/kg
  - Units: Milligrams per kilogram (mg/kg)
  - Method: Ergosterol extraction

- **lab_bacterial_biomass**: Bacterial biomass estimate
  - Data Type: DECIMAL(6,2)
  - Range: 20.00-1500.00 mg/kg
  - Precision: ±25 mg/kg
  - Units: Milligrams per kilogram (mg/kg)

- **lab_fungi_bacteria_ratio**: F:B ratio
  - Data Type: DECIMAL(4,2)
  - Range: 0.10-5.00
  - Calculation: fungal_biomass / bacterial_biomass
  - Units: Dimensionless ratio

### 2. Data Provenance & Lineage

#### 2.1 Sensor Data Flow
```
IoT Sensors → LoRa Mesh Network → Master Node → Backend API → TimescaleDB → AI Engine
```

**Processing Steps:**
1. **Sensor Reading**: Modbus RTU data collection every 15 minutes
2. **Local Validation**: Quality scoring and outlier detection at sensor level
3. **Mesh Transmission**: Compressed data packets through LoRa network
4. **Master Node Aggregation**: Data collection and batch processing
5. **Backend Ingestion**: RESTful API with authentication and validation
6. **Database Storage**: TimescaleDB hypertables for time-series optimization
7. **AI Processing**: Feature engineering and model inference

#### 2.2 Manual Data Flow
```
User Interface → Validation Layer → Backend API → PostgreSQL → AI Feature Store
```

**Processing Steps:**
1. **Data Entry**: Role-based forms with validation rules
2. **Quality Checks**: Range validation and consistency checks
3. **API Processing**: Structured data transformation
4. **Database Storage**: Relational storage with audit trails
5. **Feature Integration**: Joining with sensor data for ML features

#### 2.3 External Data Flow
```
External APIs → Data Adapters → Validation Layer → Cache Layer → AI Engine
```

**Processing Steps:**
1. **API Polling**: Scheduled data retrieval from external sources
2. **Format Standardization**: Converting to internal data schemas
3. **Quality Validation**: Completeness and accuracy checks
4. **Caching Strategy**: Redis caching for frequently accessed data
5. **Feature Enrichment**: Augmenting sensor data with external context

---

## 📊 Data Type Classifications

### 3. Complete Data Classification Matrix

#### 3.1 Classification by Source Type

**IoT Sensor Data (156 parameters):**
- Primary soil measurements: 7 parameters
- Secondary soil measurements: 12 parameters
- Sensor metadata: 23 parameters
- Spatial coordinates: 8 parameters
- System status indicators: 106 parameters

**Manual Entry Data (127 parameters):**
- FFB harvest data: 18 parameters
- Fertilizer application: 22 parameters
- Labor operations: 31 parameters
- Equipment usage: 28 parameters
- Cost tracking: 28 parameters

**External API Data (127 parameters):**
- Weather services: 31 parameters
- Weather forecasts: 15 parameters
- MPOB regulatory: 12 parameters
- Agricultural research: 45 parameters
- Satellite imagery: 24 parameters (planned)

**Laboratory Analysis (196 parameters):**
- Sample metadata: 15 parameters
- Chemical analysis: 28 parameters
- Physical properties: 18 parameters
- Biological activity: 12 parameters
- Quality control: 123 parameters

**Calculated/Derived Data (241 parameters):**
- Feature engineering: 89 parameters
- Model predictions: 67 parameters
- Confidence scores: 45 parameters
- Aggregated metrics: 40 parameters

#### 3.2 Classification by Data Format

**Numerical Data (634 parameters):**
- **Continuous Variables (487 parameters):**
  - Soil measurements: pH, moisture, temperature, nutrients
  - Weather data: temperature, humidity, rainfall, wind
  - Laboratory results: chemical concentrations, physical properties
  - Spatial coordinates: latitude, longitude, elevation
  - Calculated features: indices, ratios, aggregations

- **Discrete Variables (147 parameters):**
  - Count data: bunch count, error count, sensor readings count
  - Integer identifiers: sampling intervals, batch numbers
  - Binary flags: is_active, is_online, calibration_status
  - Ordinal scales: quality grades, ripeness index

**Categorical Data (156 parameters):**
- **Nominal Categories (98 parameters):**
  - Equipment types: sensor_type, fertilizer_type, application_method
  - Status indicators: calibration_status, weather_condition
  - Classification labels: soil_texture_class, crop_type
  - Identifier codes: device_id, laboratory_id, regulation_id

- **Ordinal Categories (58 parameters):**
  - Quality grades: FFB quality (A, B, C, Reject)
  - Risk levels: Low, Medium, High
  - Confidence levels: High, Medium, Low
  - Compliance levels: mandatory, recommended, best_practice

**Temporal Data (57 parameters):**
- **Timestamps (31 parameters):**
  - Sensor readings: measurement timestamps
  - Weather observations: forecast timestamps
  - Laboratory analysis: collection and analysis dates
  - System events: last_communication, calibration_date

- **Time Intervals (26 parameters):**
  - Sampling intervals: sensor reading frequency
  - Forecast periods: prediction time horizons
  - Retention periods: data lifecycle management
  - Processing windows: aggregation time frames

#### 3.3 Classification by Processing Stage

**Raw Data (312 parameters):**
- Direct sensor measurements (156 parameters)
- Manual data entry (127 parameters)
- External API responses (29 parameters)

**Preprocessed Data (198 parameters):**
- Quality-validated sensor data (89 parameters)
- Cleaned manual entries (67 parameters)
- Standardized API data (42 parameters)

**Engineered Features (234 parameters):**
- Temporal features: lag variables, moving averages, seasonal indicators
- Spatial features: distance calculations, interpolated values
- Derived metrics: ratios, indices, normalized values
- Cross-source features: sensor-weather correlations

**Model Outputs (103 parameters):**
- Primary predictions: N, P, K, pH forecasts (28 parameters)
- Confidence scores: prediction uncertainty (28 parameters)
- Ensemble weights: model contribution factors (12 parameters)
- Explanation features: SHAP values, feature importance (35 parameters)

#### 3.4 Classification by Update Frequency

**Real-Time Data (<1 minute, 89 parameters):**
- System status: sensor connectivity, battery levels
- Alert conditions: threshold violations, system errors
- User interactions: dashboard requests, API calls

**High-Frequency Data (1-60 minutes, 234 parameters):**
- Sensor measurements: 15-minute soil parameter readings
- Weather observations: hourly meteorological data
- System monitoring: performance metrics, health checks

**Daily Data (156 parameters):**
- Manual operations: harvest records, fertilizer applications
- Calculated aggregates: daily averages, summaries
- Quality assessments: data validation results

**Weekly/Monthly Data (89 parameters):**
- Laboratory analysis: soil composition, nutrient validation
- Calibration data: sensor accuracy verification
- Performance reports: system efficiency metrics

**Quarterly/Annual Data (67 parameters):**
- Comprehensive soil analysis: texture, biological activity
- Regulatory compliance: MPOB standard updates
- System audits: data quality assessments

**Static Data (212 parameters):**
- Configuration settings: sensor parameters, thresholds
- Geographic data: coordinates, elevation, terrain features
- Reference data: calibration standards, lookup tables

#### 3.5 Classification by Data Volume Characteristics

**High-Volume Streams (>1GB/month, 156 parameters):**
- Sensor time-series: continuous soil parameter measurements
- Weather data: high-resolution meteorological observations
- System logs: operational events, error tracking

**Medium-Volume Data (100MB-1GB/month, 234 parameters):**
- Manual entries: operational records, harvest data
- Laboratory results: analytical measurements
- Calculated features: derived metrics, aggregations

**Low-Volume Data (<100MB/month, 457 parameters):**
- Configuration data: system settings, parameters
- Reference data: standards, thresholds, lookup tables
- Metadata: data descriptions, quality indicators

---

## 🔗 Variable Relationships & Dependencies

### 4. Input-Output Relationship Matrix

#### 4.1 Sensor-to-Model Data Flow

**Primary Soil Parameters → ML Model Inputs:**
- **soil_moisture** → moisture_feature, moisture_lag_7d, moisture_rolling_mean_30d
- **soil_temperature** → temperature_feature, temperature_seasonal_component
- **soil_ph** → ph_feature, ph_deviation_from_optimal
- **soil_ec** → ec_feature, ec_normalized, salinity_index
- **soil_nitrogen** → n_feature, n_availability_index
- **soil_phosphorus** → p_feature, p_availability_index
- **soil_potassium** → k_feature, k_availability_index

**Spatial Data → Spatial Features:**
- **latitude, longitude** → utm_x, utm_y, spatial_cluster_id
- **elevation** → elevation_normalized, topographic_wetness_index
- **slope, aspect** → terrain_complexity_index, solar_radiation_potential

**Temporal Data → Time Features:**
- **timestamp** → hour_sin, hour_cos, day_of_year_sin, day_of_year_cos
- **measurement_date** → season_indicator, monsoon_phase, agricultural_calendar_phase

#### 4.2 Cross-Validation Relationships

**Sensor ↔ Laboratory Validation:**
- **sensor_ph** ↔ **lab_ph_water**: Correlation coefficient >0.95
- **sensor_nitrogen** ↔ **lab_available_nitrogen**: Validation tolerance ±10%
- **sensor_phosphorus** ↔ **lab_available_phosphorus**: Validation tolerance ±15%
- **sensor_potassium** ↔ **lab_exchangeable_potassium**: Validation tolerance ±12%
- **sensor_ec** ↔ **lab_electrical_conductivity**: Correlation coefficient >0.92

**Weather ↔ Soil Parameter Correlations:**
- **rainfall** ↔ **soil_moisture**: Positive correlation (r=0.78)
- **air_temperature** ↔ **soil_temperature**: Strong correlation (r=0.85)
- **evapotranspiration** ↔ **soil_moisture**: Negative correlation (r=-0.65)
- **humidity** ↔ **soil_moisture**: Moderate correlation (r=0.52)

#### 4.3 Calculated Variable Dependencies

**Derived Soil Health Metrics:**
```
soil_health_index = (
    normalize_ph(soil_ph) * 0.30 +
    normalize_nutrients(soil_n, soil_p, soil_k) * 0.40 +
    normalize_moisture(soil_moisture) * 0.20 +
    normalize_temperature(soil_temperature) * 0.10
)
```

**Nutrient Availability Index:**
```
nutrient_availability = (
    (soil_nitrogen / optimal_n_range) * 0.35 +
    (soil_phosphorus / optimal_p_range) * 0.35 +
    (soil_potassium / optimal_k_range) * 0.30
)
```

**Spatial Interpolation Dependencies:**
```
kriging_prediction = f(
    target_coordinates,
    neighbor_values[1:12],
    spatial_distances[1:12],
    variogram_parameters
)
```

**Ensemble Model Dependencies:**
```
final_prediction = (
    xgboost_prediction * ensemble_weight_xgb +
    neural_network_prediction * ensemble_weight_nn +
    kriging_prediction * ensemble_weight_kriging
)
```

#### 4.4 Feature Engineering Dependencies

**Lag Feature Creation:**
- **soil_moisture_lag_1d** ← soil_moisture[t-1]
- **soil_moisture_lag_7d** ← soil_moisture[t-7]
- **soil_moisture_rolling_mean_30d** ← mean(soil_moisture[t-30:t])

**Spatial Feature Engineering:**
- **distance_to_water** ← calculate_distance(sensor_coordinates, water_sources)
- **elevation_relative** ← sensor_elevation - mean_block_elevation
- **slope_category** ← categorize_slope(slope_degrees)

**Cross-Source Feature Integration:**
- **moisture_weather_interaction** ← soil_moisture * rainfall_24h
- **temperature_differential** ← soil_temperature - air_temperature
- **stress_index** ← f(soil_moisture, temperature, humidity)

---

## 🔧 Data Processing & Utilization

### 3. Data Preprocessing Pipeline

#### 3.1 Data Cleaning & Validation
**Quality Scoring Algorithm:**
```python
data_quality_score = (
    ph_validity_score * 0.2 +
    moisture_validity_score * 0.2 +
    temperature_validity_score * 0.2 +
    ec_validity_score * 0.2 +
    battery_level_score * 0.2
)
```

**Validation Rules:**
- **Range Validation**: Parameter-specific bounds checking
- **Temporal Consistency**: Detecting sudden unrealistic changes
- **Spatial Consistency**: Cross-sensor validation for nearby sensors
- **Statistical Outliers**: IQR-based outlier detection and flagging

#### 3.2 Feature Engineering Process

**Core Features (Primary Sensor Data):**
- `ec`: Electrical conductivity (direct sensor reading)
- `moisture`: Soil moisture percentage (direct sensor reading)
- `temperature`: Soil temperature in Celsius (direct sensor reading)
- `latitude`, `longitude`: GPS coordinates (spatial features)
- `elevation`: Elevation data (derived from GPS + DEM)

**Derived Features (Engineered):**
- `hour`: Hour of day (temporal pattern extraction)
- `day_of_year`: Day of year (seasonal pattern extraction)
- `season`: Categorical season (wet/dry season classification)
- `soil_type`: Soil classification (derived from texture analysis)

**Spatial Features:**
- **Distance Calculations**: Distance to water sources, estate boundaries
- **Topographic Features**: Slope, aspect, elevation derivatives
- **Neighborhood Features**: Spatial averaging of nearby sensor readings

**Temporal Features:**
- **Moving Averages**: 7-day, 30-day rolling averages
- **Lag Features**: Previous day/week values for trend analysis
- **Seasonal Decomposition**: Trend, seasonal, and residual components

#### 3.3 Data Integration & Fusion

**Multi-Source Integration Strategy:**
1. **Temporal Alignment**: Synchronizing data from different sources to common timestamps
2. **Spatial Interpolation**: Kriging interpolation for missing spatial data
3. **Feature Scaling**: StandardScaler normalization for neural network inputs
4. **Missing Value Handling**: Median imputation with quality score weighting

**Data Fusion Techniques:**
- **Sensor Fusion**: Combining multiple sensor readings for robust measurements
- **Cross-Validation**: Laboratory data validation of sensor accuracy
- **Ensemble Features**: Creating meta-features from multiple data sources

---

## 🤖 Model-Data Interaction Mechanisms

### 4. Machine Learning Architecture

#### 4.1 Ensemble Model Structure
**Architecture Overview:**
```
Input Features → [XGBoost Model] → Prediction 1 (40% weight)
               → [Neural Network] → Prediction 2 (30% weight)
               → [Kriging Model] → Prediction 3 (30% weight)
                                → Ensemble Prediction
```

**Ensemble Weighting Strategy:**
- **XGBoost (40%)**: Handles non-linear relationships and feature interactions
- **Neural Network (30%)**: Captures complex patterns and temporal dependencies
- **Kriging (30%)**: Provides spatial interpolation and uncertainty quantification

#### 4.2 XGBoost Model Configuration
**Model Parameters:**
- **Objective**: Multi-output regression for N, P, K, pH prediction
- **Boosting Rounds**: 1000 with early stopping (patience=50)
- **Learning Rate**: 0.1 with adaptive scheduling
- **Max Depth**: 6 (prevents overfitting)
- **Subsample**: 0.8 (row sampling for robustness)
- **Feature Fraction**: 0.8 (column sampling for generalization)

**Feature Importance Handling:**
- **SHAP Values**: Explainable AI for feature contribution analysis
- **Permutation Importance**: Validation of feature relevance
- **Feature Selection**: Recursive feature elimination for optimal subset

#### 4.3 Neural Network Architecture
**Network Structure:**
- **Input Layer**: Variable size based on feature count
- **Hidden Layers**: [256, 128, 64, 32] neurons with ReLU activation
- **Dropout**: 0.2 dropout rate for regularization
- **Batch Normalization**: Stable training and faster convergence
- **Output Layer**: Multi-output for simultaneous parameter prediction

**Training Configuration:**
- **Optimizer**: Adam with learning rate 0.001
- **Loss Function**: Mean Squared Error with L2 regularization
- **Batch Size**: 32 samples per batch
- **Early Stopping**: Patience of 20 epochs on validation loss
- **Learning Rate Scheduling**: ReduceLROnPlateau with factor 0.5

#### 4.4 Kriging Spatial Interpolation
**Spatial Modeling:**
- **Variogram Analysis**: Automatic variogram fitting for spatial correlation
- **Kriging Type**: Ordinary Kriging with Gaussian variogram model
- **Neighborhood**: 12 nearest neighbors for interpolation
- **Cross-Validation**: Leave-one-out validation for spatial accuracy

**Uncertainty Quantification:**
- **Kriging Variance**: Spatial uncertainty estimation
- **Confidence Intervals**: 95% confidence bounds for predictions
- **Spatial Correlation**: Modeling spatial dependencies in soil parameters

---

## ⚙️ Critical Technical Considerations

### 5. Data Quality Requirements

#### 5.1 Quality Assurance Framework
**Automated Quality Checks:**
- **Completeness**: >95% data availability requirement
- **Accuracy**: ±5% tolerance for nutrient measurements
- **Consistency**: <3σ deviation from temporal trends
- **Timeliness**: <30 seconds processing latency

**Quality Scoring Metrics:**
- **Sensor Health**: Battery level, signal strength, calibration status
- **Data Validity**: Range checks, statistical outlier detection
- **Cross-Validation**: Laboratory validation against sensor readings
- **Temporal Consistency**: Change rate validation and trend analysis

#### 5.2 Performance Implications

**Computational Requirements:**
- **Training Time**: 2-4 hours for full ensemble model retraining
- **Inference Time**: <200ms for single prediction request
- **Memory Usage**: 4GB RAM for model loading and inference
- **Storage Requirements**: 100GB for 1 year of sensor data

**Scalability Considerations:**
- **Horizontal Scaling**: Distributed training across multiple nodes
- **Model Serving**: Load balancing for high-throughput inference
- **Data Partitioning**: Time-based partitioning for efficient queries
- **Caching Strategy**: Redis caching for frequently accessed predictions

#### 5.3 Data Pipeline Bottlenecks

**Identified Bottlenecks:**
1. **Sensor Data Ingestion**: LoRa network bandwidth limitations
2. **Feature Engineering**: CPU-intensive spatial calculations
3. **Model Training**: Memory constraints for large datasets
4. **Real-time Inference**: Concurrent request handling

**Optimization Strategies:**
- **Data Compression**: GZIP compression for network transmission
- **Batch Processing**: Aggregating sensor readings for efficient processing
- **Model Optimization**: Quantization and pruning for faster inference
- **Parallel Processing**: Multi-threading for feature engineering

---

## 🔄 Data Flow Patterns & Feedback Loops

### 6. System Data Flow Architecture

#### 6.1 Real-Time Processing Pipeline
```
Sensors → Validation → Feature Engineering → Model Inference → Results → Dashboard
    ↓         ↓              ↓                    ↓           ↓         ↓
  Quality   Range         Spatial            Ensemble    Confidence  User
  Scoring   Checks      Calculations        Prediction   Scoring   Interface
```

**Processing Stages:**
1. **Data Ingestion**: Real-time sensor data collection and validation
2. **Quality Assessment**: Automated quality scoring and outlier detection
3. **Feature Engineering**: Spatial and temporal feature creation
4. **Model Inference**: Ensemble prediction with uncertainty quantification
5. **Result Processing**: Confidence scoring and result formatting
6. **User Delivery**: Dashboard updates and alert generation

#### 6.2 Feedback Mechanisms

**Model Performance Feedback:**
- **Prediction Accuracy Tracking**: Continuous validation against actual outcomes
- **Model Drift Detection**: Statistical tests for distribution changes
- **Retraining Triggers**: Automated retraining based on performance degradation
- **Human-in-the-Loop**: Expert validation and correction of predictions

**Data Quality Feedback:**
- **Sensor Calibration**: Automated calibration based on laboratory validation
- **Anomaly Detection**: Real-time detection and flagging of unusual readings
- **Quality Score Updates**: Dynamic quality scoring based on validation results
- **Alert Generation**: Automated alerts for data quality issues

**User Interaction Feedback:**
- **Prediction Validation**: User feedback on prediction accuracy
- **Recommendation Effectiveness**: Tracking implementation of AI recommendations
- **Dashboard Usage**: Analytics on user interaction patterns
- **Feature Requests**: User feedback driving feature development

---

## 📊 Data Architecture Summary

### 7. System Integration Overview

**Data Sources Integration:**
- **5 Primary Sources**: IoT sensors, manual entry, external APIs, lab results, historical data
- **Real-Time Processing**: <30 seconds end-to-end latency
- **Quality Assurance**: 96.2% average data quality across all sources
- **Scalability**: Designed for 1000+ sensors across multiple estates

**ML Model Integration:**
- **Ensemble Approach**: XGBoost + Neural Network + Kriging for robust predictions
- **Prediction Accuracy**: 89-94% confidence across all soil parameters
- **Explainable AI**: SHAP values for prediction interpretability
- **Continuous Learning**: Automated model updates based on new data

**Technical Performance:**
- **Processing Speed**: 145ms average API response time
- **Data Throughput**: 50,000+ sensor readings processed per month
- **Storage Efficiency**: TimescaleDB optimization for time-series data
- **Reliability**: 98.5% system uptime with automated failover

This comprehensive data architecture enables precise soil parameter prediction and management recommendations for agricultural optimization across Malaysian oil palm estates.

---

## 🔍 Detailed Data Specifications

### 8. Sensor Data Schemas & Formats

#### 8.1 IoT Sensor Data Structure
**Primary Sensor Reading Schema:**
```json
{
  "sensor_id": "UUID",
  "timestamp": "ISO8601",
  "location": {
    "latitude": "float (-90 to 90)",
    "longitude": "float (-180 to 180)",
    "elevation": "float (meters)"
  },
  "measurements": {
    "soil_moisture": "decimal (0-100%)",
    "soil_temperature": "decimal (-50 to 80°C)",
    "soil_ph": "decimal (0-14)",
    "soil_ec": "decimal (≥0 mS/cm)",
    "soil_nitrogen": "decimal (≥0 mg/kg)",
    "soil_phosphorus": "decimal (≥0 mg/kg)",
    "soil_potassium": "decimal (≥0 mg/kg)"
  },
  "metadata": {
    "battery_level": "decimal (0-100%)",
    "signal_strength": "decimal (0-100%)",
    "data_quality_score": "decimal (0-1)",
    "calibration_status": "string",
    "firmware_version": "string"
  }
}
```

**Data Validation Rules:**
- **Soil Moisture**: 0-100% range with ±3% accuracy tolerance
- **Soil Temperature**: -50°C to 80°C with ±0.5°C accuracy
- **Soil pH**: 0-14 range with ±0.1 pH unit accuracy
- **Electrical Conductivity**: Non-negative values with ±2% accuracy
- **NPK Values**: Non-negative with ±5% accuracy tolerance

#### 8.2 Communication Protocol Specifications
**LoRa Mesh Network Protocol:**
- **Frequency Bands**: 433MHz/475MHz (ISM band)
- **Modulation**: LoRa with spreading factor 7-12
- **Power Output**: 30dBm maximum transmission power
- **Range**: Up to 10km line-of-sight
- **Data Rate**: 0.3-50 kbps depending on spreading factor

**Message Frame Structure:**
```
Header (16 bytes):
├── Sync Pattern: 0xAA55 (2 bytes)
├── Source Address: 0x0000-0xFFFE (2 bytes)
├── Destination Address: 0x0000-0xFFFF (2 bytes)
├── Message Type: 0x01 (SENSOR_DATA) (1 byte)
├── Sequence Number: 0x00-0xFF (1 byte)
├── Payload Length: 0x00-0x30 (1 byte)
├── Timestamp: Unix timestamp (4 bytes)
└── CRC32 Checksum (4 bytes)

Payload (0-48 bytes):
└── Sensor reading data (JSON compressed)
```

### 9. Feature Engineering Deep Dive

#### 9.1 Spatial Feature Engineering
**Coordinate Transformation:**
- **Input**: WGS84 GPS coordinates (latitude, longitude)
- **Processing**: UTM projection for distance calculations
- **Derived Features**:
  - Distance to estate boundaries
  - Distance to water sources
  - Elevation derivatives (slope, aspect)
  - Spatial clustering indices

**Kriging Interpolation Process:**
1. **Variogram Analysis**: Automatic fitting of spatial correlation models
2. **Model Selection**: Gaussian, exponential, or spherical variogram models
3. **Cross-Validation**: Leave-one-out validation for optimal parameters
4. **Interpolation**: Ordinary Kriging with 12 nearest neighbors
5. **Uncertainty**: Kriging variance for confidence estimation

#### 9.2 Temporal Feature Engineering
**Time-Based Features:**
- **Cyclical Encoding**: Sin/cos transformation for hour, day, month
- **Seasonal Indicators**: Wet season (October-March), dry season (April-September)
- **Agricultural Calendar**: Planting, fertilization, harvest periods
- **Weather Patterns**: Monsoon indicators and rainfall patterns

**Lag Feature Creation:**
```python
# Example lag feature engineering
def create_lag_features(df, target_cols, lag_periods=[1, 7, 30]):
    for col in target_cols:
        for lag in lag_periods:
            df[f'{col}_lag_{lag}d'] = df[col].shift(lag)
            df[f'{col}_rolling_mean_{lag}d'] = df[col].rolling(lag).mean()
            df[f'{col}_rolling_std_{lag}d'] = df[col].rolling(lag).std()
    return df
```

#### 9.3 Cross-Source Feature Integration
**Multi-Modal Feature Fusion:**
- **Sensor-Weather Fusion**: Combining soil readings with weather data
- **Manual-Sensor Correlation**: Linking fertilizer applications with soil changes
- **Laboratory Validation**: Cross-referencing sensor accuracy with lab results
- **Historical Context**: Incorporating long-term agricultural patterns

**Feature Scaling and Normalization:**
- **StandardScaler**: Zero mean, unit variance for neural network inputs
- **MinMaxScaler**: 0-1 scaling for tree-based models
- **RobustScaler**: Median-based scaling for outlier resistance
- **QuantileTransformer**: Non-linear scaling for non-normal distributions

---

## 🧠 Advanced ML Model Mechanisms

### 10. Model Training & Optimization

#### 10.1 Ensemble Training Pipeline
**Training Data Preparation:**
1. **Data Splitting**: 70% training, 15% validation, 15% testing
2. **Temporal Splitting**: Ensuring no data leakage across time periods
3. **Stratified Sampling**: Balanced representation across estate blocks
4. **Cross-Validation**: 5-fold time-series cross-validation

**Individual Model Training:**
```python
# XGBoost Training Configuration
xgb_params = {
    'objective': 'reg:squarederror',
    'n_estimators': 1000,
    'learning_rate': 0.1,
    'max_depth': 6,
    'subsample': 0.8,
    'colsample_bytree': 0.8,
    'early_stopping_rounds': 50
}

# Neural Network Training Configuration
nn_params = {
    'hidden_sizes': [256, 128, 64, 32],
    'dropout_rate': 0.2,
    'learning_rate': 0.001,
    'batch_size': 32,
    'epochs': 200,
    'early_stopping_patience': 20
}
```

#### 10.2 Hyperparameter Optimization
**Optimization Strategy:**
- **Bayesian Optimization**: Efficient hyperparameter search
- **Grid Search**: Exhaustive search for critical parameters
- **Random Search**: Exploration of hyperparameter space
- **Multi-Objective Optimization**: Balancing accuracy and inference speed

**Parameter Search Spaces:**
- **XGBoost**: Learning rate (0.01-0.3), max depth (3-10), subsample (0.6-1.0)
- **Neural Network**: Hidden sizes, dropout rates, learning rates
- **Ensemble Weights**: Optimal combination weights for model outputs

#### 10.3 Model Validation & Testing
**Validation Metrics:**
- **Regression Metrics**: MAE, RMSE, R², MAPE for continuous predictions
- **Classification Metrics**: Precision, recall, F1-score for categorical outputs
- **Spatial Metrics**: Spatial autocorrelation, kriging cross-validation error
- **Temporal Metrics**: Time-series forecast accuracy, trend detection

**Cross-Validation Strategy:**
```python
# Time-series aware cross-validation
def time_series_cv(data, n_splits=5):
    for i in range(n_splits):
        train_end = len(data) * (i + 1) // (n_splits + 1)
        val_start = train_end
        val_end = len(data) * (i + 2) // (n_splits + 1)

        train_data = data[:train_end]
        val_data = data[val_start:val_end]

        yield train_data, val_data
```

### 11. Real-Time Inference Architecture

#### 11.1 Model Serving Infrastructure
**Inference Pipeline:**
1. **Request Processing**: API request validation and parsing
2. **Feature Engineering**: Real-time feature computation
3. **Model Loading**: Cached model instances for fast inference
4. **Ensemble Prediction**: Parallel execution of individual models
5. **Result Aggregation**: Weighted combination of model outputs
6. **Confidence Scoring**: Uncertainty quantification and confidence intervals

**Performance Optimization:**
- **Model Caching**: In-memory model storage for fast access
- **Batch Inference**: Processing multiple requests simultaneously
- **Async Processing**: Non-blocking inference for high throughput
- **Load Balancing**: Distributing requests across multiple inference servers

#### 11.2 Uncertainty Quantification
**Confidence Estimation Methods:**
- **Ensemble Variance**: Disagreement between individual models
- **Bootstrap Sampling**: Resampling-based confidence intervals
- **Bayesian Neural Networks**: Uncertainty estimation through weight distributions
- **Kriging Variance**: Spatial uncertainty from kriging interpolation

**Confidence Score Calculation:**
```python
def calculate_confidence_score(predictions, uncertainties):
    # Ensemble variance
    ensemble_var = np.var(predictions, axis=0)

    # Normalized uncertainty
    normalized_uncertainty = uncertainties / (uncertainties.max() + 1e-8)

    # Combined confidence score
    confidence = 1.0 - (ensemble_var + normalized_uncertainty) / 2.0

    return np.clip(confidence, 0.0, 1.0)
```

#### 11.3 Model Monitoring & Drift Detection
**Performance Monitoring:**
- **Prediction Accuracy**: Continuous validation against ground truth
- **Model Drift**: Statistical tests for distribution changes
- **Feature Drift**: Monitoring input feature distributions
- **Concept Drift**: Detecting changes in target relationships

**Automated Retraining Triggers:**
- **Accuracy Degradation**: Retraining when accuracy drops below threshold
- **Data Drift**: Retraining when input distributions change significantly
- **Temporal Triggers**: Scheduled retraining (monthly/quarterly)
- **Manual Triggers**: Expert-initiated retraining based on domain knowledge

---

## 🔧 System Integration & Scalability

### 12. Database Architecture & Optimization

#### 12.1 TimescaleDB Implementation
**Hypertable Configuration:**
```sql
-- Sensor readings hypertable
CREATE TABLE sensor_readings (
    time TIMESTAMPTZ NOT NULL,
    sensor_id UUID NOT NULL,
    soil_moisture DECIMAL(5,2),
    soil_temperature DECIMAL(5,2),
    soil_ph DECIMAL(4,2),
    soil_ec DECIMAL(8,2),
    location GEOMETRY(POINT, 4326),
    data_quality_score DECIMAL(3,2)
);

-- Convert to hypertable with 1-day chunks
SELECT create_hypertable('sensor_readings', 'time', chunk_time_interval => INTERVAL '1 day');

-- Create indexes for efficient queries
CREATE INDEX idx_sensor_readings_sensor_time ON sensor_readings (sensor_id, time DESC);
CREATE INDEX idx_sensor_readings_location ON sensor_readings USING GIST (location);
```

**Query Optimization:**
- **Time-Based Partitioning**: 1-day chunks for optimal query performance
- **Compression**: Automatic compression for historical data
- **Continuous Aggregates**: Pre-computed aggregations for common queries
- **Retention Policies**: Automated data lifecycle management

#### 12.2 Caching Strategy
**Redis Caching Implementation:**
- **Model Predictions**: Caching recent predictions for 1 hour
- **Feature Engineering**: Caching computed features for 30 minutes
- **API Responses**: Caching dashboard data for 5 minutes
- **Session Data**: User session and authentication caching

**Cache Invalidation Strategy:**
```python
# Cache invalidation on new data
def invalidate_cache_on_new_data(sensor_id, timestamp):
    cache_keys = [
        f"predictions:{sensor_id}:*",
        f"features:{sensor_id}:*",
        f"dashboard:estate:{get_estate_id(sensor_id)}"
    ]

    for pattern in cache_keys:
        redis_client.delete(*redis_client.keys(pattern))
```

### 13. Scalability & Performance Considerations

#### 13.1 Horizontal Scaling Architecture
**Microservices Design:**
- **Data Ingestion Service**: Handles sensor data collection and validation
- **Feature Engineering Service**: Processes raw data into ML features
- **Model Serving Service**: Handles ML inference and predictions
- **API Gateway**: Routes requests and handles authentication
- **Dashboard Service**: Serves frontend applications

**Load Balancing Strategy:**
- **Round-Robin**: Equal distribution of requests across services
- **Weighted Routing**: Performance-based request distribution
- **Health Checks**: Automatic failover for unhealthy services
- **Circuit Breakers**: Preventing cascade failures

#### 13.2 Performance Benchmarks
**Current System Performance:**
- **API Response Time**: 145ms average (target: <200ms)
- **Data Processing Latency**: 18 seconds average (target: <30s)
- **Throughput**: 1000+ concurrent requests per second
- **Data Quality**: 96.2% average quality score (target: >95%)

**Scalability Targets:**
- **Sensor Capacity**: Support for 10,000+ sensors
- **Data Volume**: 1TB+ of sensor data per year
- **Concurrent Users**: 1000+ simultaneous dashboard users
- **Geographic Scale**: Multi-estate deployment across Malaysia

#### 13.3 Disaster Recovery & Backup
**Backup Strategy:**
- **Database Backups**: Daily full backups with point-in-time recovery
- **Model Versioning**: Git-based model version control
- **Configuration Backups**: Infrastructure as code with version control
- **Data Replication**: Real-time replication to secondary data centers

**Recovery Procedures:**
- **RTO (Recovery Time Objective)**: 4 hours maximum downtime
- **RPO (Recovery Point Objective)**: 1 hour maximum data loss
- **Automated Failover**: Health monitoring with automatic failover
- **Manual Recovery**: Documented procedures for manual intervention

---

## 📋 Conclusion & Future Enhancements

### 14. System Maturity & Roadmap

#### 14.1 Current Capabilities Summary
**Operational Excellence:**
- **Data Integration**: 5 primary data sources with 98.5% reliability
- **ML Performance**: 89-94% prediction accuracy across soil parameters
- **Real-Time Processing**: <30 seconds end-to-end latency
- **Quality Assurance**: Automated validation with 96.2% data quality

**Technical Achievements:**
- **Ensemble ML**: XGBoost + Neural Network + Kriging integration
- **Spatial Analytics**: Advanced kriging interpolation for spatial predictions
- **Explainable AI**: SHAP-based feature importance and prediction explanations
- **Scalable Architecture**: Microservices design supporting horizontal scaling

#### 14.2 Planned Enhancements
**Short-Term (3-6 months):**
- **Advanced Weather Integration**: Complete weather API integration
- **Laboratory Automation**: Automated lab equipment data integration
- **Enhanced Spatial Models**: Advanced spatial interpolation techniques
- **Mobile Optimization**: Mobile-first dashboard optimization

**Medium-Term (6-12 months):**
- **Satellite Integration**: Satellite imagery for vegetation indices
- **Advanced Analytics**: Time-series forecasting and trend analysis
- **Multi-Estate Scaling**: Cross-estate data sharing and benchmarking
- **Edge Computing**: On-device inference for reduced latency

**Long-Term (1-2 years):**
- **Drone Integration**: Aerial imagery and precision agriculture
- **IoT Expansion**: Additional sensor types and environmental monitoring
- **AI Advancement**: Deep learning and transformer-based models
- **International Expansion**: Multi-country deployment and localization

---

## 📋 Complete Data Specifications Reference

### 15. Comprehensive Data Dictionary

#### 15.1 Database Schema Specifications

**Primary Tables and Storage Requirements:**

**sensor_readings (TimescaleDB Hypertable):**
- **Estimated Annual Storage**: 2.4 TB (50,000 readings/month × 12 months × 4KB/reading)
- **Partition Strategy**: Daily chunks (1-day intervals)
- **Compression**: Automatic compression after 7 days (75% size reduction)
- **Retention Policy**: 3 years online, 7 years archived
- **Index Strategy**:
  - Primary: (time DESC, sensor_id)
  - Spatial: GIST index on location geometry
  - Quality: B-tree index on data_quality_score

**manual_operations (PostgreSQL Table):**
- **Estimated Annual Storage**: 156 MB (1,000 entries/month × 12 months × 13KB/entry)
- **Partition Strategy**: Monthly partitions by operation_date
- **Backup Frequency**: Daily incremental, weekly full
- **Retention Policy**: 5 years online, permanent archive
- **Index Strategy**:
  - Primary: operation_id (UUID)
  - Foreign Keys: estate_id, block_id, user_id
  - Temporal: operation_date, created_at

**laboratory_analysis (PostgreSQL Table):**
- **Estimated Annual Storage**: 89 MB (100 samples/month × 12 months × 74KB/sample)
- **Partition Strategy**: Quarterly partitions by analysis_date
- **Quality Requirements**: 99.9% data integrity
- **Retention Policy**: 10 years (regulatory requirement)
- **Index Strategy**:
  - Primary: sample_id
  - Foreign Keys: sensor_id, laboratory_id
  - Quality: analysis_method, certification_standard

**weather_data (TimescaleDB Hypertable):**
- **Estimated Annual Storage**: 445 MB (hourly data × 31 parameters × 4KB/reading)
- **Partition Strategy**: Weekly chunks
- **Data Sources**: 3 primary APIs with failover
- **Retention Policy**: 2 years online, 5 years archived
- **Index Strategy**:
  - Primary: (timestamp DESC, station_id)
  - Spatial: location-based queries
  - Parameter: individual weather parameter indexes

#### 15.2 Data Quality Requirements Matrix

| Parameter Category | Completeness | Accuracy | Consistency | Timeliness |
|-------------------|--------------|----------|-------------|------------|
| **Primary Soil Parameters** | >98% | ±5% | >95% | <15 min |
| **Sensor Metadata** | >99% | ±2% | >98% | <1 min |
| **Weather Data** | >95% | ±3% | >92% | <60 min |
| **Laboratory Results** | >99.5% | ±1% | >99% | <24 hrs |
| **Manual Operations** | >97% | ±2% | >94% | <4 hrs |
| **Calculated Features** | >96% | ±5% | >93% | <30 sec |
| **Model Predictions** | >94% | ±8% | >91% | <200 ms |

#### 15.3 Access Patterns and Usage Frequency

**High-Frequency Access (>1000 queries/hour):**
- Real-time sensor readings: Dashboard displays
- Current weather conditions: Irrigation decisions
- System status indicators: Monitoring dashboards
- Recent predictions: Operational planning

**Medium-Frequency Access (100-1000 queries/hour):**
- Historical trend analysis: Weekly/monthly reports
- Laboratory validation data: Quality assurance
- Aggregated statistics: Performance metrics
- Spatial interpolation: Heat map generation

**Low-Frequency Access (<100 queries/hour):**
- Raw historical data: Research analysis
- Configuration parameters: System administration
- Audit logs: Compliance reporting
- Archive data: Long-term studies

#### 15.4 Data Lifecycle Management

**Data Retention Policies:**

**Tier 1 - Hot Storage (0-30 days):**
- All sensor readings: Full resolution, immediate access
- Weather data: Complete parameter set
- Manual operations: All operational records
- Model predictions: Full prediction history
- Storage Type: NVMe SSD
- Access Time: <10ms
- Backup: Real-time replication

**Tier 2 - Warm Storage (30 days - 1 year):**
- Sensor readings: Hourly aggregates + raw critical events
- Weather data: Daily summaries + extreme events
- Laboratory results: Complete records
- Model performance: Accuracy metrics
- Storage Type: SATA SSD
- Access Time: <100ms
- Backup: Daily incremental

**Tier 3 - Cold Storage (1-3 years):**
- Sensor readings: Daily aggregates
- Weather data: Monthly summaries
- Historical operations: Annual summaries
- Model archives: Quarterly snapshots
- Storage Type: High-capacity HDD
- Access Time: <1 second
- Backup: Weekly full

**Tier 4 - Archive Storage (3+ years):**
- Regulatory compliance data: As required by MPOB
- Research datasets: Compressed formats
- System audit logs: Security compliance
- Model development history: Version control
- Storage Type: Tape/Cloud archive
- Access Time: <1 hour
- Backup: Monthly verification

#### 15.5 Performance Benchmarks and SLAs

**API Response Time SLAs:**
- Real-time data queries: <200ms (99th percentile)
- Historical data queries: <2 seconds (95th percentile)
- Complex analytics queries: <30 seconds (90th percentile)
- Bulk data exports: <5 minutes (for 1GB datasets)

**Data Processing SLAs:**
- Sensor data ingestion: <30 seconds end-to-end
- Feature engineering: <2 minutes for full dataset
- Model inference: <500ms for single prediction
- Batch predictions: <10 minutes for 1000 predictions

**System Availability SLAs:**
- Core data services: 99.9% uptime
- ML prediction services: 99.5% uptime
- Dashboard services: 99.7% uptime
- Data ingestion: 99.8% uptime

**Data Quality SLAs:**
- Overall data quality score: >95%
- Sensor data accuracy: >96%
- Laboratory validation: >99%
- Model prediction accuracy: 89-94% confidence range

#### 15.6 Security and Compliance Specifications

**Data Classification Levels:**
- **Public**: Weather data, general agricultural statistics
- **Internal**: Aggregated estate performance metrics
- **Confidential**: Individual sensor readings, operational data
- **Restricted**: Laboratory results, proprietary algorithms

**Access Control Matrix:**
- **System Administrators**: Full database access, configuration management
- **Data Engineers**: ETL processes, data pipeline management
- **ML Engineers**: Model training data, feature engineering
- **Agronomists**: Soil analysis data, prediction results
- **Estate Managers**: Operational data for assigned estates
- **Researchers**: Anonymized datasets, statistical summaries

**Compliance Requirements:**
- **MPOB Standards**: Soil management data retention (5 years)
- **ISO 27001**: Information security management
- **GDPR Equivalent**: Personal data protection (where applicable)
- **Agricultural Data Standards**: Traceability and audit trails

**Encryption Standards:**
- **Data at Rest**: AES-256 encryption for all databases
- **Data in Transit**: TLS 1.3 for all API communications
- **Backup Encryption**: AES-256 for all backup storage
- **Key Management**: Hardware Security Module (HSM) based

#### 15.7 Disaster Recovery Specifications

**Recovery Time Objectives (RTO):**
- Critical systems: 2 hours maximum downtime
- Data services: 4 hours maximum downtime
- Analytics services: 8 hours maximum downtime
- Archive systems: 24 hours maximum downtime

**Recovery Point Objectives (RPO):**
- Real-time data: 5 minutes maximum data loss
- Operational data: 1 hour maximum data loss
- Historical data: 4 hours maximum data loss
- Archive data: 24 hours maximum data loss

**Backup Verification:**
- Daily: Automated backup integrity checks
- Weekly: Sample data restoration tests
- Monthly: Full system recovery simulation
- Quarterly: Cross-site disaster recovery drill

This comprehensive data architecture documentation provides the technical foundation for understanding, maintaining, and enhancing the Soil AI/ML Engine within the Yield Sight System. The complete inventory of 847 data parameters ensures full traceability and understanding of every data element flowing through the system, enabling informed decision-making for system optimization, scaling, and future development.
