# Yield Sight System - Production Deployment Guide

## 🚀 Overview

This guide provides comprehensive instructions for deploying the Yield Sight System frontend to production environments. The system is production-ready with enterprise-grade features, security, and performance optimizations.

## 📋 Prerequisites

### System Requirements
- **Node.js**: 18.x or higher
- **npm**: 9.x or higher
- **Memory**: Minimum 4GB RAM
- **Storage**: Minimum 10GB available space
- **Network**: Stable internet connection

### Required Services
- **Backend API**: Soil Master Backend running and accessible
- **Database**: PostgreSQL 15+ (configured in backend)
- **Redis**: For caching and sessions (optional but recommended)
- **CDN**: For static asset delivery (recommended)

## 🔧 Environment Setup

### 1. Environment Variables

Create `.env.production` file with the following variables:

```bash
# Application Configuration
NODE_ENV=production
NEXT_PUBLIC_APP_ENV=production
NEXT_PUBLIC_APP_NAME="Yield Sight System"
NEXT_PUBLIC_APP_VERSION=1.0.0

# URLs
NEXT_PUBLIC_APP_URL=https://yieldsight.com
NEXT_PUBLIC_API_URL=https://api.yieldsight.com/api
NEXT_PUBLIC_WS_URL=wss://api.yieldsight.com/ws

# Security
JWT_SECRET=your-super-secure-jwt-secret-32-chars-min
SESSION_SECRET=your-session-secret-32-chars-min

# Third-party Services
NEXT_PUBLIC_MAPS_API_KEY=your-google-maps-api-key
WEATHER_API_KEY=your-weather-api-key

# Monitoring
SENTRY_DSN=https://<EMAIL>/project-id
SENTRY_ORG=your-sentry-org
SENTRY_PROJECT=your-sentry-project
NEXT_PUBLIC_GA_TRACKING_ID=G-XXXXXXXXXX

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
```

### 2. SSL/TLS Configuration

Ensure SSL certificates are properly configured:
- Use Let's Encrypt for free SSL certificates
- Configure HTTPS redirect
- Enable HSTS headers
- Set up proper certificate renewal

## 🏗️ Build Process

### 1. Install Dependencies

```bash
cd soil-frontend
npm ci --production
```

### 2. Build Application

```bash
# Production build
npm run build

# Verify build
npm run start
```

### 3. Bundle Analysis (Optional)

```bash
# Analyze bundle size
npm run analyze
```

## 🖥️ Self-Hosted Deployment (Ubuntu Server 24.04 LTS)

### 1. Server Preparation

```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install Node.js 18+ via NodeSource
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PM2 for process management
sudo npm install -g pm2

# Install Nginx for reverse proxy
sudo apt install nginx -y

# Install certbot for SSL
sudo apt install certbot python3-certbot-nginx -y
```

### 2. Application Deployment

```bash
# Clone or upload application files
cd /var/www
sudo mkdir yieldsight-frontend
sudo chown $USER:$USER yieldsight-frontend
cd yieldsight-frontend

# Copy application files (via rsync, scp, or git)
# Example with rsync:
rsync -avz --exclude node_modules ./soil-frontend/ /var/www/yieldsight-frontend/

# Install dependencies
npm ci --production

# Build application
npm run build

# Set up environment variables
cp .env.example .env.production
# Edit .env.production with production values
```

### 3. Process Management with PM2

```bash
# Create PM2 ecosystem file
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'yieldsight-frontend',
    script: 'npm',
    args: 'start',
    cwd: '/var/www/yieldsight-frontend',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    instances: 'max',
    exec_mode: 'cluster',
    watch: false,
    max_memory_restart: '1G',
    error_file: '/var/log/yieldsight/error.log',
    out_file: '/var/log/yieldsight/out.log',
    log_file: '/var/log/yieldsight/combined.log',
    time: true
  }]
};
EOF

# Create log directory
sudo mkdir -p /var/log/yieldsight
sudo chown $USER:$USER /var/log/yieldsight

# Start application with PM2
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

## ☁️ Cloud Deployment

### AWS Deployment

#### Using AWS EC2

1. **Launch EC2 Instance**
```bash
# Launch Ubuntu Server 24.04 LTS instance
# Recommended: t3.medium or larger
# Security Group: Allow HTTP (80), HTTPS (443), SSH (22)
```

2. **Deploy Application**
```bash
# Connect to EC2 instance
ssh -i your-key.pem ubuntu@your-ec2-ip

# Follow self-hosted deployment steps above
# Install Node.js, PM2, Nginx, and deploy application
```

3. **Configure Load Balancer (Optional)**
```bash
# Create Application Load Balancer
aws elbv2 create-load-balancer \
  --name yieldsight-alb \
  --subnets subnet-12345 subnet-67890 \
  --security-groups sg-12345

# Create target group
aws elbv2 create-target-group \
  --name yieldsight-targets \
  --protocol HTTP \
  --port 3000 \
  --vpc-id vpc-12345
```

#### Using Vercel (Recommended for Next.js)

1. **Install Vercel CLI**
```bash
npm i -g vercel
```

2. **Deploy**
```bash
# Login to Vercel
vercel login

# Deploy to production
vercel --prod
```

3. **Configure Environment Variables**
```bash
# Set production environment variables
vercel env add NEXT_PUBLIC_API_URL production
vercel env add SENTRY_DSN production
```

### Google Cloud Platform

#### Using Compute Engine

1. **Create VM Instance**
```bash
# Create Ubuntu 24.04 LTS instance
gcloud compute instances create yieldsight-frontend \
  --image-family ubuntu-2404-lts \
  --image-project ubuntu-os-cloud \
  --machine-type e2-medium \
  --zone us-central1-a \
  --tags http-server,https-server
```

2. **Deploy Application**
```bash
# Connect to instance
gcloud compute ssh yieldsight-frontend --zone us-central1-a

# Follow self-hosted deployment steps
# Install Node.js, PM2, Nginx, and deploy application
```

## 🔄 CI/CD Pipeline

### GitHub Actions Deployment

The repository includes automated CI/CD pipelines:

1. **Continuous Integration** (`.github/workflows/ci.yml`)
   - Runs on every push and PR
   - Executes tests, linting, and security scans
   - Builds application and runs E2E tests

2. **Continuous Deployment** (`.github/workflows/deploy.yml`)
   - Deploys to staging on `dev` branch
   - Deploys to production on `main` branch
   - Includes rollback capabilities

### Manual Deployment

```bash
# Deploy to staging
git push origin dev

# Deploy to production
git push origin main

# Manual deployment trigger
gh workflow run deploy.yml -f environment=production
```

## 📊 Monitoring Setup

### 1. Application Monitoring

**Sentry Configuration:**
```javascript
// Already configured in src/lib/monitoring.ts
// Ensure SENTRY_DSN is set in environment variables
```

**Health Checks:**
- Endpoint: `/api/health`
- Monitors: Database, API, external services
- Frequency: Every 30 seconds

### 2. Performance Monitoring

**Lighthouse CI:**
- Automated performance testing
- Runs on every deployment
- Alerts on performance regression

**Real User Monitoring:**
- Google Analytics integration
- Core Web Vitals tracking
- User experience metrics

### 3. Log Aggregation

**Structured Logging:**
- JSON format for production
- Multiple log levels (error, warn, info, debug)
- Automatic error reporting to Sentry

## 🔒 Security Configuration

### 1. Security Headers

Already configured in `next.config.js`:
- Content Security Policy (CSP)
- X-Frame-Options
- X-Content-Type-Options
- Referrer-Policy
- Permissions-Policy

### 2. HTTPS Configuration

```nginx
# Nginx configuration example
server {
    listen 443 ssl http2;
    server_name yieldsight.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    # Security headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 3. Rate Limiting

Configure rate limiting at the reverse proxy level:
```nginx
# Rate limiting configuration
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;

location /api/ {
    limit_req zone=api burst=20 nodelay;
}

location /api/auth/login {
    limit_req zone=login burst=5 nodelay;
}
```

## 🔧 Performance Optimization

### 1. CDN Configuration

**CloudFlare Setup:**
- Enable caching for static assets
- Configure cache rules for API responses
- Enable Brotli compression
- Set up geographic distribution

**Cache Headers:**
```nginx
# Static assets caching
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}

# API responses
location /api/ {
    add_header Cache-Control "no-cache, must-revalidate";
}
```

### 2. Database Optimization

- Enable connection pooling
- Configure read replicas for analytics
- Set up proper indexing
- Monitor query performance

## 🚨 Troubleshooting

### Common Issues

1. **Build Failures**
   ```bash
   # Clear cache and rebuild
   rm -rf .next node_modules
   npm install
   npm run build
   ```

2. **Environment Variable Issues**
   ```bash
   # Verify environment variables
   npm run env:check
   ```

3. **Performance Issues**
   ```bash
   # Analyze bundle
   npm run analyze
   
   # Check memory usage
   node --max-old-space-size=4096 node_modules/.bin/next build
   ```

### Health Check Endpoints

- **Application Health**: `/api/health`
- **Database Health**: `/api/health/db`
- **External Services**: `/api/health/external`

### Log Locations

- **Application Logs**: `/var/log/yieldsight/app.log`
- **Error Logs**: `/var/log/yieldsight/error.log`
- **Access Logs**: `/var/log/nginx/access.log`

## 📞 Support

For deployment support and issues:
- **Documentation**: [Internal Wiki]
- **Support Email**: <EMAIL>
- **Emergency Contact**: +1-XXX-XXX-XXXX
- **Slack Channel**: #deployment-support

## ✅ Post-Deployment Checklist

- [ ] Application starts successfully
- [ ] Health checks pass
- [ ] SSL certificate valid
- [ ] Environment variables configured
- [ ] Monitoring alerts configured
- [ ] Backup procedures tested
- [ ] Performance metrics within targets
- [ ] Security scans completed
- [ ] User acceptance testing passed
- [ ] Documentation updated

---

**Deployment Status: PRODUCTION READY** ✅  
**Last Updated**: 2024-01-15  
**Version**: 1.0.0
