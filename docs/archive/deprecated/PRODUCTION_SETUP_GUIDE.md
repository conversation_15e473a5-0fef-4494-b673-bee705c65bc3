# Soil Master v1.0.3 - Complete Production Setup Guide

## Business Impact Demonstration System - Production Deployment

This guide provides comprehensive instructions for setting up the complete Soil Master v1.0.3 system on Ubuntu 24.04 LTS with all business impact demonstration features.

## 🎯 **System Overview**

Soil Master v1.0.3 is an enterprise-grade precision agriculture platform featuring:
- **Real-time ROI Calculator** with visual projections
- **Cost Savings Dashboard** with executive-level metrics
- **Before/After Scenario** demonstration tools
- **Professional Financial Reporting** system
- **Demo-ready Business Cases** for stakeholder presentations

## 📋 **System Requirements**

### **Operating System**
- **Ubuntu Server 24.04 LTS** (Required)
- Minimum 4 CPU cores, 8GB RAM, 100GB storage
- Recommended: 8 CPU cores, 16GB RAM, 500GB SSD

### **Software Dependencies**
- **Node.js v24.x** (Latest LTS)
- **Python 3.12+** with pip and venv
- **PostgreSQL 17.x** with extensions
- **Redis 7.x** for caching
- **Nginx** for web server
- **PM2** for process management

### **Database Extensions Required**
- **TimescaleDB 2.20+** for time-series data
- **PostGIS 3.5+** for geospatial data
- **Apache AGE 1.5+** for graph database capabilities

## 🚀 **Quick Start (Automated Setup)**

For rapid deployment, use our automated setup script:

```bash
# Clone the repository
git clone https://github.com/Yield-Sight-System/soil-master.git
cd soil-master

# Run automated setup (Ubuntu 24.04 LTS only)
sudo chmod +x setup.sh
sudo ./setup.sh

# Follow the interactive prompts for configuration
```

The automated setup will:
1. Install all system dependencies
2. Configure PostgreSQL with required extensions
3. Set up Redis caching
4. Install and configure all services
5. Create production-ready configuration
6. Start all services with PM2

## 📖 **Manual Setup Instructions**

### **Step 1: System Preparation**

```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install essential packages
sudo apt install -y curl wget git build-essential software-properties-common

# Install Node.js v24 (Latest LTS)
curl -fsSL https://deb.nodesource.com/setup_24.x | sudo -E bash -
sudo apt install -y nodejs

# Verify Node.js installation
node --version  # Should show v24.x.x
npm --version   # Should show 10.x.x or higher
```

### **Step 2: PostgreSQL 17 Installation with Extensions**

```bash
# Add PostgreSQL official repository
sudo sh -c 'echo "deb http://apt.postgresql.org/pub/repos/apt $(lsb_release -cs)-pgdg main" > /etc/apt/sources.list.d/pgdg.list'
wget --quiet -O - https://www.postgresql.org/media/keys/ACCC4CF8.asc | sudo apt-key add -
sudo apt update

# Install PostgreSQL 17
sudo apt install -y postgresql-17 postgresql-client-17 postgresql-contrib-17

# Install PostGIS extension
sudo apt install -y postgresql-17-postgis-3

# Install TimescaleDB extension
echo "deb https://packagecloud.io/timescale/timescaledb/ubuntu/ $(lsb_release -c -s) main" | sudo tee /etc/apt/sources.list.d/timescaledb.list
wget --quiet -O - https://packagecloud.io/timescale/timescaledb/gpgkey | sudo apt-key add -
sudo apt update
sudo apt install -y timescaledb-2-postgresql-17

# Install Apache AGE extension
sudo apt install -y postgresql-17-age

# Configure PostgreSQL
sudo systemctl start postgresql
sudo systemctl enable postgresql

# Tune TimescaleDB
sudo timescaledb-tune --quiet --yes
sudo systemctl restart postgresql
```

### **Step 3: Database Setup**

```bash
# Switch to postgres user
sudo -u postgres psql

-- Create database and user
CREATE DATABASE soilmaster_db;
CREATE USER soilmaster_user WITH ENCRYPTED PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE soilmaster_db TO soilmaster_user;

-- Connect to the database
\c soilmaster_db

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS timescaledb;
CREATE EXTENSION IF NOT EXISTS postgis;
CREATE EXTENSION IF NOT EXISTS age;

-- Load AGE extension
LOAD 'age';
SET search_path = ag_catalog, "$user", public;

-- Create AGE graph for relationships
SELECT create_graph('soil_relationships');

-- Exit PostgreSQL
\q
```

### **Step 4: Redis Installation**

```bash
# Install Redis
sudo apt install -y redis-server

# Configure Redis for production
sudo nano /etc/redis/redis.conf

# Update these settings:
# maxmemory 2gb
# maxmemory-policy allkeys-lru
# save 900 1
# save 300 10
# save 60 10000

# Start and enable Redis
sudo systemctl start redis-server
sudo systemctl enable redis-server

# Test Redis connection
redis-cli ping  # Should return PONG
```

### **Step 5: Python Environment Setup**

```bash
# Install Python 3.12 and dependencies
sudo apt install -y python3.12 python3.12-venv python3.12-dev python3-pip

# Create Python virtual environment for soil-ai
cd soil-ai
python3.12 -m venv venv
source venv/bin/activate

# Install Python dependencies
pip install --upgrade pip
pip install -r requirements-production.txt

# Verify installation
python -c "import torch; print(f'PyTorch version: {torch.__version__}')"
python -c "import xgboost; print(f'XGBoost version: {xgboost.__version__}')"

deactivate
cd ..
```

### **Step 6: Environment Configuration**

```bash
# Copy environment template
cp .env.example .env

# Edit environment configuration
nano .env

# Update these critical settings:
# - Database connection details
# - JWT secret keys (generate with: openssl rand -hex 64)
# - Redis password
# - API keys for external services
# - Domain names and SSL settings
```

**Critical Environment Variables to Update:**
```bash
# Database
DB_PASSWORD=your_secure_database_password

# Security
JWT_SECRET_KEY=your_64_character_jwt_secret_key
JWT_REFRESH_SECRET_KEY=your_64_character_refresh_secret_key

# Redis
REDIS_PASSWORD=your_secure_redis_password

# Domain
API_BASE_URL=https://api.yourdomain.com
CORS_ORIGINS=https://yourdomain.com,https://app.yourdomain.com
```

### **Step 7: Backend Service Setup**

```bash
# Navigate to backend directory
cd soil-backend

# Install Node.js dependencies
npm install --production

# Run database migrations
npm run migrate

# Verify backend configuration
npm run check-config

# Test backend startup
npm run start:dev

# Stop test server (Ctrl+C)
cd ..
```

### **Step 8: Frontend Service Setup**

```bash
# Navigate to frontend directory
cd soil-frontend

# Install Node.js dependencies
npm install --production

# Build production frontend
npm run build

# Test frontend build
npm run start

# Stop test server (Ctrl+C)
cd ..
```

### **Step 9: Soil-AI Service Setup**

```bash
# Navigate to soil-ai directory
cd soil-ai

# Activate Python environment
source venv/bin/activate

# Test soil-ai service
python -m soil_ai.main --test

# Verify GPU support (if available)
python -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}')"

deactivate
cd ..
```

### **Step 10: Database Migration Execution**

```bash
# Navigate to backend directory
cd soil-backend

# Check current migration status
npm run migrate:status

# Run all migrations including new ROI tables
npm run migrate

# Verify ROI tables were created
npm run db:verify-roi-tables

# Load sample ROI data (optional for demo)
npm run db:seed-roi-demo-data

cd ..
```

### **Step 11: Process Management with PM2**

```bash
# Install PM2 globally
sudo npm install -g pm2

# Create PM2 ecosystem configuration
cp ecosystem.config.js.example ecosystem.config.js

# Edit PM2 configuration if needed
nano ecosystem.config.js

# Start all services with PM2
pm2 start ecosystem.config.js

# Save PM2 configuration
pm2 save

# Setup PM2 startup script
pm2 startup
# Follow the instructions provided by the command

# Check service status
pm2 status
pm2 logs
```

### **Step 12: Nginx Configuration**

```bash
# Install Nginx
sudo apt install -y nginx

# Create Nginx configuration
sudo nano /etc/nginx/sites-available/soilmaster

# Add the following configuration:
```

```nginx
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    ssl_certificate /etc/ssl/certs/soilmaster.crt;
    ssl_certificate_key /etc/ssl/private/soilmaster.key;

    # Frontend (Next.js)
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Backend API
    location /api/ {
        proxy_pass http://localhost:8000;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Soil-AI Service
    location /ai/ {
        proxy_pass http://localhost:8001;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

```bash
# Enable the site
sudo ln -s /etc/nginx/sites-available/soilmaster /etc/nginx/sites-enabled/
sudo rm /etc/nginx/sites-enabled/default

# Test Nginx configuration
sudo nginx -t

# Start and enable Nginx
sudo systemctl start nginx
sudo systemctl enable nginx
```

### **Step 13: SSL Certificate Setup**

```bash
# Install Certbot for Let's Encrypt
sudo apt install -y certbot python3-certbot-nginx

# Obtain SSL certificate
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# Test automatic renewal
sudo certbot renew --dry-run
```

## ✅ **Service Verification**

### **Check All Services**

```bash
# Check PM2 services
pm2 status

# Check system services
sudo systemctl status postgresql
sudo systemctl status redis-server
sudo systemctl status nginx

# Check service logs
pm2 logs
sudo journalctl -u postgresql -f
sudo journalctl -u nginx -f
```

### **Health Check Endpoints**

```bash
# Backend health check
curl http://localhost:8000/health

# Frontend health check
curl http://localhost:3000/api/health

# Soil-AI health check
curl http://localhost:8001/health

# Full system health check
curl https://yourdomain.com/api/health
```

### **ROI Calculation Test**

```bash
# Test ROI calculation endpoint
curl -X POST https://yourdomain.com/api/v1/roi/calculate \
  -H "Content-Type: application/json" \
  -d '{
    "estate_size_hectares": 1000,
    "crop_type": "palm_oil",
    "region": "malaysia",
    "current_yield_per_hectare": 20.5,
    "expected_yield_improvement_percent": 25,
    "technology_investment": 150000
  }'
```

### **Database Verification**

```bash
# Check database connection
sudo -u postgres psql soilmaster_db -c "SELECT version();"

# Verify ROI tables exist
sudo -u postgres psql soilmaster_db -c "\dt roi_*"

# Check TimescaleDB extension
sudo -u postgres psql soilmaster_db -c "SELECT * FROM pg_extension WHERE extname='timescaledb';"

# Check PostGIS extension
sudo -u postgres psql soilmaster_db -c "SELECT PostGIS_version();"

# Check Apache AGE extension
sudo -u postgres psql soilmaster_db -c "SELECT * FROM ag_graph WHERE name='soil_relationships';"
```

## 🔧 **Service Management Commands**

### **Start Services**
```bash
# Start all services
pm2 start ecosystem.config.js

# Start individual services
pm2 start soil-backend
pm2 start soil-frontend
pm2 start soil-ai
```

### **Stop Services**
```bash
# Stop all services
pm2 stop all

# Stop individual services
pm2 stop soil-backend
pm2 stop soil-frontend
pm2 stop soil-ai
```

### **Restart Services**
```bash
# Restart all services
pm2 restart all

# Restart individual services
pm2 restart soil-backend
pm2 restart soil-frontend
pm2 restart soil-ai
```

### **Monitor Services**
```bash
# Real-time monitoring
pm2 monit

# View logs
pm2 logs
pm2 logs soil-backend
pm2 logs soil-frontend
pm2 logs soil-ai

# View service details
pm2 show soil-backend
```

## 🔍 **Troubleshooting**

### **Common Issues**

#### **Database Connection Issues**
```bash
# Check PostgreSQL status
sudo systemctl status postgresql

# Check database connectivity
sudo -u postgres psql -c "SELECT version();"

# Check database extensions
sudo -u postgres psql soilmaster_db -c "SELECT * FROM pg_extension;"

# Check database permissions
sudo -u postgres psql soilmaster_db -c "SELECT * FROM information_schema.role_table_grants WHERE grantee='soilmaster_user';"
```

#### **Redis Connection Issues**
```bash
# Check Redis status
sudo systemctl status redis-server

# Test Redis connectivity
redis-cli ping

# Check Redis configuration
redis-cli config get "*"

# Check Redis memory usage
redis-cli info memory
```

#### **Service Startup Issues**
```bash
# Check PM2 logs for errors
pm2 logs --err

# Check system logs
sudo journalctl -xe

# Check port availability
sudo netstat -tlnp | grep :3000
sudo netstat -tlnp | grep :8000
sudo netstat -tlnp | grep :8001

# Check disk space
df -h

# Check memory usage
free -h
```

#### **Frontend Build Issues**
```bash
# Clear Node.js cache
npm cache clean --force

# Reinstall dependencies
rm -rf node_modules package-lock.json
npm install

# Check Node.js version
node --version  # Should be v24.x.x

# Check build logs
npm run build 2>&1 | tee build.log
```

#### **Backend Migration Issues**
```bash
# Check migration status
cd soil-backend
npm run migrate:status

# Check database connection
npm run db:check

# Reset migrations (CAUTION: This will drop all data)
npm run migrate:reset
npm run migrate

# Check migration logs
npm run migrate 2>&1 | tee migration.log
```

#### **Soil-AI Service Issues**
```bash
# Check Python environment
cd soil-ai
source venv/bin/activate
python --version  # Should be 3.12+

# Check Python dependencies
pip list | grep -E "(torch|xgboost|scikit-learn)"

# Test GPU availability
python -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}')"

# Check model files
ls -la soil_ai/models/

# Test prediction service
python -m soil_ai.main --test-prediction
```

### **Performance Optimization**

#### **Database Optimization**
```bash
# Analyze database performance
sudo -u postgres psql soilmaster_db -c "SELECT * FROM pg_stat_activity;"

# Update database statistics
sudo -u postgres psql soilmaster_db -c "ANALYZE;"

# Check slow queries
sudo -u postgres psql soilmaster_db -c "SELECT query, mean_time, calls FROM pg_stat_statements ORDER BY mean_time DESC LIMIT 10;"

# Optimize TimescaleDB
sudo -u postgres psql soilmaster_db -c "SELECT add_compression_policy('sensor_data', INTERVAL '7 days');"
```

#### **Redis Optimization**
```bash
# Check Redis memory usage
redis-cli info memory

# Check Redis performance
redis-cli info stats

# Monitor Redis operations
redis-cli monitor

# Optimize Redis configuration
redis-cli config set maxmemory-policy allkeys-lru
```

#### **Application Optimization**
```bash
# Check application memory usage
pm2 show soil-backend | grep memory
pm2 show soil-frontend | grep memory
pm2 show soil-ai | grep memory

# Monitor CPU usage
top -p $(pgrep -f "soil-")

# Check network connections
ss -tulpn | grep -E "(3000|8000|8001)"
```
