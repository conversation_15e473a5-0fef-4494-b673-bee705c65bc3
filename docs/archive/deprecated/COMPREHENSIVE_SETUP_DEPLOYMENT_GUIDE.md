# Yield Sight System - Comprehensive Setup & Deployment Guide

## 🎯 Overview

This guide provides enterprise-grade setup and deployment instructions for the Yield Sight System (soil-master project) on Ubuntu Server 24.04 LTS. The documentation follows zero-defect production readiness requirements with comprehensive testing strategies, security hardening, and systematic deployment procedures.

## 📋 System Requirements & Prerequisites

### Hardware Requirements (Minimum)
- **CPU**: 4 cores (8 cores recommended for production)
- **RAM**: 8GB minimum (16GB+ recommended for production)
- **Storage**: 50GB SSD minimum (100GB+ recommended)
- **Network**: 1Gbps network interface
- **Architecture**: x86_64 (AMD64)

### Hardware Requirements (Recommended Production)
- **CPU**: 8+ cores with AVX2 support for ML workloads
- **RAM**: 32GB+ for optimal ML model performance
- **Storage**: 200GB+ NVMe SSD with backup storage
- **Network**: Redundant network interfaces
- **GPU**: NVIDIA GPU with CUDA support (optional, for ML acceleration)

### Operating System Requirements
- **OS**: Ubuntu Server 24.04 LTS (Noble Numbat)
- **Kernel**: Linux 6.8+ 
- **Architecture**: x86_64 only
- **Updates**: Latest security patches applied
- **Locale**: UTF-8 encoding support

### Required System Packages
```bash
# Essential build tools and libraries
build-essential
curl
wget
git
unzip
software-properties-common
apt-transport-https
ca-certificates
gnupg
lsb-release

# Python development dependencies
python3.11
python3.11-dev
python3.11-venv
python3-pip
libpq-dev
libgdal-dev

# Poetry (Python package manager)
curl -sSL https://install.python-poetry.org | python3 -
libproj-dev
libgeos-dev

# Node.js and npm (will be installed via NodeSource)
# PostgreSQL and extensions (will be installed separately)
# Redis server
# Nginx web server
# PM2 process manager
```

### Software Version Requirements
- **Python**: 3.11.x (exact version for compatibility)
- **Node.js**: 18.x LTS or 20.x LTS
- **npm**: 9.x or 10.x
- **PostgreSQL**: 17.5+ with extensions:
  - TimescaleDB 2.20+
  - PostGIS 3.5.3+
  - pgvector 0.7.0+
- **Redis**: 6.0+ (7.0+ recommended)
- **Nginx**: 1.24+ (latest stable)
- **PM2**: Latest stable version

## 🔧 Initial Environment Setup

### Phase 1: System Preparation

#### 1.1 Update System and Install Base Packages
```bash
# Update package lists and upgrade system
sudo apt update && sudo apt upgrade -y

# Install essential packages
sudo apt install -y \
    build-essential \
    curl \
    wget \
    git \
    unzip \
    software-properties-common \
    apt-transport-https \
    ca-certificates \
    gnupg \
    lsb-release \
    ufw \
    fail2ban \
    htop \
    tree \
    vim

# Install Python 3.11 and development tools
sudo apt install -y \
    python3.11 \
    python3.11-dev \
    python3.11-venv \
    python3-pip \
    libpq-dev \
    libgdal-dev \
    libproj-dev \
    libgeos-dev \
    libspatialindex-dev \
    libssl-dev \
    libffi-dev

# Verify Python installation
python3.11 --version
```

#### 1.2 Install Node.js 20.x LTS
```bash
# Add NodeSource repository for Node.js 20.x LTS
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -

# Install Node.js
sudo apt install -y nodejs

# Verify installation
node --version  # Should show v20.x.x
npm --version   # Should show 10.x.x

# Install global packages
sudo npm install -g pm2@latest
sudo npm install -g @nestjs/cli
```

#### 1.3 Install and Configure PostgreSQL 17.5+
```bash
# Add PostgreSQL official repository
curl -fsSL https://www.postgresql.org/media/keys/ACCC4CF8.asc | \
    sudo gpg --dearmor -o /etc/apt/trusted.gpg.d/postgresql.gpg

echo "deb http://apt.postgresql.org/pub/repos/apt/ $(lsb_release -cs)-pgdg main" | \
    sudo tee /etc/apt/sources.list.d/pgdg.list

# Add TimescaleDB repository
curl -fsSL https://packagecloud.io/timescale/timescaledb/gpgkey | \
    sudo gpg --dearmor -o /etc/apt/trusted.gpg.d/timescaledb.gpg

echo "deb https://packagecloud.io/timescale/timescaledb/ubuntu/ $(lsb_release -cs) main" | \
    sudo tee /etc/apt/sources.list.d/timescaledb.list

# Update package lists
sudo apt update

# Install PostgreSQL with extensions
sudo apt install -y \
    postgresql-17 \
    postgresql-contrib-17 \
    postgresql-17-postgis-3 \
    timescaledb-2-postgresql-17 \
    postgresql-17-pgvector

# Enable and start PostgreSQL
sudo systemctl enable postgresql
sudo systemctl start postgresql

# Verify installation
sudo systemctl status postgresql
```

#### 1.4 Install and Configure Redis
```bash
# Install Redis
sudo apt install -y redis-server

# Configure Redis for production
sudo cp /etc/redis/redis.conf /etc/redis/redis.conf.backup

# Edit Redis configuration
sudo tee /etc/redis/redis.conf > /dev/null << 'EOF'
# Redis production configuration
bind 127.0.0.1
port 6379
timeout 300
tcp-keepalive 60
daemonize yes
supervised systemd
pidfile /var/run/redis/redis-server.pid
loglevel notice
logfile /var/log/redis/redis-server.log
databases 16
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir /var/lib/redis
maxmemory 1gb
maxmemory-policy allkeys-lru
EOF

# Enable and start Redis
sudo systemctl enable redis-server
sudo systemctl start redis-server

# Verify installation
sudo systemctl status redis-server
redis-cli ping  # Should return PONG
```

#### 1.5 Install and Configure Nginx
```bash
# Install Nginx
sudo apt install -y nginx

# Enable and start Nginx
sudo systemctl enable nginx
sudo systemctl start nginx

# Verify installation
sudo systemctl status nginx
curl -I http://localhost  # Should return 200 OK
```

### Phase 2: Security Configuration

#### 2.1 Configure Firewall (UFW)
```bash
# Reset UFW to defaults
sudo ufw --force reset

# Set default policies
sudo ufw default deny incoming
sudo ufw default allow outgoing

# Allow SSH (adjust port if needed)
sudo ufw allow 22/tcp

# Allow HTTP and HTTPS
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# Allow specific application ports (adjust as needed)
sudo ufw allow 8000/tcp  # Backend API
sudo ufw allow 3000/tcp  # Frontend (development)
sudo ufw allow 8001/tcp  # AI Service

# Enable firewall
sudo ufw enable

# Verify status
sudo ufw status verbose
```

#### 2.2 Configure Fail2Ban
```bash
# Create custom jail configuration
sudo tee /etc/fail2ban/jail.local > /dev/null << 'EOF'
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 3
backend = systemd

[sshd]
enabled = true
port = ssh
filter = sshd
logpath = /var/log/auth.log
maxretry = 3

[nginx-http-auth]
enabled = true
filter = nginx-http-auth
port = http,https
logpath = /var/log/nginx/error.log

[nginx-limit-req]
enabled = true
filter = nginx-limit-req
port = http,https
logpath = /var/log/nginx/error.log
maxretry = 10
EOF

# Enable and start Fail2Ban
sudo systemctl enable fail2ban
sudo systemctl start fail2ban

# Verify status
sudo fail2ban-client status
```

### Phase 3: Database Setup and Configuration

#### 3.1 Configure PostgreSQL for Production
```bash
# Tune PostgreSQL for TimescaleDB
sudo timescaledb-tune --quiet --yes

# Create application database and user
sudo -u postgres psql << 'EOF'
-- Create application user with secure password
CREATE USER yieldsight_user WITH PASSWORD 'CHANGE_THIS_SECURE_PASSWORD_IN_PRODUCTION';

-- Create main database
CREATE DATABASE yieldsight_soil_db OWNER yieldsight_user;

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE yieldsight_soil_db TO yieldsight_user;

-- Connect to database and enable extensions
\c yieldsight_soil_db

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS timescaledb;
CREATE EXTENSION IF NOT EXISTS postgis;
CREATE EXTENSION IF NOT EXISTS pgvector;
CREATE EXTENSION IF NOT EXISTS pg_stat_statements;
CREATE EXTENSION IF NOT EXISTS btree_gin;
CREATE EXTENSION IF NOT EXISTS btree_gist;

-- Grant schema permissions
GRANT ALL ON SCHEMA public TO yieldsight_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO yieldsight_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO yieldsight_user;

-- Exit psql
\q
EOF

# Verify database setup
sudo -u postgres psql -d yieldsight_soil_db -c "\dx"  # List extensions
```

#### 3.2 Configure PostgreSQL Security
```bash
# Backup original configuration
sudo cp /etc/postgresql/17/main/postgresql.conf /etc/postgresql/17/main/postgresql.conf.backup
sudo cp /etc/postgresql/17/main/pg_hba.conf /etc/postgresql/17/main/pg_hba.conf.backup

# Configure PostgreSQL for security and performance
sudo tee -a /etc/postgresql/17/main/postgresql.conf > /dev/null << 'EOF'

# Security settings
ssl = on
ssl_cert_file = '/etc/ssl/certs/ssl-cert-snakeoil.pem'
ssl_key_file = '/etc/ssl/private/ssl-cert-snakeoil.key'
password_encryption = scram-sha-256

# Performance settings
shared_preload_libraries = 'timescaledb,pg_stat_statements'
max_connections = 200
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100
random_page_cost = 1.1
effective_io_concurrency = 200

# Logging
log_destination = 'stderr'
logging_collector = on
log_directory = 'log'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_rotation_age = 1d
log_rotation_size = 100MB
log_min_duration_statement = 1000
log_checkpoints = on
log_connections = on
log_disconnections = on
log_lock_waits = on
EOF

# Restart PostgreSQL to apply changes
sudo systemctl restart postgresql

# Verify PostgreSQL is running
sudo systemctl status postgresql
```

## 🚀 Application Installation & Configuration

### Phase 4: Application Setup

#### 4.1 Create Application User and Directories
```bash
# Create application user
sudo useradd -r -m -s /bin/bash yieldsight

# Create application directories
sudo mkdir -p /opt/yieldsight/{soil-backend,soil-frontend,soil-ai}
sudo mkdir -p /var/log/yieldsight
sudo mkdir -p /etc/yieldsight
sudo mkdir -p /var/lib/yieldsight/{uploads,models,backups}

# Set ownership
sudo chown -R yieldsight:yieldsight /opt/yieldsight
sudo chown -R yieldsight:yieldsight /var/log/yieldsight
sudo chown -R yieldsight:yieldsight /var/lib/yieldsight

# Set permissions
sudo chmod 755 /opt/yieldsight
sudo chmod 755 /var/log/yieldsight
sudo chmod 750 /etc/yieldsight
sudo chmod 755 /var/lib/yieldsight
```

#### 4.2 Clone Repository and Setup Project Structure
```bash
# Switch to application user
sudo -u yieldsight -i

# Clone the repository
cd /opt/yieldsight
git clone https://github.com/Yield-Sight-System/soil-master.git
cd soil-master

# Verify project structure
ls -la  # Should show soil-backend, soil-frontend, soil-ai directories

# Set up symbolic links for easier access
ln -sf /opt/yieldsight/soil-master/soil-backend /opt/yieldsight/soil-backend/current
ln -sf /opt/yieldsight/soil-master/soil-frontend /opt/yieldsight/soil-frontend/current
ln -sf /opt/yieldsight/soil-master/soil-ai /opt/yieldsight/soil-ai/current
```

### Phase 5: Backend Application Setup

#### 5.1 Setup Python Virtual Environment for Backend
```bash
# Navigate to backend directory
cd /opt/yieldsight/soil-master/soil-backend

# Create Python virtual environment
python3.11 -m venv venv

# Activate virtual environment
source venv/bin/activate

# Upgrade pip and install wheel
pip install --upgrade pip setuptools wheel

# Install production dependencies
pip install -r requirements.txt

# Verify installation
pip list | grep -E "(fastapi|sqlalchemy|uvicorn|redis|celery)"
```

#### 5.2 Configure Backend Environment Variables
```bash
# Create production environment file
sudo -u yieldsight tee /opt/yieldsight/soil-master/soil-backend/.env > /dev/null << 'EOF'
# =============================================================================
# PRODUCTION ENVIRONMENT CONFIGURATION - YIELD SIGHT SYSTEM SOIL BACKEND v1.0.1
# =============================================================================

# Application Environment
APP_ENV=production
APP_NAME="Yield Sight System Soil Backend"
APP_VERSION=1.0.1
APP_HOST=0.0.0.0
APP_PORT=8000
DEBUG=false
API_BASE_URL=https://api.yourdomain.com

# CORS Configuration (adjust domains for production)
CORS_ORIGINS=https://yourdomain.com,https://app.yourdomain.com

# Security Configuration
SECRET_KEY=CHANGE_THIS_TO_A_SECURE_64_CHARACTER_SECRET_KEY_IN_PRODUCTION
JWT_SECRET_KEY=CHANGE_THIS_TO_A_SECURE_JWT_SECRET_KEY_IN_PRODUCTION
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# Database Configuration
DATABASE_URL=postgresql+asyncpg://yieldsight_user:CHANGE_THIS_SECURE_PASSWORD_IN_PRODUCTION@localhost:5432/yieldsight_soil_db
DB_HOST=localhost
DB_PORT=5432
DB_NAME=yieldsight_soil_db
DB_USER=yieldsight_user
DB_PASSWORD=CHANGE_THIS_SECURE_PASSWORD_IN_PRODUCTION
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600

# Redis Configuration
REDIS_URL=redis://localhost:6379/0
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=
REDIS_SSL=false
REDIS_POOL_SIZE=10
REDIS_POOL_TIMEOUT=30

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60
RATE_LIMIT_STORAGE=redis

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2
CELERY_TASK_SERIALIZER=json
CELERY_RESULT_SERIALIZER=json
CELERY_ACCEPT_CONTENT=json
CELERY_TIMEZONE=UTC
CELERY_ENABLE_UTC=true

# OpenAI Configuration
OPENAI_API_KEY=CHANGE_THIS_TO_YOUR_OPENAI_API_KEY
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=2000
OPENAI_TEMPERATURE=0.7

# Mem0 AI Configuration
MEM0_API_KEY=CHANGE_THIS_TO_YOUR_MEM0_API_KEY
MEM0_USER_ID=soil_backend_user
MEM0_CONFIG_PATH=/etc/yieldsight/mem0_config.yaml

# File Storage Configuration
UPLOAD_DIR=/var/lib/yieldsight/uploads
MAX_UPLOAD_SIZE=10485760
ALLOWED_FILE_TYPES=csv,json,pdf,png,jpg,jpeg
FILE_CLEANUP_ENABLED=true
FILE_RETENTION_DAYS=30

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_FILE=/var/log/yieldsight/backend.log
LOG_MAX_SIZE=100MB
LOG_BACKUP_COUNT=5
LOG_ROTATION=daily

# Monitoring Configuration
METRICS_ENABLED=true
METRICS_PORT=9090
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_INTERVAL=30

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USE_TLS=true
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
FROM_EMAIL=<EMAIL>
FROM_NAME="Yield Sight System"

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE="0 2 * * *"
BACKUP_RETENTION_DAYS=30
BACKUP_STORAGE_PATH=/var/lib/yieldsight/backups
EOF

# Set secure permissions
sudo chmod 600 /opt/yieldsight/soil-master/soil-backend/.env
sudo chown yieldsight:yieldsight /opt/yieldsight/soil-master/soil-backend/.env
```

#### 5.3 Setup Database Schema and Migrations
```bash
# Navigate to backend directory and activate virtual environment
cd /opt/yieldsight/soil-master/soil-backend
source venv/bin/activate

# Run database migrations
alembic upgrade head

# Verify database schema
python -c "
from app.database.connection import get_database_url
from sqlalchemy import create_engine, text
engine = create_engine(get_database_url().replace('+asyncpg', ''))
with engine.connect() as conn:
    result = conn.execute(text('SELECT tablename FROM pg_tables WHERE schemaname = \\'public\\';'))
    tables = [row[0] for row in result]
    print(f'Created tables: {tables}')
"

# Create initial admin user (optional)
python -c "
import asyncio
from app.core.security import get_password_hash
from app.database.connection import get_async_session
from app.models.user import User
from sqlalchemy.ext.asyncio import AsyncSession

async def create_admin():
    async for session in get_async_session():
        admin_user = User(
            email='<EMAIL>',
            username='admin',
            full_name='System Administrator',
            hashed_password=get_password_hash('CHANGE_THIS_ADMIN_PASSWORD'),
            is_active=True,
            is_superuser=True
        )
        session.add(admin_user)
        await session.commit()
        print('Admin user created successfully')
        break

asyncio.run(create_admin())
"
```

### Phase 6: AI Service Setup

#### 6.1 Setup Python Virtual Environment for AI Service
```bash
# Navigate to AI service directory
cd /opt/yieldsight/soil-master/soil-ai

# Create Python virtual environment
python3.11 -m venv venv

# Activate virtual environment
source venv/bin/activate

# Install production dependencies
pip install --upgrade pip setuptools wheel
pip install -r requirements-production.txt

# Verify ML libraries installation
python -c "
import numpy as np
import pandas as pd
import sklearn
import xgboost as xgb
import torch
print('NumPy version:', np.__version__)
print('Pandas version:', pd.__version__)
print('Scikit-learn version:', sklearn.__version__)
print('XGBoost version:', xgb.__version__)
print('PyTorch version:', torch.__version__)
print('CUDA available:', torch.cuda.is_available())
"
```

#### 6.2 Configure AI Service Environment Variables
```bash
# Create AI service environment file
sudo -u yieldsight tee /opt/yieldsight/soil-master/soil-ai/.env > /dev/null << 'EOF'
# =============================================================================
# SOIL AI/ML ENGINE v1.0.1 - PRODUCTION ENVIRONMENT CONFIGURATION
# =============================================================================

# Application Settings
ENVIRONMENT=production
APP_NAME=soil-ai
APP_VERSION=1.0.1
DEBUG=false

# Server Configuration
HOST=127.0.0.1
PORT=8001
WORKERS=4
WORKER_CLASS=uvicorn.workers.UvicornWorker
WORKER_CONNECTIONS=1000
MAX_REQUESTS=1000
MAX_REQUESTS_JITTER=100
TIMEOUT=30
KEEPALIVE=2

# Database Configuration
DATABASE_URL=postgresql+asyncpg://yieldsight_user:CHANGE_THIS_SECURE_PASSWORD_IN_PRODUCTION@localhost:5432/yieldsight_soil_db

# Model Configuration
MODEL_BASE_PATH=/var/lib/yieldsight/models
ENSEMBLE_MODEL_PATH=${MODEL_BASE_PATH}/ensemble_model.pkl
XGBOOST_MODEL_PATH=${MODEL_BASE_PATH}/xgboost_model.pkl
KRIGING_MODEL_PATH=${MODEL_BASE_PATH}/kriging_model.pkl
CORRECTION_MODEL_PATH=${MODEL_BASE_PATH}/correction_model.pkl
SHAP_EXPLAINER_PATH=${MODEL_BASE_PATH}/shap_explainer.pkl

# XGBoost GPU/CPU Configuration
XGBOOST_DEVICE=auto
XGBOOST_TREE_METHOD=hist
XGBOOST_GPU_ID=0
XGBOOST_N_JOBS=-1

# Model Training Settings
MODEL_TRAINING_ENABLED=true
AUTO_RETRAIN_ENABLED=false
RETRAIN_SCHEDULE=0 2 * * *
MIN_TRAINING_SAMPLES=1000
VALIDATION_SPLIT=0.2
CROSS_VALIDATION_FOLDS=5
HYPERPARAMETER_TUNING_ENABLED=true
OPTUNA_N_TRIALS=100

# Prediction Settings
PREDICTION_CACHE_ENABLED=true
PREDICTION_CACHE_TTL=3600
REDIS_URL=redis://localhost:6379/3
MAX_BATCH_SIZE=100
MAX_SPATIAL_LOCATIONS=50

# Sensor Correction Settings
ANOMALY_DETECTION_ENABLED=true
ISOLATION_FOREST_CONTAMINATION=0.1
ISOLATION_FOREST_N_ESTIMATORS=100
CORRECTION_CONFIDENCE_THRESHOLD=0.8
MAX_CORRECTION_PERCENTAGE=50.0
TEMPORAL_VALIDATION_ENABLED=true
TEMPORAL_WINDOW_HOURS=24
MAX_TEMPORAL_DEVIATION=3.0

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_FILE=/var/log/yieldsight/ai-service.log
LOG_MAX_SIZE=100MB
LOG_BACKUP_COUNT=5

# Security Configuration
API_KEY_HEADER=X-API-Key
ALLOWED_HOSTS=localhost,127.0.0.1,api.yourdomain.com
CORS_ORIGINS=https://yourdomain.com,https://app.yourdomain.com
SECURITY_HEADERS_ENABLED=true

# External Services
SOIL_BACKEND_URL=http://localhost:8000
SOIL_BACKEND_API_KEY=CHANGE_THIS_TO_BACKEND_API_KEY
SOIL_BACKEND_TIMEOUT=30
EOF

# Set secure permissions
sudo chmod 600 /opt/yieldsight/soil-master/soil-ai/.env
sudo chown yieldsight:yieldsight /opt/yieldsight/soil-master/soil-ai/.env
```

### Phase 7: Frontend Application Setup

#### 7.1 Install Frontend Dependencies
```bash
# Navigate to frontend directory
cd /opt/yieldsight/soil-master/soil-frontend

# Install Node.js dependencies
npm ci --production

# Verify installation
npm list --depth=0 | grep -E "(next|react|typescript)"

# Install PM2 globally if not already installed
sudo npm install -g pm2@latest
```

#### 7.2 Configure Frontend Environment Variables
```bash
# Create production environment file
sudo -u yieldsight tee /opt/yieldsight/soil-master/soil-frontend/.env.production > /dev/null << 'EOF'
# =============================================================================
# YIELD SIGHT SYSTEM FRONTEND v1.0.1 - PRODUCTION ENVIRONMENT
# =============================================================================

# Application Configuration
NODE_ENV=production
NEXT_PUBLIC_APP_ENV=production
NEXT_PUBLIC_APP_NAME="Yield Sight System"
NEXT_PUBLIC_APP_VERSION=1.0.1
NEXT_PUBLIC_APP_DESCRIPTION="Smart Agriculture Management System"

# Application URLs
NEXT_PUBLIC_APP_URL=https://yourdomain.com
NEXT_PUBLIC_API_URL=https://api.yourdomain.com/api
NEXT_PUBLIC_WS_URL=wss://api.yourdomain.com/ws

# Security Configuration
NEXTAUTH_URL=https://yourdomain.com
NEXTAUTH_SECRET=CHANGE_THIS_TO_A_SECURE_NEXTAUTH_SECRET_IN_PRODUCTION
JWT_SECRET=CHANGE_THIS_TO_A_SECURE_JWT_SECRET_IN_PRODUCTION

# Database Configuration (for NextAuth)
DATABASE_URL=postgresql://yieldsight_user:CHANGE_THIS_SECURE_PASSWORD_IN_PRODUCTION@localhost:5432/yieldsight_soil_db

# External API Keys
NEXT_PUBLIC_MAPS_API_KEY=CHANGE_THIS_TO_YOUR_GOOGLE_MAPS_API_KEY
WEATHER_API_KEY=CHANGE_THIS_TO_YOUR_WEATHER_API_KEY
WEATHER_API_PROVIDER=openweather
WEATHER_API_BASE_URL=https://api.openweathermap.org/data/2.5

# Maps Configuration
NEXT_PUBLIC_MAPS_PROVIDER=google
NEXT_PUBLIC_MAPS_STYLE=satellite
NEXT_PUBLIC_DEFAULT_MAP_CENTER_LAT=-1.2921
NEXT_PUBLIC_DEFAULT_MAP_CENTER_LNG=36.8219
NEXT_PUBLIC_DEFAULT_MAP_ZOOM=10
NEXT_PUBLIC_MAP_MAX_ZOOM=20
NEXT_PUBLIC_MAP_MIN_ZOOM=5

# Performance Configuration
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_PERFORMANCE_MONITORING=true

# Feature Flags
NEXT_PUBLIC_ENABLE_PWA=true
NEXT_PUBLIC_ENABLE_OFFLINE_MODE=true
NEXT_PUBLIC_ENABLE_PUSH_NOTIFICATIONS=true

# Monitoring and Analytics
SENTRY_DSN=CHANGE_THIS_TO_YOUR_SENTRY_DSN
SENTRY_ORG=your-sentry-org
SENTRY_PROJECT=yieldsight-frontend
SENTRY_AUTH_TOKEN=CHANGE_THIS_TO_YOUR_SENTRY_AUTH_TOKEN

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME="Yield Sight System"

# File Upload Configuration
NEXT_PUBLIC_MAX_FILE_SIZE=10485760
NEXT_PUBLIC_ALLOWED_FILE_TYPES=csv,json,pdf,png,jpg,jpeg

# Cache Configuration
NEXT_PUBLIC_CACHE_TTL=300
NEXT_PUBLIC_STATIC_CACHE_TTL=86400

# Security Headers
NEXT_PUBLIC_CSP_ENABLED=true
NEXT_PUBLIC_HSTS_ENABLED=true
NEXT_PUBLIC_HSTS_MAX_AGE=31536000
EOF

# Set secure permissions
sudo chmod 600 /opt/yieldsight/soil-master/soil-frontend/.env.production
sudo chown yieldsight:yieldsight /opt/yieldsight/soil-master/soil-frontend/.env.production
```

#### 7.3 Build Frontend for Production
```bash
# Navigate to frontend directory
cd /opt/yieldsight/soil-master/soil-frontend

# Build the application
npm run build

# Verify build output
ls -la .next/
ls -la .next/static/

# Test the production build locally (optional)
npm run start &
sleep 5
curl -I http://localhost:3000  # Should return 200 OK
pkill -f "next start"
```

## 🚀 Production Deployment Process

### Phase 8: PM2 Process Manager Setup

#### 8.1 Configure PM2 for Backend Service
```bash
# Create PM2 ecosystem configuration for backend
sudo -u yieldsight tee /opt/yieldsight/soil-master/soil-backend/ecosystem.config.js > /dev/null << 'EOF'
module.exports = {
  apps: [
    {
      name: 'yieldsight-backend',
      script: 'venv/bin/uvicorn',
      args: 'app.main:app --host 0.0.0.0 --port 8000 --workers 4',
      cwd: '/opt/yieldsight/soil-master/soil-backend',
      instances: 1,
      exec_mode: 'fork',
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      env: {
        NODE_ENV: 'production',
        PYTHONPATH: '/opt/yieldsight/soil-master/soil-backend'
      },
      env_production: {
        NODE_ENV: 'production',
        PYTHONPATH: '/opt/yieldsight/soil-master/soil-backend'
      },
      log_file: '/var/log/yieldsight/backend-combined.log',
      out_file: '/var/log/yieldsight/backend-out.log',
      error_file: '/var/log/yieldsight/backend-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      kill_timeout: 5000,
      listen_timeout: 10000,
      shutdown_with_message: true
    },
    {
      name: 'yieldsight-celery-worker',
      script: 'venv/bin/celery',
      args: '-A app.core.celery worker --loglevel=info --concurrency=4',
      cwd: '/opt/yieldsight/soil-master/soil-backend',
      instances: 1,
      exec_mode: 'fork',
      autorestart: true,
      watch: false,
      max_memory_restart: '512M',
      env: {
        NODE_ENV: 'production',
        PYTHONPATH: '/opt/yieldsight/soil-master/soil-backend'
      },
      log_file: '/var/log/yieldsight/celery-combined.log',
      out_file: '/var/log/yieldsight/celery-out.log',
      error_file: '/var/log/yieldsight/celery-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z'
    },
    {
      name: 'yieldsight-celery-beat',
      script: 'venv/bin/celery',
      args: '-A app.core.celery beat --loglevel=info',
      cwd: '/opt/yieldsight/soil-master/soil-backend',
      instances: 1,
      exec_mode: 'fork',
      autorestart: true,
      watch: false,
      env: {
        NODE_ENV: 'production',
        PYTHONPATH: '/opt/yieldsight/soil-master/soil-backend'
      },
      log_file: '/var/log/yieldsight/celery-beat-combined.log',
      out_file: '/var/log/yieldsight/celery-beat-out.log',
      error_file: '/var/log/yieldsight/celery-beat-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z'
    }
  ]
};
EOF
```

#### 8.2 Configure PM2 for AI Service
```bash
# Create PM2 ecosystem configuration for AI service
sudo -u yieldsight tee /opt/yieldsight/soil-master/soil-ai/ecosystem.config.js > /dev/null << 'EOF'
module.exports = {
  apps: [
    {
      name: 'yieldsight-ai',
      script: 'venv/bin/uvicorn',
      args: 'soil_ai.main:app --host 127.0.0.1 --port 8001 --workers 2',
      cwd: '/opt/yieldsight/soil-master/soil-ai',
      instances: 1,
      exec_mode: 'fork',
      autorestart: true,
      watch: false,
      max_memory_restart: '2G',
      env: {
        NODE_ENV: 'production',
        PYTHONPATH: '/opt/yieldsight/soil-master/soil-ai'
      },
      log_file: '/var/log/yieldsight/ai-combined.log',
      out_file: '/var/log/yieldsight/ai-out.log',
      error_file: '/var/log/yieldsight/ai-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      kill_timeout: 10000,
      listen_timeout: 15000
    }
  ]
};
EOF
```

#### 8.3 Configure PM2 for Frontend Service
```bash
# Create PM2 ecosystem configuration for frontend
sudo -u yieldsight tee /opt/yieldsight/soil-master/soil-frontend/ecosystem.config.js > /dev/null << 'EOF'
module.exports = {
  apps: [
    {
      name: 'yieldsight-frontend',
      script: 'npm',
      args: 'start',
      cwd: '/opt/yieldsight/soil-master/soil-frontend',
      instances: 1,
      exec_mode: 'fork',
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      env: {
        NODE_ENV: 'production',
        PORT: 3000
      },
      log_file: '/var/log/yieldsight/frontend-combined.log',
      out_file: '/var/log/yieldsight/frontend-out.log',
      error_file: '/var/log/yieldsight/frontend-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true
    }
  ]
};
EOF
```

### Phase 9: Nginx Reverse Proxy Configuration

#### 9.1 Create Nginx Configuration for Production
```bash
# Remove default Nginx configuration
sudo rm -f /etc/nginx/sites-enabled/default

# Create main application configuration
sudo tee /etc/nginx/sites-available/yieldsight > /dev/null << 'EOF'
# Yield Sight System - Production Nginx Configuration
# Optimized for high performance and security

# Rate limiting zones
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=auth:10m rate=5r/s;
limit_req_zone $binary_remote_addr zone=upload:10m rate=2r/s;

# Connection limiting
limit_conn_zone $binary_remote_addr zone=conn_limit_per_ip:10m;

# Upstream servers
upstream backend_api {
    server 127.0.0.1:8000 max_fails=3 fail_timeout=30s;
    keepalive 32;
}

upstream ai_service {
    server 127.0.0.1:8001 max_fails=3 fail_timeout=30s;
    keepalive 16;
}

upstream frontend_app {
    server 127.0.0.1:3000 max_fails=3 fail_timeout=30s;
    keepalive 32;
}

# Main server block (HTTPS)
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    # SSL Configuration
    ssl_certificate /etc/ssl/certs/yourdomain.com.crt;
    ssl_certificate_key /etc/ssl/private/yourdomain.com.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    ssl_stapling on;
    ssl_stapling_verify on;

    # Security Headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://maps.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' wss: https:; frame-src 'self';" always;

    # Connection and rate limiting
    limit_conn conn_limit_per_ip 20;

    # Logging
    access_log /var/log/nginx/yieldsight_access.log;
    error_log /var/log/nginx/yieldsight_error.log;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # Frontend application (main site)
    location / {
        proxy_pass http://frontend_app;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }

    # Backend API
    location /api/ {
        limit_req zone=api burst=20 nodelay;

        proxy_pass http://backend_api/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;

        # CORS headers for API
        add_header Access-Control-Allow-Origin "https://yourdomain.com" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Authorization, Content-Type, X-Requested-With" always;
        add_header Access-Control-Allow-Credentials "true" always;

        if ($request_method = 'OPTIONS') {
            return 204;
        }
    }

    # AI Service API
    location /ai/ {
        limit_req zone=api burst=10 nodelay;

        proxy_pass http://ai_service/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 600s;  # Longer timeout for ML operations
        proxy_connect_timeout 75s;
    }

    # Authentication endpoints (stricter rate limiting)
    location ~ ^/api/(auth|login|register) {
        limit_req zone=auth burst=5 nodelay;

        proxy_pass http://backend_api;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # File upload endpoints (stricter rate limiting)
    location ~ ^/api/.*/(upload|import) {
        limit_req zone=upload burst=3 nodelay;
        client_max_body_size 50M;

        proxy_pass http://backend_api;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 600s;
        proxy_send_timeout 600s;
    }

    # WebSocket connections
    location /ws {
        proxy_pass http://backend_api;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 86400;
    }

    # Static files caching
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Content-Type-Options nosniff;
    }

    # Health check endpoint
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}

# HTTP to HTTPS redirect
server {
    listen 80;
    listen [::]:80;
    server_name yourdomain.com www.yourdomain.com;

    # Redirect all HTTP requests to HTTPS
    return 301 https://$server_name$request_uri;
}

# API subdomain (optional)
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name api.yourdomain.com;

    # SSL Configuration (same as main server)
    ssl_certificate /etc/ssl/certs/yourdomain.com.crt;
    ssl_certificate_key /etc/ssl/private/yourdomain.com.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Security Headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    add_header X-Frame-Options "DENY" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;

    # Logging
    access_log /var/log/nginx/api_access.log;
    error_log /var/log/nginx/api_error.log;

    # API endpoints
    location / {
        limit_req zone=api burst=20 nodelay;

        proxy_pass http://backend_api/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }

    # AI Service
    location /ai/ {
        limit_req zone=api burst=10 nodelay;

        proxy_pass http://ai_service/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 600s;
        proxy_connect_timeout 75s;
    }
}
EOF

# Enable the site
sudo ln -sf /etc/nginx/sites-available/yieldsight /etc/nginx/sites-enabled/

# Test Nginx configuration
sudo nginx -t

# If configuration is valid, reload Nginx
if sudo nginx -t; then
    sudo systemctl reload nginx
    echo "Nginx configuration applied successfully"
else
    echo "Nginx configuration test failed. Please check the configuration."
    exit 1
fi
```

### Phase 10: SSL Certificate Setup

#### 10.1 Install Certbot for Let's Encrypt SSL
```bash
# Install Certbot
sudo apt install -y certbot python3-certbot-nginx

# Obtain SSL certificates (replace yourdomain.com with your actual domain)
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com -d api.yourdomain.com

# Set up automatic renewal
sudo systemctl enable certbot.timer
sudo systemctl start certbot.timer

# Test automatic renewal
sudo certbot renew --dry-run

# Verify SSL configuration
sudo nginx -t && sudo systemctl reload nginx
```

#### 10.2 Alternative: Manual SSL Certificate Setup
```bash
# If using custom SSL certificates, place them in the correct locations:
# Certificate: /etc/ssl/certs/yourdomain.com.crt
# Private Key: /etc/ssl/private/yourdomain.com.key
# Certificate Chain: /etc/ssl/certs/yourdomain.com-chain.crt

# Set proper permissions
sudo chmod 644 /etc/ssl/certs/yourdomain.com.crt
sudo chmod 600 /etc/ssl/private/yourdomain.com.key
sudo chown root:root /etc/ssl/certs/yourdomain.com.crt
sudo chown root:root /etc/ssl/private/yourdomain.com.key
```

### Phase 11: Service Management and Startup

#### 11.1 Create Systemd Services for PM2
```bash
# Create systemd service for PM2
sudo tee /etc/systemd/system/yieldsight-pm2.service > /dev/null << 'EOF'
[Unit]
Description=Yield Sight System PM2 Process Manager
Documentation=https://pm2.keymetrics.io/
After=network.target postgresql.service redis-server.service
Wants=postgresql.service redis-server.service

[Service]
Type=forking
User=yieldsight
LimitNOFILE=infinity
LimitNPROC=infinity
LimitCORE=infinity
Environment=PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
Environment=PM2_HOME=/home/<USER>/.pm2
ExecStart=/usr/bin/pm2 resurrect
ExecReload=/usr/bin/pm2 reload all
ExecStop=/usr/bin/pm2 kill
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# Enable the service
sudo systemctl daemon-reload
sudo systemctl enable yieldsight-pm2.service
```

#### 11.2 Start All Services
```bash
# Switch to application user
sudo -u yieldsight -i

# Start backend services
cd /opt/yieldsight/soil-master/soil-backend
pm2 start ecosystem.config.js --env production

# Start AI service
cd /opt/yieldsight/soil-master/soil-ai
pm2 start ecosystem.config.js --env production

# Start frontend service
cd /opt/yieldsight/soil-master/soil-frontend
pm2 start ecosystem.config.js --env production

# Save PM2 configuration
pm2 save

# Generate PM2 startup script
pm2 startup systemd -u yieldsight --hp /home/<USER>

# Exit back to root user
exit

# Start the systemd service
sudo systemctl start yieldsight-pm2.service

# Verify all services are running
sudo -u yieldsight pm2 status
```

### Phase 12: Log Management and Monitoring

#### 12.1 Configure Log Rotation
```bash
# Create logrotate configuration for application logs
sudo tee /etc/logrotate.d/yieldsight > /dev/null << 'EOF'
/var/log/yieldsight/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 yieldsight yieldsight
    postrotate
        sudo -u yieldsight pm2 reloadLogs
    endscript
}

/var/log/nginx/yieldsight_*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        if [ -f /var/run/nginx.pid ]; then
            kill -USR1 `cat /var/run/nginx.pid`
        fi
    endscript
}
EOF

# Test logrotate configuration
sudo logrotate -d /etc/logrotate.d/yieldsight
```

#### 12.2 Setup Basic Monitoring Scripts
```bash
# Create monitoring script
sudo tee /usr/local/bin/yieldsight-monitor.sh > /dev/null << 'EOF'
#!/bin/bash
# Yield Sight System Health Monitor

LOG_FILE="/var/log/yieldsight/monitor.log"
DATE=$(date '+%Y-%m-%d %H:%M:%S')

log() {
    echo "[$DATE] $1" >> $LOG_FILE
}

# Check if services are running
check_service() {
    local service_name=$1
    if sudo -u yieldsight pm2 describe $service_name > /dev/null 2>&1; then
        local status=$(sudo -u yieldsight pm2 describe $service_name | grep "status" | awk '{print $4}')
        if [ "$status" = "online" ]; then
            log "✓ $service_name is running"
            return 0
        else
            log "✗ $service_name is not running (status: $status)"
            return 1
        fi
    else
        log "✗ $service_name not found"
        return 1
    fi
}

# Check database connectivity
check_database() {
    if sudo -u postgres psql -d yieldsight_soil_db -c "SELECT 1;" > /dev/null 2>&1; then
        log "✓ Database is accessible"
        return 0
    else
        log "✗ Database connection failed"
        return 1
    fi
}

# Check Redis connectivity
check_redis() {
    if redis-cli ping > /dev/null 2>&1; then
        log "✓ Redis is accessible"
        return 0
    else
        log "✗ Redis connection failed"
        return 1
    fi
}

# Check disk space
check_disk_space() {
    local usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ $usage -lt 80 ]; then
        log "✓ Disk usage: ${usage}%"
        return 0
    else
        log "⚠ High disk usage: ${usage}%"
        return 1
    fi
}

# Main monitoring function
main() {
    log "=== Health Check Started ==="

    local failed=0

    check_service "yieldsight-backend" || failed=$((failed + 1))
    check_service "yieldsight-ai" || failed=$((failed + 1))
    check_service "yieldsight-frontend" || failed=$((failed + 1))
    check_service "yieldsight-celery-worker" || failed=$((failed + 1))
    check_service "yieldsight-celery-beat" || failed=$((failed + 1))

    check_database || failed=$((failed + 1))
    check_redis || failed=$((failed + 1))
    check_disk_space || failed=$((failed + 1))

    if [ $failed -eq 0 ]; then
        log "✓ All systems operational"
    else
        log "⚠ $failed issues detected"
    fi

    log "=== Health Check Completed ==="
}

main
EOF

# Make script executable
sudo chmod +x /usr/local/bin/yieldsight-monitor.sh

# Create cron job for monitoring
echo "*/5 * * * * /usr/local/bin/yieldsight-monitor.sh" | sudo crontab -
```

## 🔍 Deployment Validation & Testing

### Phase 13: System Validation

#### 13.1 Validate All Services
```bash
# Create comprehensive validation script
sudo tee /usr/local/bin/yieldsight-validate.sh > /dev/null << 'EOF'
#!/bin/bash
# Yield Sight System Deployment Validation Script

set -e

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Test database connectivity
test_database() {
    log "Testing database connectivity..."
    if sudo -u postgres psql -d yieldsight_soil_db -c "SELECT version();" > /dev/null 2>&1; then
        log "✓ Database connection successful"

        # Test extensions
        local extensions=$(sudo -u postgres psql -d yieldsight_soil_db -t -c "SELECT extname FROM pg_extension;" | tr -d ' ')
        if echo "$extensions" | grep -q "timescaledb"; then
            log "✓ TimescaleDB extension loaded"
        else
            error "✗ TimescaleDB extension not found"
            return 1
        fi

        if echo "$extensions" | grep -q "postgis"; then
            log "✓ PostGIS extension loaded"
        else
            error "✗ PostGIS extension not found"
            return 1
        fi

        return 0
    else
        error "✗ Database connection failed"
        return 1
    fi
}

# Test Redis connectivity
test_redis() {
    log "Testing Redis connectivity..."
    if redis-cli ping | grep -q "PONG"; then
        log "✓ Redis connection successful"
        return 0
    else
        error "✗ Redis connection failed"
        return 1
    fi
}

# Test PM2 services
test_pm2_services() {
    log "Testing PM2 services..."
    local services=("yieldsight-backend" "yieldsight-ai" "yieldsight-frontend" "yieldsight-celery-worker" "yieldsight-celery-beat")

    for service in "${services[@]}"; do
        if sudo -u yieldsight pm2 describe "$service" > /dev/null 2>&1; then
            local status=$(sudo -u yieldsight pm2 describe "$service" | grep "status" | awk '{print $4}')
            if [ "$status" = "online" ]; then
                log "✓ $service is running"
            else
                error "✗ $service is not running (status: $status)"
                return 1
            fi
        else
            error "✗ $service not found"
            return 1
        fi
    done

    return 0
}

# Test HTTP endpoints
test_endpoints() {
    log "Testing HTTP endpoints..."

    # Test frontend
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:3000 | grep -q "200"; then
        log "✓ Frontend is responding"
    else
        error "✗ Frontend is not responding"
        return 1
    fi

    # Test backend API
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/health | grep -q "200"; then
        log "✓ Backend API is responding"
    else
        error "✗ Backend API is not responding"
        return 1
    fi

    # Test AI service
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:8001/health | grep -q "200"; then
        log "✓ AI service is responding"
    else
        error "✗ AI service is not responding"
        return 1
    fi

    return 0
}

# Test Nginx configuration
test_nginx() {
    log "Testing Nginx configuration..."
    if sudo nginx -t > /dev/null 2>&1; then
        log "✓ Nginx configuration is valid"

        if systemctl is-active --quiet nginx; then
            log "✓ Nginx is running"
            return 0
        else
            error "✗ Nginx is not running"
            return 1
        fi
    else
        error "✗ Nginx configuration is invalid"
        return 1
    fi
}

# Test SSL certificates (if configured)
test_ssl() {
    log "Testing SSL certificates..."
    if [ -f "/etc/ssl/certs/yourdomain.com.crt" ]; then
        local expiry=$(openssl x509 -enddate -noout -in /etc/ssl/certs/yourdomain.com.crt | cut -d= -f2)
        local expiry_epoch=$(date -d "$expiry" +%s)
        local current_epoch=$(date +%s)
        local days_until_expiry=$(( (expiry_epoch - current_epoch) / 86400 ))

        if [ $days_until_expiry -gt 30 ]; then
            log "✓ SSL certificate is valid (expires in $days_until_expiry days)"
        else
            warn "⚠ SSL certificate expires soon ($days_until_expiry days)"
        fi
        return 0
    else
        warn "⚠ SSL certificate not found (using self-signed or Let's Encrypt)"
        return 0
    fi
}

# Test system resources
test_resources() {
    log "Testing system resources..."

    # Check memory usage
    local mem_usage=$(free | grep Mem | awk '{printf "%.0f", $3/$2 * 100.0}')
    if [ $mem_usage -lt 80 ]; then
        log "✓ Memory usage: ${mem_usage}%"
    else
        warn "⚠ High memory usage: ${mem_usage}%"
    fi

    # Check disk usage
    local disk_usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ $disk_usage -lt 80 ]; then
        log "✓ Disk usage: ${disk_usage}%"
    else
        warn "⚠ High disk usage: ${disk_usage}%"
    fi

    # Check CPU load
    local cpu_load=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    local cpu_cores=$(nproc)
    local cpu_usage=$(echo "$cpu_load $cpu_cores" | awk '{printf "%.0f", $1/$2 * 100}')

    if [ $cpu_usage -lt 80 ]; then
        log "✓ CPU load: ${cpu_usage}%"
    else
        warn "⚠ High CPU load: ${cpu_usage}%"
    fi

    return 0
}

# Main validation function
main() {
    info "=== Yield Sight System Deployment Validation ==="
    info "Starting comprehensive system validation..."

    local failed=0

    test_database || failed=$((failed + 1))
    test_redis || failed=$((failed + 1))
    test_pm2_services || failed=$((failed + 1))
    test_endpoints || failed=$((failed + 1))
    test_nginx || failed=$((failed + 1))
    test_ssl || failed=$((failed + 1))
    test_resources || failed=$((failed + 1))

    echo
    if [ $failed -eq 0 ]; then
        log "🎉 All validation tests passed! System is ready for production."
        log "Access your application at: https://yourdomain.com"
        log "API documentation: https://yourdomain.com/api/docs"
    else
        error "❌ $failed validation tests failed. Please review and fix the issues."
        exit 1
    fi

    info "=== Validation Complete ==="
}

main "$@"
EOF

# Make script executable
sudo chmod +x /usr/local/bin/yieldsight-validate.sh

# Run validation
sudo /usr/local/bin/yieldsight-validate.sh
```

#### 13.2 Performance Testing
```bash
# Install Apache Bench for basic load testing
sudo apt install -y apache2-utils

# Test frontend performance
ab -n 100 -c 10 http://localhost:3000/

# Test backend API performance
ab -n 100 -c 10 http://localhost:8000/health

# Test AI service performance
ab -n 50 -c 5 http://localhost:8001/health
```

## 🛠️ Troubleshooting Guide

### Common Issues and Solutions

#### Issue 1: Services Not Starting
```bash
# Check PM2 status
sudo -u yieldsight pm2 status

# Check logs for specific service
sudo -u yieldsight pm2 logs yieldsight-backend

# Restart specific service
sudo -u yieldsight pm2 restart yieldsight-backend

# Check system resources
htop
df -h
free -h
```

#### Issue 2: Database Connection Issues
```bash
# Check PostgreSQL status
sudo systemctl status postgresql

# Check database connectivity
sudo -u postgres psql -d yieldsight_soil_db -c "SELECT version();"

# Check database logs
sudo tail -f /var/log/postgresql/postgresql-17-main.log

# Restart PostgreSQL
sudo systemctl restart postgresql
```

#### Issue 3: Nginx Configuration Issues
```bash
# Test Nginx configuration
sudo nginx -t

# Check Nginx status
sudo systemctl status nginx

# Check Nginx logs
sudo tail -f /var/log/nginx/error.log
sudo tail -f /var/log/nginx/yieldsight_error.log

# Restart Nginx
sudo systemctl restart nginx
```

#### Issue 4: SSL Certificate Issues
```bash
# Check certificate validity
openssl x509 -in /etc/ssl/certs/yourdomain.com.crt -text -noout

# Renew Let's Encrypt certificates
sudo certbot renew

# Test SSL configuration
openssl s_client -connect yourdomain.com:443 -servername yourdomain.com
```

## 📚 Maintenance and Operations

### Daily Operations

#### Log Monitoring
```bash
# Monitor application logs
sudo tail -f /var/log/yieldsight/*.log

# Monitor Nginx logs
sudo tail -f /var/log/nginx/yieldsight_*.log

# Monitor system logs
sudo journalctl -f -u yieldsight-pm2.service
```

#### Backup Procedures
```bash
# Database backup
sudo -u postgres pg_dump yieldsight_soil_db > /var/lib/yieldsight/backups/db_$(date +%Y%m%d_%H%M%S).sql

# Application backup
sudo tar -czf /var/lib/yieldsight/backups/app_$(date +%Y%m%d_%H%M%S).tar.gz /opt/yieldsight/soil-master

# Configuration backup
sudo tar -czf /var/lib/yieldsight/backups/config_$(date +%Y%m%d_%H%M%S).tar.gz /etc/yieldsight /etc/nginx/sites-available/yieldsight
```

#### Update Procedures
```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Update application code
cd /opt/yieldsight/soil-master
sudo -u yieldsight git pull origin main

# Restart services
sudo -u yieldsight pm2 restart all

# Validate deployment
sudo /usr/local/bin/yieldsight-validate.sh
```

### Security Maintenance

#### Regular Security Tasks
```bash
# Update security packages
sudo apt update && sudo apt list --upgradable | grep -i security

# Check for failed login attempts
sudo fail2ban-client status sshd

# Review firewall rules
sudo ufw status verbose

# Check SSL certificate expiry
sudo certbot certificates

# Review application logs for security events
sudo grep -i "error\|warning\|failed" /var/log/yieldsight/*.log
```

## 📞 Support and Documentation

### Important File Locations
- **Application Code**: `/opt/yieldsight/soil-master/`
- **Configuration Files**: `/etc/yieldsight/`
- **Log Files**: `/var/log/yieldsight/`
- **Data Storage**: `/var/lib/yieldsight/`
- **Nginx Configuration**: `/etc/nginx/sites-available/yieldsight`
- **SSL Certificates**: `/etc/ssl/certs/` and `/etc/ssl/private/`

### Useful Commands
```bash
# Check all service status
sudo -u yieldsight pm2 status

# View real-time logs
sudo -u yieldsight pm2 logs

# Restart all services
sudo -u yieldsight pm2 restart all

# Reload Nginx configuration
sudo nginx -s reload

# Check system health
sudo /usr/local/bin/yieldsight-validate.sh

# Monitor system resources
htop
```

### Emergency Contacts and Procedures
1. **System Administrator**: Contact your system administrator for infrastructure issues
2. **Database Issues**: Check PostgreSQL documentation and logs
3. **Application Issues**: Review application logs and PM2 status
4. **Security Incidents**: Follow your organization's incident response procedures

---

## ✅ Deployment Checklist

- [ ] System requirements verified
- [ ] Ubuntu Server 24.04 LTS installed and updated
- [ ] All required packages installed
- [ ] PostgreSQL with extensions configured
- [ ] Redis installed and configured
- [ ] Nginx installed and configured
- [ ] Application code cloned and dependencies installed
- [ ] Environment variables configured
- [ ] Database migrations completed
- [ ] PM2 services configured and running
- [ ] SSL certificates installed
- [ ] Firewall configured
- [ ] Monitoring and logging setup
- [ ] Backup procedures implemented
- [ ] Validation tests passed
- [ ] Performance testing completed
- [ ] Documentation reviewed

**🎉 Congratulations! Your Yield Sight System is now deployed and ready for production use.**
```
```
```
```
```
