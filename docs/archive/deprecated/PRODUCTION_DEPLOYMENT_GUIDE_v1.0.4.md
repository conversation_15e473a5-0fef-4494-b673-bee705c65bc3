# 🚀 Soil Master v1.0.4 - Production Deployment Guide

**Complete setup instructions for Ubuntu 24.04 LTS deployment**

---

## 📋 **Prerequisites**

### **System Requirements**
- **Operating System**: Ubuntu Server 24.04 LTS (Clean installation)
- **Memory**: Minimum 8GB RAM (16GB recommended for production)
- **Storage**: Minimum 100GB SSD (500GB recommended for production)
- **CPU**: Minimum 4 cores (8 cores recommended for production)
- **Network**: Stable internet connection for initial setup

### **Required Software Versions**
- **Node.js**: v24.x LTS
- **Python**: 3.11+
- **PostgreSQL**: 17+
- **Redis**: 7.0+
- **Nginx**: 1.24+
- **PM2**: Latest stable

---

## 🔧 **Step 1: System Preparation**

### **1.1 Update System**
```bash
# Update package lists and upgrade system
sudo apt update && sudo apt upgrade -y

# Install essential build tools
sudo apt install -y curl wget git build-essential software-properties-common
```

### **1.2 Install Node.js v24**
```bash
# Install Node.js v24 LTS
curl -fsSL https://deb.nodesource.com/setup_24.x | sudo -E bash -
sudo apt install -y nodejs

# Verify installation
node --version  # Should show v24.x.x
npm --version   # Should show 10.x.x or higher
```

### **1.3 Install Python 3.11+**
```bash
# Install Python 3.11 and pip
sudo apt install -y python3.11 python3.11-pip python3.11-venv python3.11-dev

# Create symlinks for convenience
sudo ln -sf /usr/bin/python3.11 /usr/bin/python3
sudo ln -sf /usr/bin/pip3.11 /usr/bin/pip3

# Verify installation
python3 --version  # Should show Python 3.11.x
```

### **1.4 Install PostgreSQL 17**
```bash
# Add PostgreSQL official repository
sudo sh -c 'echo "deb http://apt.postgresql.org/pub/repos/apt $(lsb_release -cs)-pgdg main" > /etc/apt/sources.list.d/pgdg.list'
wget --quiet -O - https://www.postgresql.org/media/keys/ACCC4CF8.asc | sudo apt-key add -
sudo apt update

# Install PostgreSQL 17
sudo apt install -y postgresql-17 postgresql-client-17 postgresql-contrib-17

# Start and enable PostgreSQL
sudo systemctl start postgresql
sudo systemctl enable postgresql

# Verify installation
sudo -u postgres psql -c "SELECT version();"
```

### **1.5 Install Redis**
```bash
# Install Redis
sudo apt install -y redis-server

# Configure Redis for production
sudo sed -i 's/^# maxmemory <bytes>/maxmemory 1gb/' /etc/redis/redis.conf
sudo sed -i 's/^# maxmemory-policy noeviction/maxmemory-policy allkeys-lru/' /etc/redis/redis.conf

# Start and enable Redis
sudo systemctl start redis-server
sudo systemctl enable redis-server

# Verify installation
redis-cli ping  # Should return PONG
```

### **1.6 Install Nginx**
```bash
# Install Nginx
sudo apt install -y nginx

# Start and enable Nginx
sudo systemctl start nginx
sudo systemctl enable nginx

# Verify installation
sudo systemctl status nginx
```

---

## 📁 **Step 2: Project Setup**

### **2.1 Create Application Directory**
```bash
# Create application directory
sudo mkdir -p /opt/soil-master
sudo chown $USER:$USER /opt/soil-master
cd /opt/soil-master

# Clone the repository
git clone https://github.com/Yield-Sight-System/soil-master.git .

# Verify version
grep '"version"' soil-frontend/package.json  # Should show "1.0.4"
```

### **2.2 Create System User**
```bash
# Create dedicated user for the application
sudo useradd -r -s /bin/bash -d /opt/soil-master soil-master
sudo chown -R soil-master:soil-master /opt/soil-master

# Create necessary directories
sudo mkdir -p /var/log/soil-master
sudo mkdir -p /opt/soil-master/uploads
sudo mkdir -p /opt/soil-master/recordings
sudo mkdir -p /opt/soil-master/backups
sudo chown -R soil-master:soil-master /var/log/soil-master
sudo chown -R soil-master:soil-master /opt/soil-master
```

---

## 🗄️ **Step 3: Database Setup**

### **3.1 Configure PostgreSQL**
```bash
# Switch to postgres user
sudo -u postgres psql

-- Create database and user
CREATE DATABASE soil_master_v104;
CREATE USER soil_user WITH ENCRYPTED PASSWORD 'your_secure_password_here';

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE soil_master_v104 TO soil_user;
ALTER USER soil_user CREATEDB;

-- Enable required extensions
\c soil_master_v104
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";
CREATE EXTENSION IF NOT EXISTS "timescaledb";

-- Exit psql
\q
```

### **3.2 Run Database Migrations**
```bash
# Switch to application directory
cd /opt/soil-master/soil-backend

# Create Python virtual environment
python3 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Validate migrations before running
python scripts/validate_migrations.py --database-url "postgresql://soil_user:your_secure_password_here@localhost:5432/soil_master_v104"

# Run migrations
alembic upgrade head

# Verify migration status
alembic current
alembic history --verbose
```

### **3.3 Seed Initial Data**
```bash
# Run initial data seeding
python scripts/init_database.py
python scripts/run_mock_data_generation.py

# Verify data integrity
python scripts/validate_data_integrity.py
```

---

## ⚙️ **Step 4: Environment Configuration**

### **4.1 Backend Environment**
```bash
# Copy environment template
cd /opt/soil-master
cp .env.example .env

# Edit environment variables
nano .env
```

**Key variables to update in .env:**
```bash
# System Configuration
SYSTEM_VERSION=1.0.4
SYSTEM_ENV=production

# Database Configuration
DATABASE_URL=postgresql://soil_user:your_secure_password_here@localhost:5432/soil_master_v104

# Security Configuration
SECRET_KEY=your-super-secret-key-change-this-in-production-min-64-chars
JWT_SECRET_KEY=your-jwt-secret-key-change-this-in-production-min-64-chars

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# File Storage
UPLOAD_DIR=/opt/soil-master/uploads
RECORDING_DIR=/opt/soil-master/recordings

# v1.0.4 Features
DEMO_STABILITY_ENABLED=true
MOBILE_OPTIMIZATION=true
BUSINESS_CASE_TEMPLATES=true
EXECUTIVE_REPORTING=true
```

### **4.2 Frontend Environment**
```bash
# Create frontend environment file
cd /opt/soil-master/soil-frontend
cp .env.local.example .env.local

# Edit frontend environment
nano .env.local
```

**Frontend environment variables:**
```bash
NEXT_PUBLIC_API_URL=http://localhost:8000/api/v1
NEXT_PUBLIC_APP_VERSION=1.0.4
NEXT_PUBLIC_DEMO_STABILITY_ENABLED=true
NEXT_PUBLIC_MOBILE_OPTIMIZATION=true
NEXT_PUBLIC_BUSINESS_CASE_TEMPLATES=true
NEXT_PUBLIC_EXECUTIVE_REPORTING=true
```

---

## 🏗️ **Step 5: Service Installation**

### **5.1 Install Backend Dependencies**
```bash
cd /opt/soil-master/soil-backend

# Activate virtual environment
source venv/bin/activate

# Install production dependencies
pip install -r requirements.txt

# Install PM2 globally
sudo npm install -g pm2
```

### **5.2 Install Frontend Dependencies**
```bash
cd /opt/soil-master/soil-frontend

# Install dependencies
npm ci --production

# Build for production
npm run build

# Verify build
ls -la .next/
```

### **5.3 Install Soil-AI Dependencies**
```bash
cd /opt/soil-master/soil-ai

# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Test AI service
python -c "import xgboost; print('XGBoost version:', xgboost.__version__)"
```

---

## 🚀 **Step 6: Service Configuration**

### **6.1 Create PM2 Ecosystem File**
```bash
# Create PM2 configuration
cat > /opt/soil-master/ecosystem.config.js << 'EOF'
module.exports = {
  apps: [
    {
      name: 'soil-backend',
      script: 'venv/bin/uvicorn',
      args: 'app.main:app --host 0.0.0.0 --port 8000',
      cwd: '/opt/soil-master/soil-backend',
      instances: 2,
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PYTHONPATH: '/opt/soil-master/soil-backend'
      },
      error_file: '/var/log/soil-master/backend-error.log',
      out_file: '/var/log/soil-master/backend-out.log',
      log_file: '/var/log/soil-master/backend-combined.log'
    },
    {
      name: 'soil-ai',
      script: 'venv/bin/uvicorn',
      args: 'soil_ai.inference.api.main:app --host 0.0.0.0 --port 8001',
      cwd: '/opt/soil-master/soil-ai',
      instances: 1,
      env: {
        NODE_ENV: 'production',
        PYTHONPATH: '/opt/soil-master/soil-ai'
      },
      error_file: '/var/log/soil-master/ai-error.log',
      out_file: '/var/log/soil-master/ai-out.log',
      log_file: '/var/log/soil-master/ai-combined.log'
    },
    {
      name: 'soil-frontend',
      script: 'npm',
      args: 'start',
      cwd: '/opt/soil-master/soil-frontend',
      instances: 1,
      env: {
        NODE_ENV: 'production',
        PORT: 3000
      },
      error_file: '/var/log/soil-master/frontend-error.log',
      out_file: '/var/log/soil-master/frontend-out.log',
      log_file: '/var/log/soil-master/frontend-combined.log'
    }
  ]
};
EOF
```

### **6.2 Configure Nginx**
```bash
# Create Nginx configuration
sudo cat > /etc/nginx/sites-available/soil-master << 'EOF'
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    
    # Frontend (Next.js)
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
    }
    
    # Backend API
    location /api/ {
        proxy_pass http://localhost:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
    }
    
    # AI Service
    location /ai/ {
        proxy_pass http://localhost:8001/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 86400;
    }
    
    # Static files
    location /static/ {
        alias /opt/soil-master/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Uploads
    location /uploads/ {
        alias /opt/soil-master/uploads/;
        expires 1d;
    }
    
    # Recordings
    location /recordings/ {
        alias /opt/soil-master/recordings/;
        expires 1d;
    }
}
EOF

# Enable the site
sudo ln -s /etc/nginx/sites-available/soil-master /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default

# Test Nginx configuration
sudo nginx -t

# Reload Nginx
sudo systemctl reload nginx
```

---

## 🔄 **Step 7: Service Startup**

### **7.1 Start Services in Correct Order**
```bash
# 1. Start PostgreSQL (should already be running)
sudo systemctl status postgresql

# 2. Start Redis (should already be running)
sudo systemctl status redis-server

# 3. Start backend and AI services
cd /opt/soil-master
sudo -u soil-master pm2 start ecosystem.config.js

# 4. Verify services are running
sudo -u soil-master pm2 status
sudo -u soil-master pm2 logs

# 5. Start Nginx (should already be running)
sudo systemctl status nginx
```

### **7.2 Enable Auto-start**
```bash
# Save PM2 configuration
sudo -u soil-master pm2 save

# Generate startup script
sudo -u soil-master pm2 startup
# Follow the instructions provided by the command above

# Enable services to start on boot
sudo systemctl enable postgresql
sudo systemctl enable redis-server
sudo systemctl enable nginx
```

---

## ✅ **Step 8: Validation & Testing**

### **8.1 Health Checks**
```bash
# Check database connectivity
cd /opt/soil-master/soil-backend
source venv/bin/activate
python scripts/setup_validation.py

# Check API endpoints
curl -f http://localhost:8000/api/v1/health
curl -f http://localhost:8001/health

# Check frontend
curl -f http://localhost:3000

# Check through Nginx
curl -f http://localhost/api/v1/health
```

### **8.2 Run Validation Suite**
```bash
# Run comprehensive validation
cd /opt/soil-master/soil-frontend
npm run validate:production

# Run backend tests
cd /opt/soil-master/soil-backend
source venv/bin/activate
python -m pytest tests/ -v

# Run migration validation
python scripts/validate_migrations.py
```

### **8.3 Demo Functionality Test**
```bash
# Test demo scenarios
curl -X GET "http://localhost/api/v1/demo/scenarios"

# Test ROI calculations
curl -X POST "http://localhost/api/v1/roi/calculate" \
  -H "Content-Type: application/json" \
  -d '{"estate_size_hectares": 1000, "crop_type": "palm_oil", "region": "malaysia"}'

# Test v1.0.4 features
curl -X GET "http://localhost/api/v1/demo/health"
curl -X GET "http://localhost/api/v1/business-cases/templates"
```

---

## 🔒 **Step 9: Security Hardening**

### **9.1 Firewall Configuration**
```bash
# Configure UFW firewall
sudo ufw enable
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw status
```

### **9.2 SSL/TLS Setup (Optional)**
```bash
# Install Certbot for Let's Encrypt
sudo apt install -y certbot python3-certbot-nginx

# Obtain SSL certificate
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# Test automatic renewal
sudo certbot renew --dry-run
```

---

## 📊 **Step 10: Monitoring Setup**

### **10.1 Log Rotation**
```bash
# Configure log rotation
sudo cat > /etc/logrotate.d/soil-master << 'EOF'
/var/log/soil-master/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 soil-master soil-master
    postrotate
        sudo -u soil-master pm2 reloadLogs
    endscript
}
EOF
```

### **10.2 System Monitoring**
```bash
# Install monitoring tools
sudo apt install -y htop iotop nethogs

# Set up basic monitoring script
cat > /opt/soil-master/monitor.sh << 'EOF'
#!/bin/bash
echo "=== Soil Master v1.0.4 System Status ==="
echo "Date: $(date)"
echo ""
echo "=== Services ==="
sudo systemctl status postgresql --no-pager -l
sudo systemctl status redis-server --no-pager -l
sudo systemctl status nginx --no-pager -l
sudo -u soil-master pm2 status
echo ""
echo "=== Disk Usage ==="
df -h /opt/soil-master
echo ""
echo "=== Memory Usage ==="
free -h
echo ""
echo "=== API Health ==="
curl -s http://localhost/api/v1/health | jq .
EOF

chmod +x /opt/soil-master/monitor.sh
```

---

## 🎉 **Deployment Complete!**

Your Soil Master v1.0.4 system is now deployed and ready for production use.

### **Access URLs:**
- **Frontend**: http://your-domain.com
- **API Documentation**: http://your-domain.com/api/v1/docs
- **Health Check**: http://your-domain.com/api/v1/health

### **Next Steps:**
1. Configure domain DNS to point to your server
2. Set up SSL certificates for HTTPS
3. Configure backup procedures
4. Set up monitoring and alerting
5. Train users on the new v1.0.4 features

### **Support:**
- Check logs: `sudo -u soil-master pm2 logs`
- Monitor system: `/opt/soil-master/monitor.sh`
- Restart services: `sudo -u soil-master pm2 restart all`

**🎯 Your Soil Master v1.0.4 platform is production-ready!**

---

## 📋 **Service Management Commands**

### **Start All Services**
```bash
# Start in correct order
sudo systemctl start postgresql
sudo systemctl start redis-server
cd /opt/soil-master && sudo -u soil-master pm2 start ecosystem.config.js
sudo systemctl start nginx
```

### **Stop All Services**
```bash
# Stop in reverse order
sudo systemctl stop nginx
sudo -u soil-master pm2 stop all
sudo systemctl stop redis-server
sudo systemctl stop postgresql
```

### **Restart All Services**
```bash
# Restart services
sudo systemctl restart postgresql
sudo systemctl restart redis-server
sudo -u soil-master pm2 restart all
sudo systemctl restart nginx
```

### **Check Service Status**
```bash
# Quick status check
/opt/soil-master/monitor.sh

# Detailed status
sudo systemctl status postgresql redis-server nginx
sudo -u soil-master pm2 status
```
