# XGBoost GPU/CPU Fallback Implementation Guide

## Overview

This document details the implementation of the robust GPU-first, CPU-fallback strategy for XGBoost in the Soil AI application, ensuring optimal performance when GPU is available while maintaining compatibility on CPU-only systems.

## Implementation Architecture

### 1. Device Detection Function

```python
def _detect_compute_device() -> Dict[str, Any]:
    """
    Detect optimal compute device for XGBoost training.
    
    Implements GPU-first, CPU-fallback strategy with robust error handling.
    """
    device_config = {
        "tree_method": "hist",
        "device": "cpu",
        "gpu_available": False,
        "cuda_support": False,
        "device_used": "cpu",
        "fallback_reason": None
    }
    
    # Check for forced CPU mode
    force_cpu = os.getenv("FORCE_CPU", "false").lower() in ("true", "1", "yes")
    if force_cpu:
        device_config["fallback_reason"] = "Forced CPU mode via FORCE_CPU environment variable"
        return device_config
    
    # Check GPU settings
    if not settings.gpu.enabled:
        device_config["fallback_reason"] = "GPU disabled in application settings"
        return device_config
    
    # Test CUDA support and GPU availability
    try:
        # Check XGBoost CUDA compilation
        result = subprocess.run(["python", "-c", "import xgboost as xgb; print(xgb.build_info())"], 
                              capture_output=True, text=True, timeout=10)
        
        if "USE_CUDA:ON" not in result.stdout:
            device_config["fallback_reason"] = "XGBoost not compiled with CUDA support"
            return device_config
            
        # Test GPU with minimal XGBoost operation
        test_model = xgb.XGBRegressor(n_estimators=1, max_depth=1, tree_method="hist", 
                                    device="cuda", verbosity=0)
        test_model.fit(np.random.random((10, 5)), np.random.random(10))
        
        # GPU test successful
        device_config.update({
            "tree_method": "hist",
            "device": "cuda", 
            "gpu_available": True,
            "cuda_support": True,
            "device_used": "gpu",
            "fallback_reason": None
        })
        
    except Exception as e:
        device_config["fallback_reason"] = f"GPU test failed: {str(e)}"
        
    return device_config
```

### 2. XGBoost Model Integration

```python
class XGBoostPredictor(BasePredictor):
    def __init__(self, **kwargs):
        # Detect optimal compute device
        self.device_config = _detect_compute_device()
        
        # Configure XGBoost parameters with device selection
        self.xgb_params = {
            "n_estimators": self.n_estimators,
            "max_depth": self.max_depth,
            "learning_rate": self.learning_rate,
            "tree_method": self.device_config["tree_method"],
            "device": self.device_config["device"],
            "verbosity": 1,
            **kwargs
        }
        
        # Log device configuration
        device_used = self.device_config["device_used"]
        if self.device_config["fallback_reason"]:
            logger.info(f"XGBoost device: {device_used} (fallback: {self.device_config['fallback_reason']})")
        else:
            logger.info(f"XGBoost device: {device_used}")
```

### 3. Training with Fallback

```python
def _train_with_fallback(self, X, y, fit_params):
    """Train model with GPU/CPU fallback on training errors."""
    try:
        with warnings.catch_warnings():
            warnings.filterwarnings("ignore", category=UserWarning, module="xgboost")
            self._model.fit(X, y, **fit_params)
            
    except Exception as e:
        error_msg = str(e).lower()
        gpu_errors = ["must have at least one device", "gpu_hist", "cuda", 
                     "gpu is found", "xgboost is not compiled with cuda"]
        
        is_gpu_error = any(gpu_err in error_msg for gpu_err in gpu_errors)
        
        if is_gpu_error and self.device_config["device"] == "cuda":
            logger.warning(f"GPU training failed: {e}")
            logger.info("Attempting CPU fallback for training")
            
            # Recreate model with CPU parameters
            cpu_params = self.xgb_params.copy()
            cpu_params.update({"tree_method": "hist", "device": "cpu"})
            
            base_model = xgb.XGBRegressor(**cpu_params)
            if len(self.target_names) > 1:
                self._model = MultiOutputRegressor(base_model)
            else:
                self._model = base_model
            
            # Update device config
            self.device_config.update({
                "device": "cpu",
                "device_used": "cpu", 
                "fallback_reason": f"Training failed with GPU: {str(e)}"
            })
            
            # Retry training with CPU
            with warnings.catch_warnings():
                warnings.filterwarnings("ignore", category=UserWarning, module="xgboost")
                self._model.fit(X, y, **fit_params)
                
            logger.info("Successfully completed training with CPU fallback")
        else:
            raise
```

## Configuration Options

### Environment Variables

| Variable | Values | Description |
|----------|--------|-------------|
| `FORCE_CPU` | `true`, `false` | Force CPU mode, skip GPU detection |
| `GPU_ENABLED` | `true`, `false` | Application-level GPU setting |

### Application Settings

```yaml
gpu:
  enabled: true  # Enable GPU detection and usage
  
model:
  xgboost_tree_method: "hist"  # Tree construction method
  xgboost_device: "auto"       # Device selection (auto/cpu/cuda)
```

## Monitoring and Debugging

### Device Information API

```python
def get_device_info(self) -> Dict[str, Any]:
    """Get comprehensive device configuration information."""
    return {
        "device_used": self.device_config["device_used"],
        "device": self.device_config["device"],
        "tree_method": self.device_config["tree_method"],
        "gpu_available": self.device_config["gpu_available"],
        "cuda_support": self.device_config["cuda_support"],
        "fallback_reason": self.device_config["fallback_reason"],
        "xgboost_version": xgb.__version__,
        "force_cpu_env": os.getenv("FORCE_CPU", "false"),
        "gpu_enabled_setting": settings.gpu.enabled
    }
```

### Logging Examples

```
INFO: XGBoost: Using CPU mode (no CUDA support)
INFO: XGBoost device: cpu (fallback: XGBoost not compiled with CUDA support)
INFO: XGBoost model training completed successfully
```

## Performance Characteristics

### GPU Mode (when available)
- **Training Speed**: 2-5x faster than CPU
- **Memory Usage**: GPU memory utilized
- **Scalability**: Better for large datasets

### CPU Mode (fallback)
- **Training Speed**: Standard CPU performance
- **Memory Usage**: System RAM only
- **Compatibility**: Universal compatibility

## Deployment Scenarios

### Scenario 1: GPU-Enabled Server
```bash
# XGBoost with CUDA support installed
pip install xgboost[gpu]
# Application automatically detects and uses GPU
```

### Scenario 2: CPU-Only Server (Current)
```bash
# Standard XGBoost installation
pip install xgboost
# Application automatically falls back to CPU
```

### Scenario 3: Mixed Environment
```bash
# Force CPU mode for testing
export FORCE_CPU=true
# Application respects override
```

## Troubleshooting

### Common Issues

1. **GPU Detection Fails**
   - Check CUDA installation
   - Verify XGBoost GPU compilation
   - Review driver compatibility

2. **Training Errors**
   - Monitor logs for fallback messages
   - Verify memory availability
   - Check model parameters

3. **Performance Issues**
   - Compare GPU vs CPU training times
   - Monitor resource utilization
   - Adjust batch sizes

### Diagnostic Commands

```bash
# Check XGBoost build info
python -c "import xgboost as xgb; print(xgb.build_info())"

# Test GPU availability
nvidia-smi

# Check CUDA version
nvcc --version
```

## Best Practices

1. **Always implement fallback**: Never assume GPU availability
2. **Log device selection**: Provide transparency for debugging
3. **Test both modes**: Verify functionality in GPU and CPU modes
4. **Monitor performance**: Track training times and resource usage
5. **Handle errors gracefully**: Implement comprehensive exception handling

## Future Enhancements

1. **Multi-GPU Support**: Extend to multiple GPU devices
2. **Dynamic Switching**: Runtime device switching based on load
3. **Performance Profiling**: Automated GPU vs CPU benchmarking
4. **Resource Optimization**: Memory and compute optimization strategies

---

This implementation ensures optimal performance across diverse deployment environments while maintaining robust fallback capabilities.
