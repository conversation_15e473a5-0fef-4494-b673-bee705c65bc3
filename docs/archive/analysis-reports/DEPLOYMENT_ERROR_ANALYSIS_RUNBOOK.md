# 🔧 Soil Master v1.0.3 - Deployment Error Analysis & Resolution Runbook

**Document Version:** 1.0  
**Date:** July 13, 2025  
**Environment:** Ubuntu Server 24.04 LTS  
**Purpose:** Technical runbook for deployment error prevention and resolution  

## 📋 Executive Summary

This runbook documents the comprehensive error analysis from the Soil Master v1.0.3 production deployment, providing categorized error patterns, root cause analysis, prevention strategies, and quick resolution guides for future deployment teams.

## 🔍 Error Pattern Analysis

### 1. Import/Dependency Errors (40% of issues)

**Pattern:** Missing or incorrectly referenced Python modules
**Root Cause:** Inconsistent module naming and import paths between development and production
**Impact:** Service startup failures, API endpoint unavailability

**Common Examples:**
- `ModuleNotFoundError: No module named 'app.models.soil_data'`
- Missing schema imports in API routers
- Incorrect relative import paths

### 2. Configuration Errors (30% of issues)

**Pattern:** Mismatched service configurations and environment settings
**Root Cause:** Development configurations not adapted for production deployment
**Impact:** Service startup failures, incorrect process execution

**Common Examples:**
- PM2 trying to run `npm start` on Python applications
- Incorrect log file paths requiring system permissions
- Database connection string mismatches

### 3. Service Startup Errors (20% of issues)

**Pattern:** Process management and orchestration failures
**Root Cause:** Incorrect PM2 ecosystem configuration and service dependencies
**Impact:** Services not starting or restarting continuously

**Common Examples:**
- Wrong script interpreters (npm vs Python)
- Missing virtual environment activation
- Incorrect working directories

### 4. API Routing Errors (10% of issues)

**Pattern:** Endpoint accessibility and routing conflicts
**Root Cause:** Duplicate router prefixes and incorrect path configurations
**Impact:** 404 errors on valid endpoints, API unavailability

**Common Examples:**
- Double prefix paths (`/api/v1/roi/roi/calculate`)
- Router not properly included in main application
- Incorrect endpoint definitions

## 🚨 Specific Error Examples & Solutions

### Error Type 1: Module Import Failures

**Error Example:**
```
ModuleNotFoundError: No module named 'app.models.soil_data'
File "/app/services/database_optimization_service.py", line 18
from app.models.soil_data import SoilReading, SensorLocation
```

**Root Cause:** Referenced module doesn't exist; should import from `app.models.sensor`

**Resolution Steps:**
1. Identify the correct module location
2. Update import statement: `from app.models.sensor import SensorReading, Sensor`
3. Verify all references to renamed classes
4. Restart affected services

**Prevention:** Module dependency mapping and import validation scripts

### Error Type 2: PM2 Configuration Mismatch

**Error Example:**
```
npm error Missing script: "start"
PM2 trying to execute npm start on Python FastAPI application
```

**Root Cause:** Incorrect PM2 ecosystem configuration using npm for Python services

**Resolution Steps:**
1. Update ecosystem.config.js:
   ```javascript
   script: './venv/bin/python',
   args: '-m uvicorn app.main:app --host 0.0.0.0 --port 8000',
   interpreter: 'none'
   ```
2. Remove npm-specific configurations
3. Restart PM2 processes

**Prevention:** Service-specific PM2 configuration templates

### Error Type 3: Router Prefix Conflicts

**Error Example:**
```
404 Not Found on /api/v1/roi/calculate
Actual path becomes: /api/v1/roi/roi/calculate
```

**Root Cause:** Double prefix definition in router and main application

**Resolution Steps:**
1. Remove prefix from router definition:
   ```python
   router = APIRouter(tags=["ROI Analysis"])  # Remove prefix="/roi"
   ```
2. Keep prefix only in main router inclusion
3. Restart backend service

**Prevention:** Router configuration validation scripts

### Error Type 4: Database Migration Issues

**Error Example:**
```
asyncpg.exceptions.StringDataRightTruncationError: 
value too long for type character varying(32)
```

**Root Cause:** Alembic revision IDs exceeding database column limits

**Resolution Steps:**
1. Use direct SQL migration scripts for initial setup
2. Create shorter revision IDs
3. Verify database schema compatibility
4. Run migration verification scripts

**Prevention:** Database schema validation and migration testing

## 🛡️ Prevention Strategies

### 1. Pre-Deployment Validation Checklist

**Module Dependencies:**
- [ ] Verify all import statements resolve correctly
- [ ] Check virtual environment contains all required packages
- [ ] Validate module paths match actual file structure
- [ ] Test import resolution in production-like environment

**Service Configuration:**
- [ ] Validate PM2 ecosystem configuration for each service type
- [ ] Verify log file paths and permissions
- [ ] Check database connection strings
- [ ] Confirm environment variable availability

**API Routing:**
- [ ] Test all endpoint accessibility
- [ ] Verify router prefix configurations
- [ ] Check for duplicate route definitions
- [ ] Validate API documentation generation

### 2. Automated Testing Procedures

**Import Validation Script:**
```bash
#!/bin/bash
# validate_imports.sh
cd soil-backend
source venv/bin/activate
python -c "
import sys
try:
    from app.models.sensor import SensorReading, Sensor
    from app.services.roi_service import ROICalculationService
    from app.schemas.roi import ROICalculationRequest
    print('✅ All critical imports successful')
except ImportError as e:
    print(f'❌ Import error: {e}')
    sys.exit(1)
"
```

**Service Configuration Test:**
```bash
#!/bin/bash
# test_pm2_config.sh
pm2 start ecosystem.config.js --dry-run
if [ $? -eq 0 ]; then
    echo "✅ PM2 configuration valid"
else
    echo "❌ PM2 configuration errors detected"
    exit 1
fi
```

### 3. Configuration Validation Scripts

**Database Schema Validator:**
```python
#!/usr/bin/env python3
# validate_database.py
import asyncio
import asyncpg
import os

async def validate_schema():
    conn = await asyncpg.connect(os.getenv("DATABASE_URL"))
    
    # Check ROI tables exist
    tables = ['roi_scenarios', 'roi_calculations', 'investment_breakdown']
    for table in tables:
        result = await conn.fetchval(f"SELECT COUNT(*) FROM {table}")
        print(f"✅ Table {table}: {result} records")
    
    await conn.close()

if __name__ == "__main__":
    asyncio.run(validate_schema())
```

## ⚡ Quick Resolution Guides

### Guide 1: Service Won't Start

**Symptoms:** PM2 shows service as stopped or errored
**Quick Diagnosis:**
```bash
pm2 logs [service-name] --lines 20
```

**Common Fixes:**
1. **Import Error:** Check and fix module imports
2. **Config Error:** Verify PM2 ecosystem configuration
3. **Permission Error:** Check file and directory permissions
4. **Port Conflict:** Verify ports are available

### Guide 2: API Endpoints Return 404

**Symptoms:** Valid endpoints return 404 Not Found
**Quick Diagnosis:**
```bash
curl -v http://localhost:8000/api/v1/roi/calculate
```

**Common Fixes:**
1. **Router Prefix:** Remove duplicate prefixes
2. **Import Error:** Check router imports in main application
3. **Service Down:** Verify backend service is running

### Guide 3: Database Connection Failures

**Symptoms:** Database-related errors in logs
**Quick Diagnosis:**
```bash
sudo -u postgres psql -c "SELECT 1;"
```

**Common Fixes:**
1. **Service Down:** Start PostgreSQL service
2. **Connection String:** Verify DATABASE_URL
3. **Permissions:** Check database user permissions
4. **Schema Missing:** Run migration scripts

### Guide 4: Frontend Build Failures

**Symptoms:** Next.js build or runtime errors
**Quick Diagnosis:**
```bash
cd soil-frontend && npm run build
```

**Common Fixes:**
1. **Dependencies:** Run `npm install`
2. **Cache Issues:** Clear `.next` directory
3. **Environment:** Check environment variables
4. **TypeScript:** Fix type errors

## 🔄 Rollback Procedures

### Emergency Rollback Steps

1. **Stop All Services:**
   ```bash
   pm2 stop all
   ```

2. **Restore Previous Version:**
   ```bash
   git checkout [previous-stable-commit]
   ```

3. **Rebuild Services:**
   ```bash
   cd soil-frontend && npm run build
   cd ../soil-backend && source venv/bin/activate
   ```

4. **Restart Services:**
   ```bash
   pm2 start ecosystem.config.js
   ```

### Database Rollback

1. **Backup Current State:**
   ```bash
   sudo -u postgres pg_dump soil_master > backup_$(date +%Y%m%d_%H%M%S).sql
   ```

2. **Restore Previous Schema:**
   ```bash
   sudo -u postgres psql -d soil_master < previous_backup.sql
   ```

## 🔗 Integration with Development Workflow

### CI/CD Pipeline Additions

**Pre-deployment Checks:**
```yaml
# .github/workflows/deploy.yml
- name: Validate Imports
  run: ./scripts/validate_imports.sh

- name: Test PM2 Configuration
  run: ./scripts/test_pm2_config.sh

- name: Validate Database Schema
  run: python scripts/validate_database.py
```

### Development Environment Updates

**Local Development Setup:**
1. Mirror production PM2 configuration
2. Use production-like database setup
3. Implement import validation in development
4. Add pre-commit hooks for configuration validation

### Automated Testing Integration

**Unit Tests for Configuration:**
```python
def test_router_configuration():
    """Test that routers don't have conflicting prefixes"""
    from app.api.v1.roi import router
    assert not router.prefix or router.prefix == ""

def test_import_resolution():
    """Test that all critical imports resolve"""
    from app.models.sensor import SensorReading, Sensor
    from app.services.roi_service import ROICalculationService
    assert True  # If imports succeed, test passes
```

## 📚 Reference Documentation

### Error Code Reference
- **E001:** Module import failures
- **E002:** PM2 configuration errors  
- **E003:** Database connection issues
- **E004:** API routing conflicts
- **E005:** Service startup failures

### Monitoring Integration
- Set up alerts for specific error patterns
- Implement health checks for all services
- Create dashboards for deployment metrics
- Add automated error reporting

## 🔧 Automated Prevention Scripts

### Script 1: Pre-Deployment Validator

```bash
#!/bin/bash
# scripts/pre_deployment_validator.sh
set -e

echo "🔍 Running Pre-Deployment Validation..."

# Check 1: Import Validation
echo "Validating Python imports..."
cd soil-backend
source venv/bin/activate
python -c "
from app.models.sensor import SensorReading, Sensor
from app.services.roi_service import ROICalculationService
from app.schemas.roi import ROICalculationRequest
from app.api.v1.roi import router
print('✅ All imports validated')
"

# Check 2: PM2 Configuration
echo "Validating PM2 configuration..."
pm2 start ecosystem.config.js --dry-run
echo "✅ PM2 configuration valid"

# Check 3: Database Connection
echo "Testing database connection..."
python -c "
import asyncio
import asyncpg
import os
async def test():
    conn = await asyncpg.connect(os.getenv('DATABASE_URL', 'postgresql://postgres@localhost/soil_master'))
    await conn.fetchval('SELECT 1')
    await conn.close()
    print('✅ Database connection successful')
asyncio.run(test())
"

# Check 4: Frontend Build
echo "Validating frontend build..."
cd ../soil-frontend
npm run build > /dev/null 2>&1
echo "✅ Frontend build successful"

echo "🎉 All pre-deployment validations passed!"
```

### Script 2: Service Health Monitor

```bash
#!/bin/bash
# scripts/health_monitor.sh

check_service() {
    local service_name=$1
    local port=$2
    local endpoint=$3

    if curl -f -s "http://localhost:${port}${endpoint}" > /dev/null; then
        echo "✅ ${service_name}: HEALTHY"
        return 0
    else
        echo "❌ ${service_name}: UNHEALTHY"
        return 1
    fi
}

echo "🏥 Service Health Check Report"
echo "=============================="

check_service "Frontend" 3000 "/"
check_service "Backend" 8000 "/health"
check_service "ROI API" 8000 "/api/v1/roi/benchmarks"

# PM2 Process Check
echo ""
echo "📊 PM2 Process Status:"
pm2 list | grep -E "(online|stopped|errored)"
```

### Script 3: Error Pattern Detector

```python
#!/usr/bin/env python3
# scripts/error_pattern_detector.py
import re
import sys
from pathlib import Path

ERROR_PATTERNS = {
    'import_error': r'ModuleNotFoundError: No module named',
    'pm2_config_error': r'npm error Missing script',
    'router_prefix_error': r'404.*roi.*calculate',
    'database_error': r'asyncpg\.exceptions\.',
    'permission_error': r'Permission denied|EACCES'
}

def analyze_logs(log_file):
    """Analyze log file for known error patterns"""
    if not Path(log_file).exists():
        print(f"❌ Log file not found: {log_file}")
        return

    with open(log_file, 'r') as f:
        content = f.read()

    found_errors = []
    for error_type, pattern in ERROR_PATTERNS.items():
        if re.search(pattern, content, re.IGNORECASE):
            found_errors.append(error_type)

    if found_errors:
        print(f"🚨 Detected error patterns in {log_file}:")
        for error in found_errors:
            print(f"   - {error}")
    else:
        print(f"✅ No known error patterns in {log_file}")

if __name__ == "__main__":
    log_files = [
        "logs/backend-error.log",
        "logs/frontend-error.log",
        "logs/soil-ai-error.log"
    ]

    for log_file in log_files:
        analyze_logs(log_file)
```

## 📋 Deployment Checklist Template

### Pre-Deployment Checklist

**Environment Preparation:**
- [ ] Ubuntu 24.04 LTS verified
- [ ] Node.js v24+ installed
- [ ] Python 3.12+ with venv
- [ ] PostgreSQL 17+ running
- [ ] PM2 globally installed

**Code Validation:**
- [ ] All imports resolve correctly
- [ ] No circular dependencies
- [ ] Router prefixes configured properly
- [ ] Database migrations tested

**Configuration Validation:**
- [ ] PM2 ecosystem.config.js syntax valid
- [ ] Environment variables set
- [ ] Log directories exist with permissions
- [ ] Database connection strings correct

**Service Testing:**
- [ ] Frontend builds successfully
- [ ] Backend starts without errors
- [ ] Database migrations complete
- [ ] All API endpoints accessible

### Post-Deployment Verification

**Service Status:**
- [ ] All PM2 processes online
- [ ] No error logs in past 5 minutes
- [ ] Memory usage within normal ranges
- [ ] All ports accessible

**Functional Testing:**
- [ ] Frontend loads correctly
- [ ] Backend health check passes
- [ ] ROI calculation API responds
- [ ] Database queries execute

**Performance Validation:**
- [ ] Response times < 1 second
- [ ] No memory leaks detected
- [ ] CPU usage stable
- [ ] Database connections stable

## 🎯 Lessons Learned & Best Practices

### Key Insights from v1.0.3 Deployment

1. **Import Path Consistency:** Always verify import paths match actual file structure
2. **Service-Specific Configuration:** Use appropriate process managers for each technology
3. **Router Architecture:** Avoid duplicate prefixes in nested router configurations
4. **Database Schema Validation:** Test migrations in production-like environments
5. **Comprehensive Logging:** Implement structured logging for easier debugging

### Recommended Development Practices

1. **Mirror Production Locally:** Development environments should match production
2. **Automated Testing:** Include configuration and import validation in CI/CD
3. **Incremental Deployment:** Deploy and test services individually
4. **Rollback Readiness:** Always have a tested rollback procedure
5. **Documentation First:** Update documentation before deployment

### Future Improvements

1. **Container Orchestration:** Consider Docker for consistent environments
2. **Infrastructure as Code:** Use Terraform or Ansible for reproducible deployments
3. **Advanced Monitoring:** Implement APM tools for better observability
4. **Blue-Green Deployment:** Reduce downtime with parallel environments
5. **Automated Recovery:** Implement self-healing mechanisms

---

**📞 Support Escalation:**
For issues not covered in this runbook, escalate to the development team with:
1. Complete error logs from PM2 and application logs
2. System configuration details (OS, versions, environment)
3. Steps attempted from this runbook with results
4. Current system state information (PM2 status, service health)
5. Error pattern analysis results from automated scripts

**🔄 Document Updates:**
This runbook should be updated after each deployment with new error patterns and solutions discovered.
