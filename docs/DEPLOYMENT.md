# Soil Master v1.0.2 Deployment Guide

This comprehensive guide covers production deployment of Soil Master on Ubuntu Server 24.04 LTS with enterprise-grade configuration.

## 📋 Prerequisites

### System Requirements

**Minimum Requirements**
- **OS**: Ubuntu Server 24.04 LTS
- **CPU**: 4 cores (2.4GHz+)
- **Memory**: 8GB RAM
- **Storage**: 50GB SSD
- **Network**: 100Mbps bandwidth

**Recommended Production**
- **OS**: Ubuntu Server 24.04 LTS
- **CPU**: 8+ cores (Intel Xeon or AMD EPYC)
- **Memory**: 32GB+ RAM
- **Storage**: 500GB+ NVMe SSD
- **Network**: 1Gbps+ bandwidth

### Software Dependencies

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install required packages
sudo apt install -y \
    python3.11 python3.11-venv python3-pip \
    nodejs npm \
    postgresql-15 postgresql-contrib postgresql-15-postgis-3 \
    redis-server \
    nginx \
    git curl wget unzip \
    build-essential \
    software-properties-common
```

## 🚀 Quick Deployment

### Automated Deployment

```bash
# Clone repository
git clone https://github.com/Yield-Sight-System/soil-master.git
cd soil-master

# Run automated deployment
sudo ./deployment/production/deploy.sh
```

### Manual Deployment Steps

If you prefer manual control over the deployment process:

#### 1. Database Setup

```bash
# Configure PostgreSQL
sudo -u postgres createdb soil_master
sudo -u postgres createuser soil_master
sudo -u postgres psql -c "ALTER USER soil_master WITH PASSWORD 'your_secure_password';"
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE soil_master TO soil_master;"

# Enable PostGIS
sudo -u postgres psql -d soil_master -c "CREATE EXTENSION postgis;"
sudo -u postgres psql -d soil_master -c "CREATE EXTENSION postgis_topology;"
```

#### 2. Backend Deployment

```bash
# Navigate to backend directory
cd soil-backend

# Create virtual environment
python3.11 -m venv venv
source venv/bin/activate

# Install dependencies
pip install --upgrade pip
pip install -r requirements.txt

# Configure environment
cp .env.example .env
# Edit .env with your configuration

# Run database migrations
alembic upgrade head

# Test backend
python -m pytest tests/ -v
```

#### 3. Frontend Deployment

```bash
# Navigate to frontend directory
cd ../soil-frontend

# Install dependencies
npm ci --production=false

# Configure environment
cp .env.example .env.production
# Edit .env.production with your configuration

# Run tests
npm test -- --watchAll=false

# Build production bundle
npm run build
```

#### 4. Service Configuration

```bash
# Copy systemd service files
sudo cp deployment/production/systemd/*.service /etc/systemd/system/

# Reload systemd and enable services
sudo systemctl daemon-reload
sudo systemctl enable soil-master-backend
sudo systemctl enable soil-master-frontend

# Start services
sudo systemctl start soil-master-backend
sudo systemctl start soil-master-frontend
```

#### 5. Nginx Configuration

```bash
# Copy Nginx configuration
sudo cp deployment/production/nginx.conf /etc/nginx/sites-available/soil-master

# Enable site
sudo ln -s /etc/nginx/sites-available/soil-master /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default

# Test and reload Nginx
sudo nginx -t
sudo systemctl reload nginx
```

## 🔧 Configuration

### Environment Variables

#### Backend (.env)

```bash
# Database Configuration
DATABASE_URL=postgresql://soil_master:your_password@localhost:5432/soil_master
REDIS_URL=redis://localhost:6379

# Security
SECRET_KEY=your-super-secret-key-here
JWT_SECRET=your-jwt-secret-here
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=30

# Application
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=info

# AI/ML Configuration
MODEL_PATH=/opt/soilmaster/models
PREDICTION_CACHE_TTL=3600
GPU_ENABLED=false

# Demo Configuration
DEMO_MODE=true
DEMO_SCENARIOS_PATH=/opt/soilmaster/demo-data
DEMO_CACHE_TTL=300

# Monitoring
PROMETHEUS_ENABLED=true
METRICS_PORT=8001

# Email Configuration
SMTP_HOST=localhost
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_smtp_password
```

#### Frontend (.env.production)

```bash
# API Configuration
NEXT_PUBLIC_API_BASE_URL=https://api.soilmaster.com
NEXT_PUBLIC_WS_URL=wss://api.soilmaster.com/ws

# Demo Configuration
NEXT_PUBLIC_DEMO_MODE=true
NEXT_PUBLIC_DEMO_AUTO_REFRESH=true
NEXT_PUBLIC_DEMO_REFRESH_INTERVAL=30000

# Monitoring
NEXT_PUBLIC_MONITORING_ENABLED=true
NEXT_PUBLIC_SENTRY_DSN=your_sentry_dsn

# Maps Configuration
NEXT_PUBLIC_MAPBOX_TOKEN=your_mapbox_token
NEXT_PUBLIC_DEFAULT_MAP_CENTER=[3.1390,101.6869]
NEXT_PUBLIC_DEFAULT_MAP_ZOOM=10

# Performance
NEXT_PUBLIC_CACHE_ENABLED=true
NEXT_PUBLIC_CDN_URL=https://cdn.soilmaster.com
```

### Database Configuration

#### PostgreSQL Tuning

```sql
-- /etc/postgresql/15/main/postgresql.conf

# Memory settings
shared_buffers = 8GB                    # 25% of RAM
effective_cache_size = 24GB             # 75% of RAM
work_mem = 256MB
maintenance_work_mem = 2GB

# Connection settings
max_connections = 200
max_prepared_transactions = 200

# Performance settings
random_page_cost = 1.1                  # For SSD
effective_io_concurrency = 200          # For SSD
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100

# Logging
log_statement = 'ddl'
log_min_duration_statement = 1000
log_connections = on
log_disconnections = on

# Security
ssl = on
ssl_cert_file = '/etc/ssl/certs/ssl-cert-snakeoil.pem'
ssl_key_file = '/etc/ssl/private/ssl-cert-snakeoil.key'
```

#### Redis Configuration

```bash
# /etc/redis/redis.conf

# Memory management
maxmemory 4gb
maxmemory-policy allkeys-lru

# Persistence
save 900 1
save 300 10
save 60 10000

# Security
requirepass your_redis_password
bind 127.0.0.1

# Performance
tcp-keepalive 300
timeout 0
```

## 🔒 Security Configuration

### SSL/TLS Setup

```bash
# Install Certbot
sudo snap install --classic certbot

# Obtain SSL certificate
sudo certbot --nginx -d soilmaster.com -d www.soilmaster.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### Firewall Configuration

```bash
# Configure UFW
sudo ufw default deny incoming
sudo ufw default allow outgoing

# Allow SSH, HTTP, HTTPS
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# Allow application ports (localhost only)
sudo ufw allow from 127.0.0.1 to any port 8000
sudo ufw allow from 127.0.0.1 to any port 3000

# Enable firewall
sudo ufw enable
```

### Security Hardening

```bash
# Run security hardening script
sudo ./security/security-hardening.sh

# Configure Fail2Ban
sudo systemctl enable fail2ban
sudo systemctl start fail2ban

# Check security status
sudo ./security/audit.sh
```

## 📊 Monitoring Setup

### Prometheus and Grafana

```bash
# Install monitoring stack
./monitoring/setup.sh install

# Start monitoring services
./monitoring/setup.sh start

# Access dashboards
# Grafana: http://localhost:3001 (admin/admin)
# Prometheus: http://localhost:9090
```

### Log Management

```bash
# Configure log rotation
sudo tee /etc/logrotate.d/soilmaster << EOF
/var/log/soilmaster/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 soilmaster soilmaster
    postrotate
        systemctl reload soil-master-backend
        systemctl reload soil-master-frontend
    endscript
}
EOF
```

## 🧪 Testing Deployment

### Health Checks

```bash
# Run comprehensive health check
./deployment/production/health-check.sh

# Check individual services
systemctl status soil-master-backend
systemctl status soil-master-frontend
systemctl status nginx
systemctl status postgresql
systemctl status redis
```

### Performance Testing

```bash
# Run load tests
./tests/performance/run_load_tests.sh

# Demo performance validation
./tests/demo/validate_performance.sh

# Database performance test
./tests/performance/db_benchmark.sh
```

### Smoke Tests

```bash
# API smoke tests
curl -f http://localhost/api/v1/health
curl -f http://localhost/api/v1/demo/scenarios

# Frontend smoke test
curl -f http://localhost/

# Demo system test
curl -f http://localhost/demo
```

## 🔄 Maintenance

### Backup Procedures

```bash
# Database backup
sudo -u postgres pg_dump soil_master > backup_$(date +%Y%m%d_%H%M%S).sql

# Application backup
tar -czf app_backup_$(date +%Y%m%d_%H%M%S).tar.gz \
    /opt/soilmaster \
    /etc/nginx/sites-available/soil-master \
    /etc/systemd/system/soil-master-*

# Automated backup script
sudo crontab -e
# Add: 0 2 * * * /opt/soilmaster/scripts/backup.sh
```

### Updates and Patches

```bash
# System updates
sudo apt update && sudo apt upgrade -y

# Application updates
git pull origin main
./deployment/production/deploy.sh

# Security updates (automatic)
sudo dpkg-reconfigure -plow unattended-upgrades
```

### Log Monitoring

```bash
# Monitor application logs
sudo journalctl -u soil-master-backend -f
sudo journalctl -u soil-master-frontend -f

# Monitor Nginx logs
sudo tail -f /var/log/nginx/soil_master_access.log
sudo tail -f /var/log/nginx/soil_master_error.log

# Monitor system logs
sudo tail -f /var/log/syslog
```

## 🚨 Troubleshooting

### Common Issues

#### Service Won't Start

```bash
# Check service status
systemctl status soil-master-backend

# Check logs
journalctl -u soil-master-backend -n 50

# Check configuration
sudo nginx -t
python -c "import app.main"
```

#### Database Connection Issues

```bash
# Check PostgreSQL status
systemctl status postgresql

# Test connection
sudo -u postgres psql -d soil_master -c "SELECT 1;"

# Check configuration
sudo -u postgres psql -c "SHOW config_file;"
```

#### Performance Issues

```bash
# Check system resources
htop
iotop
nethogs

# Check database performance
sudo -u postgres psql -d soil_master -c "SELECT * FROM pg_stat_activity;"

# Check cache performance
redis-cli info stats
```

### Emergency Procedures

#### Rollback Deployment

```bash
# Emergency rollback
./deployment/production/deploy.sh rollback

# Manual rollback
sudo systemctl stop soil-master-backend soil-master-frontend
# Restore from backup
sudo systemctl start soil-master-backend soil-master-frontend
```

#### Database Recovery

```bash
# Stop application
sudo systemctl stop soil-master-backend

# Restore database
sudo -u postgres psql -d soil_master < backup_file.sql

# Start application
sudo systemctl start soil-master-backend
```

## 📞 Support

### Getting Help

- **Documentation**: https://docs.soilmaster.com
- **Issues**: https://github.com/Yield-Sight-System/soil-master/issues
- **Email**: <EMAIL>

### Enterprise Support

For enterprise deployments:
- **24/7 Support**: <EMAIL>
- **Professional Services**: <EMAIL>
- **Training**: <EMAIL>

---

**Deployment Guide v1.0.2** - Last updated: 2024-01-15
