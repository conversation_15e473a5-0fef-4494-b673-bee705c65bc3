# Soil Master v1.0.2 Troubleshooting Guide

## Overview

This comprehensive troubleshooting guide provides solutions for common issues encountered in the Soil Master production environment. It covers system diagnostics, performance optimization, and emergency recovery procedures.

## Quick Diagnostic Commands

### System Health Check

```bash
#!/bin/bash
# Quick system health check script

echo "=== Soil Master System Health Check ==="
echo "Timestamp: $(date)"
echo

# Check system resources
echo "--- System Resources ---"
echo "CPU Usage: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)%"
echo "Memory Usage: $(free | grep Mem | awk '{printf("%.1f%%", $3/$2 * 100.0)}')"
echo "Disk Usage: $(df -h / | awk 'NR==2{printf "%s", $5}')"
echo

# Check services
echo "--- Service Status ---"
services=("nginx" "postgresql" "redis-server" "prometheus" "grafana-server")
for service in "${services[@]}"; do
    if systemctl is-active --quiet "$service"; then
        echo "✅ $service: Running"
    else
        echo "❌ $service: Not running"
    fi
done

# Check PM2 processes
echo
echo "--- PM2 Processes ---"
pm2 jlist | jq -r '.[] | "\(.name): \(.pm2_env.status)"'

# Check application health
echo
echo "--- Application Health ---"
curl -s -o /dev/null -w "Backend Health: %{http_code}\n" http://localhost:8000/health
curl -s -o /dev/null -w "Frontend Health: %{http_code}\n" http://localhost:3000

echo
echo "=== Health Check Complete ==="
```

### Performance Monitoring

```bash
# Real-time performance monitoring
watch -n 1 'echo "=== Performance Monitor ==="; 
echo "CPU: $(top -bn1 | grep "Cpu(s)" | awk "{print \$2}")"; 
echo "Memory: $(free -h | grep Mem | awk "{print \$3\"/\"\$2}")"; 
echo "Load: $(uptime | awk -F"load average:" "{print \$2}")"; 
echo "Connections: $(ss -tuln | wc -l)"; 
echo "PM2 Status:"; pm2 status'
```

## Common Issues and Solutions

### 1. Application Startup Issues

#### Issue: Backend Service Won't Start

**Symptoms**:
- PM2 shows backend as "errored" or "stopped"
- Health check returns connection refused
- Application logs show startup errors

**Diagnostic Steps**:
```bash
# Check PM2 status
pm2 status

# View backend logs
pm2 logs soil-master-backend --lines 50

# Check database connectivity
psql -h localhost -U soil_master -d soil_master -c "SELECT version();"

# Check Redis connectivity
redis-cli -a your_redis_password ping
```

**Common Solutions**:

1. **Database Connection Issues**:
   ```bash
   # Check PostgreSQL status
   sudo systemctl status postgresql
   
   # Restart PostgreSQL if needed
   sudo systemctl restart postgresql
   
   # Verify database credentials in .env file
   cat /opt/soilmaster/soil-backend/.env | grep DATABASE_URL
   ```

2. **Redis Connection Issues**:
   ```bash
   # Check Redis status
   sudo systemctl status redis-server
   
   # Restart Redis if needed
   sudo systemctl restart redis-server
   
   # Test Redis authentication
   redis-cli -a your_redis_password ping
   ```

3. **Environment Configuration**:
   ```bash
   # Verify environment file exists
   ls -la /opt/soilmaster/soil-backend/.env
   
   # Check for missing environment variables
   grep -E "(DATABASE_URL|REDIS_URL|SECRET_KEY)" /opt/soilmaster/soil-backend/.env
   ```

4. **Python Dependencies**:
   ```bash
   # Activate virtual environment
   source /opt/soilmaster/venv/bin/activate
   
   # Reinstall dependencies
   cd /opt/soilmaster/soil-backend
   pip install -r requirements.txt
   ```

#### Issue: Frontend Service Won't Start

**Symptoms**:
- PM2 shows frontend as "errored" or "stopped"
- Port 3000 not responding
- Build errors in logs

**Diagnostic Steps**:
```bash
# Check PM2 status
pm2 status

# View frontend logs
pm2 logs soil-master-frontend --lines 50

# Check Node.js version
node --version

# Check build status
ls -la /opt/soilmaster/soil-frontend/.next/
```

**Common Solutions**:

1. **Build Issues**:
   ```bash
   cd /opt/soilmaster/soil-frontend
   
   # Clean and rebuild
   rm -rf .next node_modules
   npm ci
   npm run build
   ```

2. **Port Conflicts**:
   ```bash
   # Check what's using port 3000
   sudo lsof -i :3000
   
   # Kill conflicting process if needed
   sudo kill -9 <PID>
   ```

3. **Memory Issues**:
   ```bash
   # Increase Node.js memory limit
   pm2 restart soil-master-frontend --node-args="--max-old-space-size=2048"
   ```

### 2. Database Issues

#### Issue: Database Connection Timeouts

**Symptoms**:
- Slow query responses
- Connection timeout errors
- High database CPU usage

**Diagnostic Steps**:
```bash
# Check database connections
sudo -u postgres psql -c "SELECT count(*) FROM pg_stat_activity;"

# Check slow queries
sudo -u postgres psql -d soil_master -c "
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;"

# Check database size
sudo -u postgres psql -c "
SELECT pg_size_pretty(pg_database_size('soil_master'));"
```

**Solutions**:

1. **Connection Pool Optimization**:
   ```bash
   # Edit PostgreSQL configuration
   sudo nano /etc/postgresql/15/main/postgresql.conf
   
   # Increase connection limits
   max_connections = 200
   shared_buffers = 256MB
   effective_cache_size = 1GB
   
   # Restart PostgreSQL
   sudo systemctl restart postgresql
   ```

2. **Query Optimization**:
   ```sql
   -- Analyze database statistics
   ANALYZE;
   
   -- Reindex if needed
   REINDEX DATABASE soil_master;
   
   -- Check for missing indexes
   SELECT schemaname, tablename, attname, n_distinct, correlation 
   FROM pg_stats 
   WHERE schemaname = 'public' 
   ORDER BY n_distinct DESC;
   ```

#### Issue: Database Corruption

**Symptoms**:
- Data integrity errors
- Unexpected query results
- Database startup failures

**Emergency Recovery**:
```bash
# Stop application services
pm2 stop all

# Check database integrity
sudo -u postgres pg_dump soil_master > /tmp/integrity_check.sql

# If corruption detected, restore from backup
sudo /opt/soilmaster/backup/restore-database.sh --backup-date=latest

# Restart services
pm2 start ecosystem.config.js
```

### 3. Performance Issues

#### Issue: Slow Demo Response Times

**Symptoms**:
- Heatmap generation takes >2 seconds
- Scenario switching is slow
- Performance monitor shows high response times

**Diagnostic Steps**:
```bash
# Check demo API performance
curl -w "@curl-format.txt" -o /dev/null -s "http://localhost:8000/api/v1/demo/heatmap?scenario_id=scenario-1&parameter=soil_nitrogen"

# Monitor Redis cache performance
redis-cli --latency-history -i 1

# Check database query performance
sudo -u postgres psql -d soil_master -c "
SELECT query, mean_time, calls, total_time
FROM pg_stat_statements 
WHERE query LIKE '%demo%' 
ORDER BY mean_time DESC;"
```

**Solutions**:

1. **Cache Optimization**:
   ```bash
   # Check Redis memory usage
   redis-cli info memory
   
   # Optimize Redis configuration
   redis-cli config set maxmemory 512mb
   redis-cli config set maxmemory-policy allkeys-lru
   
   # Clear cache if needed
   redis-cli flushall
   ```

2. **Database Optimization**:
   ```sql
   -- Update table statistics
   ANALYZE scenarios;
   ANALYZE soil_data;
   ANALYZE demo_sessions;
   
   -- Check for slow queries
   SELECT query, mean_time 
   FROM pg_stat_statements 
   WHERE mean_time > 1000 
   ORDER BY mean_time DESC;
   ```

3. **Application Optimization**:
   ```bash
   # Restart PM2 processes to clear memory
   pm2 restart ecosystem.config.js
   
   # Monitor memory usage
   pm2 monit
   ```

### 4. SSL/TLS Issues

#### Issue: SSL Certificate Problems

**Symptoms**:
- HTTPS connections fail
- Browser security warnings
- Certificate expiry alerts

**Diagnostic Steps**:
```bash
# Check certificate status
sudo certbot certificates

# Test SSL configuration
openssl s_client -connect yourdomain.com:443 -servername yourdomain.com

# Check certificate expiry
openssl x509 -in /etc/letsencrypt/live/yourdomain.com/cert.pem -enddate -noout
```

**Solutions**:

1. **Certificate Renewal**:
   ```bash
   # Manual renewal
   sudo certbot renew --force-renewal
   
   # Test automatic renewal
   sudo certbot renew --dry-run
   
   # Restart Nginx
   sudo systemctl reload nginx
   ```

2. **Configuration Issues**:
   ```bash
   # Test Nginx configuration
   sudo nginx -t
   
   # Check SSL configuration
   sudo nano /etc/nginx/sites-available/soil-master.conf
   
   # Reload Nginx
   sudo systemctl reload nginx
   ```

### 5. Monitoring System Issues

#### Issue: Prometheus Not Collecting Metrics

**Symptoms**:
- Grafana dashboards show no data
- Prometheus targets down
- Missing metrics in queries

**Diagnostic Steps**:
```bash
# Check Prometheus status
sudo systemctl status prometheus

# Check Prometheus targets
curl http://localhost:9090/api/v1/targets

# Check metrics endpoint
curl http://localhost:8000/metrics
```

**Solutions**:

1. **Service Configuration**:
   ```bash
   # Restart Prometheus
   sudo systemctl restart prometheus
   
   # Check configuration
   sudo promtool check config /etc/prometheus/prometheus.yml
   
   # Reload configuration
   sudo systemctl reload prometheus
   ```

2. **Target Configuration**:
   ```yaml
   # Edit Prometheus configuration
   sudo nano /etc/prometheus/prometheus.yml
   
   # Verify target endpoints
   - job_name: 'soil-master-backend'
     static_configs:
       - targets: ['localhost:8000']
   ```

#### Issue: Grafana Dashboard Issues

**Symptoms**:
- Dashboards not loading
- No data in panels
- Authentication problems

**Solutions**:

1. **Service Issues**:
   ```bash
   # Restart Grafana
   sudo systemctl restart grafana-server
   
   # Check logs
   sudo tail -f /var/log/grafana/grafana.log
   ```

2. **Data Source Issues**:
   ```bash
   # Test Prometheus connection
   curl http://localhost:9090/api/v1/query?query=up
   
   # Reconfigure data source in Grafana UI
   # Navigate to Configuration > Data Sources > Prometheus
   ```

## Emergency Procedures

### System Recovery

#### Complete System Failure

1. **Immediate Assessment**:
   ```bash
   # Check system status
   systemctl status
   
   # Check disk space
   df -h
   
   # Check memory
   free -h
   
   # Check processes
   ps aux | grep -E "(nginx|postgres|redis|pm2)"
   ```

2. **Service Recovery**:
   ```bash
   # Start essential services
   sudo systemctl start postgresql
   sudo systemctl start redis-server
   sudo systemctl start nginx
   
   # Start application
   pm2 start ecosystem.config.js
   ```

3. **Data Recovery** (if needed):
   ```bash
   # Run disaster recovery script
   sudo /opt/soilmaster/backup/disaster-recovery-restore.sh --backup-date=latest
   ```

#### Database Emergency Recovery

1. **Stop All Services**:
   ```bash
   pm2 stop all
   sudo systemctl stop nginx
   ```

2. **Assess Database State**:
   ```bash
   sudo systemctl status postgresql
   sudo -u postgres pg_isready
   ```

3. **Restore from Backup**:
   ```bash
   # Find latest backup
   ls -la /opt/soilmaster/backups/database/
   
   # Restore database
   sudo /opt/soilmaster/backup/restore-database.sh --backup-file=<latest_backup>
   ```

4. **Restart Services**:
   ```bash
   sudo systemctl start nginx
   pm2 start ecosystem.config.js
   ```

### Performance Emergency

#### High CPU Usage

1. **Identify Process**:
   ```bash
   top -p $(pgrep -d',' -f soil-master)
   ```

2. **Immediate Relief**:
   ```bash
   # Restart high-usage processes
   pm2 restart soil-master-backend
   pm2 restart soil-master-frontend
   ```

3. **Long-term Solution**:
   ```bash
   # Scale PM2 instances
   pm2 scale soil-master-backend +2
   ```

#### Memory Issues

1. **Check Memory Usage**:
   ```bash
   free -h
   ps aux --sort=-%mem | head -10
   ```

2. **Clear Caches**:
   ```bash
   # Clear system cache
   sudo sync && sudo sysctl vm.drop_caches=3
   
   # Clear Redis cache
   redis-cli flushall
   
   # Restart PM2 processes
   pm2 restart ecosystem.config.js
   ```

## Monitoring and Alerting

### Key Metrics to Monitor

1. **System Metrics**:
   - CPU usage < 80%
   - Memory usage < 85%
   - Disk usage < 90%
   - Load average < number of cores

2. **Application Metrics**:
   - Response time < 1 second
   - Error rate < 1%
   - Throughput > 100 requests/minute
   - Cache hit rate > 80%

3. **Database Metrics**:
   - Connection count < 150
   - Query time < 500ms
   - Lock wait time < 100ms
   - Database size growth rate

### Alert Thresholds

```yaml
# Critical Alerts (Immediate Response)
- CPU usage > 90% for 5 minutes
- Memory usage > 95% for 2 minutes
- Disk usage > 95%
- Application down for 1 minute
- Database connection failures

# Warning Alerts (Response within 1 hour)
- CPU usage > 80% for 15 minutes
- Memory usage > 85% for 10 minutes
- Response time > 2 seconds
- Error rate > 5%
- Cache hit rate < 60%
```

## Support Escalation

### Level 1: Self-Service

- Check this troubleshooting guide
- Review system logs
- Restart affected services
- Check monitoring dashboards

### Level 2: Technical Support

**Contact**: <EMAIL>  
**Phone**: ******-SOIL-MASTER  
**Response Time**: 2 hours for critical issues

**Information to Provide**:
- Error messages and logs
- System performance metrics
- Steps taken to resolve
- Business impact assessment

### Level 3: Emergency Response

**Contact**: <EMAIL>  
**Phone**: ******-EMERGENCY  
**Response Time**: 30 minutes for production down

**Escalation Criteria**:
- Complete system failure
- Data corruption or loss
- Security breach
- Customer-facing service down

---

**Document Version**: 1.0.2  
**Last Updated**: $(date)  
**Emergency Contact**: <EMAIL>  
**Maintained by**: Soil Master Operations Team
