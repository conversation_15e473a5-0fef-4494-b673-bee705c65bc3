#!/bin/bash

# Production Monitoring Script for Soil Master v1.0.2
# Comprehensive health monitoring and alerting

set -euo pipefail

# Configuration
DEPLOY_USER="soilmaster"
APP_DIR="/opt/soilmaster"
BACKEND_DIR="$APP_DIR/backend"
LOG_DIR="/var/log/soilmaster"
ALERT_EMAIL="${ALERT_EMAIL:-<EMAIL>}"
SLACK_WEBHOOK="${SLACK_WEBHOOK:-}"

# Thresholds
CPU_THRESHOLD=80
MEMORY_THRESHOLD=85
DISK_THRESHOLD=90
RESPONSE_TIME_THRESHOLD=2000  # milliseconds
ERROR_RATE_THRESHOLD=5        # percentage

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}" >&2
}

warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

# Check system resources
check_system_resources() {
    local status="OK"
    local alerts=()
    
    # CPU usage
    local cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)
    cpu_usage=${cpu_usage%.*}  # Remove decimal part
    
    if [ "$cpu_usage" -gt "$CPU_THRESHOLD" ]; then
        alerts+=("High CPU usage: ${cpu_usage}%")
        status="CRITICAL"
    fi
    
    # Memory usage
    local memory_info=$(free | grep Mem)
    local total_mem=$(echo $memory_info | awk '{print $2}')
    local used_mem=$(echo $memory_info | awk '{print $3}')
    local memory_usage=$((used_mem * 100 / total_mem))
    
    if [ "$memory_usage" -gt "$MEMORY_THRESHOLD" ]; then
        alerts+=("High memory usage: ${memory_usage}%")
        status="CRITICAL"
    fi
    
    # Disk usage
    local disk_usage=$(df / | tail -1 | awk '{print $5}' | cut -d'%' -f1)
    
    if [ "$disk_usage" -gt "$DISK_THRESHOLD" ]; then
        alerts+=("High disk usage: ${disk_usage}%")
        status="CRITICAL"
    fi
    
    echo "SYSTEM_RESOURCES:$status:CPU=${cpu_usage}%,Memory=${memory_usage}%,Disk=${disk_usage}%"
    
    for alert in "${alerts[@]}"; do
        echo "ALERT:$alert"
    done
}

# Check services
check_services() {
    local status="OK"
    local alerts=()
    
    # Check PostgreSQL
    if ! sudo systemctl is-active --quiet postgresql; then
        alerts+=("PostgreSQL service is down")
        status="CRITICAL"
    fi
    
    # Check Redis
    if ! sudo systemctl is-active --quiet redis-server; then
        alerts+=("Redis service is down")
        status="CRITICAL"
    fi
    
    # Check Nginx
    if ! sudo systemctl is-active --quiet nginx; then
        alerts+=("Nginx service is down")
        status="CRITICAL"
    fi
    
    # Check backend application
    if ! sudo -u "$DEPLOY_USER" pm2 list | grep -q "online"; then
        alerts+=("Backend application is down")
        status="CRITICAL"
    fi
    
    echo "SERVICES:$status"
    
    for alert in "${alerts[@]}"; do
        echo "ALERT:$alert"
    done
}

# Check application health
check_application_health() {
    local status="OK"
    local alerts=()
    
    # Check API health endpoint
    local start_time=$(date +%s%3N)
    local http_code=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/health || echo "000")
    local end_time=$(date +%s%3N)
    local response_time=$((end_time - start_time))
    
    if [ "$http_code" != "200" ]; then
        alerts+=("API health check failed (HTTP $http_code)")
        status="CRITICAL"
    elif [ "$response_time" -gt "$RESPONSE_TIME_THRESHOLD" ]; then
        alerts+=("API response time too high: ${response_time}ms")
        status="WARNING"
    fi
    
    # Check database connectivity
    if ! sudo -u postgres psql -d soilmaster_prod -c "SELECT 1;" > /dev/null 2>&1; then
        alerts+=("Database connectivity failed")
        status="CRITICAL"
    fi
    
    # Check Redis connectivity
    if ! redis-cli ping > /dev/null 2>&1; then
        alerts+=("Redis connectivity failed")
        status="CRITICAL"
    fi
    
    echo "APPLICATION:$status:ResponseTime=${response_time}ms"
    
    for alert in "${alerts[@]}"; do
        echo "ALERT:$alert"
    done
}

# Check log errors
check_log_errors() {
    local status="OK"
    local alerts=()
    
    # Check for recent errors in backend logs
    local error_count=$(sudo tail -n 1000 "$LOG_DIR/backend-error.log" 2>/dev/null | grep -c "$(date '+%Y-%m-%d')" || echo "0")
    
    if [ "$error_count" -gt 10 ]; then
        alerts+=("High error count in backend logs: $error_count")
        status="WARNING"
    fi
    
    # Check for critical errors
    local critical_errors=$(sudo tail -n 100 "$LOG_DIR/backend-error.log" 2>/dev/null | grep -i "critical\|fatal" | wc -l || echo "0")
    
    if [ "$critical_errors" -gt 0 ]; then
        alerts+=("Critical errors found in logs: $critical_errors")
        status="CRITICAL"
    fi
    
    echo "LOGS:$status:Errors=$error_count,Critical=$critical_errors"
    
    for alert in "${alerts[@]}"; do
        echo "ALERT:$alert"
    done
}

# Check SSL certificate
check_ssl_certificate() {
    local status="OK"
    local alerts=()
    local domain="${DOMAIN:-localhost}"
    
    if [ "$domain" != "localhost" ]; then
        # Check certificate expiration
        local cert_expiry=$(echo | openssl s_client -servername "$domain" -connect "$domain:443" 2>/dev/null | openssl x509 -noout -dates | grep notAfter | cut -d= -f2)
        local expiry_timestamp=$(date -d "$cert_expiry" +%s)
        local current_timestamp=$(date +%s)
        local days_until_expiry=$(( (expiry_timestamp - current_timestamp) / 86400 ))
        
        if [ "$days_until_expiry" -lt 7 ]; then
            alerts+=("SSL certificate expires in $days_until_expiry days")
            status="CRITICAL"
        elif [ "$days_until_expiry" -lt 30 ]; then
            alerts+=("SSL certificate expires in $days_until_expiry days")
            status="WARNING"
        fi
        
        echo "SSL:$status:ExpiresIn=${days_until_expiry}days"
    else
        echo "SSL:SKIPPED:LocalhostDomain"
    fi
    
    for alert in "${alerts[@]}"; do
        echo "ALERT:$alert"
    done
}

# Send alert
send_alert() {
    local message="$1"
    local severity="$2"
    
    # Email alert
    if command -v mail >/dev/null 2>&1 && [ -n "$ALERT_EMAIL" ]; then
        echo "$message" | mail -s "Soil Master Alert [$severity]" "$ALERT_EMAIL"
    fi
    
    # Slack alert
    if [ -n "$SLACK_WEBHOOK" ]; then
        local color="danger"
        [ "$severity" = "WARNING" ] && color="warning"
        
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"attachments\":[{\"color\":\"$color\",\"title\":\"Soil Master Alert\",\"text\":\"$message\",\"fields\":[{\"title\":\"Severity\",\"value\":\"$severity\",\"short\":true},{\"title\":\"Timestamp\",\"value\":\"$(date)\",\"short\":true}]}]}" \
            "$SLACK_WEBHOOK" 2>/dev/null || true
    fi
}

# Generate health report
generate_health_report() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local report_file="/tmp/soilmaster_health_$(date +%s).txt"
    
    {
        echo "Soil Master v1.0.2 Health Report"
        echo "Generated: $timestamp"
        echo "================================"
        echo ""
        
        echo "System Resources:"
        check_system_resources
        echo ""
        
        echo "Services Status:"
        check_services
        echo ""
        
        echo "Application Health:"
        check_application_health
        echo ""
        
        echo "Log Analysis:"
        check_log_errors
        echo ""
        
        echo "SSL Certificate:"
        check_ssl_certificate
        echo ""
        
        echo "Process Information:"
        echo "PM2 Status:"
        sudo -u "$DEPLOY_USER" pm2 list
        echo ""
        
        echo "System Load:"
        uptime
        echo ""
        
        echo "Disk Usage:"
        df -h
        echo ""
        
        echo "Memory Usage:"
        free -h
        echo ""
        
        echo "Network Connections:"
        ss -tuln | grep -E ":80|:443|:8000|:5432|:6379"
        
    } > "$report_file"
    
    echo "$report_file"
}

# Main monitoring function
main() {
    local action="${1:-check}"
    local alerts=()
    local critical_alerts=()
    local warning_alerts=()
    
    case "$action" in
        "check")
            log "Running health checks..."
            
            # Collect all check results
            local results=(
                "$(check_system_resources)"
                "$(check_services)"
                "$(check_application_health)"
                "$(check_log_errors)"
                "$(check_ssl_certificate)"
            )
            
            # Parse results and collect alerts
            for result in "${results[@]}"; do
                while IFS= read -r line; do
                    if [[ $line == ALERT:* ]]; then
                        local alert_msg="${line#ALERT:}"
                        alerts+=("$alert_msg")
                        
                        # Categorize alerts
                        if [[ $result == *"CRITICAL"* ]]; then
                            critical_alerts+=("$alert_msg")
                        else
                            warning_alerts+=("$alert_msg")
                        fi
                    fi
                done <<< "$result"
            done
            
            # Display results
            if [ ${#critical_alerts[@]} -gt 0 ]; then
                error "CRITICAL ALERTS DETECTED:"
                for alert in "${critical_alerts[@]}"; do
                    error "  - $alert"
                done
                
                # Send critical alert
                local critical_message="Critical alerts detected on Soil Master:\n$(printf '%s\n' "${critical_alerts[@]}")"
                send_alert "$critical_message" "CRITICAL"
            fi
            
            if [ ${#warning_alerts[@]} -gt 0 ]; then
                warning "WARNING ALERTS:"
                for alert in "${warning_alerts[@]}"; do
                    warning "  - $alert"
                done
            fi
            
            if [ ${#alerts[@]} -eq 0 ]; then
                log "✓ All health checks passed"
            fi
            
            # Display summary
            echo ""
            log "Health Check Summary:"
            for result in "${results[@]}"; do
                local status_line=$(echo "$result" | head -n1)
                echo "  $status_line"
            done
            ;;
            
        "report")
            log "Generating comprehensive health report..."
            local report_file=$(generate_health_report)
            log "Health report generated: $report_file"
            cat "$report_file"
            ;;
            
        "watch")
            log "Starting continuous monitoring (Ctrl+C to stop)..."
            while true; do
                clear
                echo "Soil Master v1.0.2 - Live Monitoring"
                echo "Last updated: $(date)"
                echo "=================================="
                echo ""
                
                main check
                
                echo ""
                echo "Refreshing in 30 seconds..."
                sleep 30
            done
            ;;
            
        *)
            echo "Usage: $0 [check|report|watch]"
            echo "  check  - Run health checks (default)"
            echo "  report - Generate comprehensive health report"
            echo "  watch  - Continuous monitoring"
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
