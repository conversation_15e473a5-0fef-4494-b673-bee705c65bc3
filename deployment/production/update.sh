#!/bin/bash

# Production Update Script for Soil Master v1.0.2
# Zero-downtime updates with rollback capability

set -euo pipefail

# Configuration
DEPLOY_USER="soilmaster"
APP_DIR="/opt/soilmaster"
BACKEND_DIR="$APP_DIR/backend"
FRONTEND_DIR="$APP_DIR/frontend"
AI_DIR="$APP_DIR/soil-ai"
BACKUP_DIR="$APP_DIR/backups"
LOG_DIR="/var/log/soilmaster"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}" >&2
}

warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

# Create backup
create_backup() {
    local backup_timestamp=$(date +%Y%m%d_%H%M%S)
    local backup_path="$BACKUP_DIR/backup_$backup_timestamp"
    
    log "Creating backup at $backup_path"
    
    sudo mkdir -p "$backup_path"
    
    # Backup application files
    sudo cp -r "$BACKEND_DIR" "$backup_path/"
    sudo cp -r "$FRONTEND_DIR" "$backup_path/"
    sudo cp -r "$AI_DIR" "$backup_path/"
    
    # Backup database
    sudo -u postgres pg_dump soilmaster_prod > "$backup_path/database.sql"
    
    # Backup configuration files
    sudo cp /etc/nginx/sites-available/soilmaster "$backup_path/"
    sudo cp "$BACKEND_DIR/.env" "$backup_path/" 2>/dev/null || true
    
    sudo chown -R "$DEPLOY_USER:$DEPLOY_USER" "$backup_path"
    
    echo "$backup_path" > /tmp/last_backup_path
    log "✓ Backup created at $backup_path"
}

# Update backend
update_backend() {
    log "Updating backend application..."
    
    # Copy new backend files
    if [ -d "soil-backend" ]; then
        sudo cp -r soil-backend/* "$BACKEND_DIR/"
        sudo chown -R "$DEPLOY_USER:$DEPLOY_USER" "$BACKEND_DIR"
    else
        error "Backend source directory 'soil-backend' not found"
        exit 1
    fi
    
    # Update Python dependencies
    sudo -u "$DEPLOY_USER" "$BACKEND_DIR/venv/bin/pip" install --upgrade -r "$BACKEND_DIR/requirements.txt"
    
    # Run database migrations
    cd "$BACKEND_DIR"
    sudo -u "$DEPLOY_USER" "$BACKEND_DIR/venv/bin/alembic" upgrade head
    
    log "✓ Backend updated"
}

# Update soil-ai
update_soil_ai() {
    log "Updating soil-ai module..."
    
    if [ -d "soil-ai" ]; then
        sudo cp -r soil-ai/* "$AI_DIR/"
        sudo chown -R "$DEPLOY_USER:$DEPLOY_USER" "$AI_DIR"
        
        # Reinstall soil-ai module
        sudo -u "$DEPLOY_USER" "$BACKEND_DIR/venv/bin/pip" install -e "$AI_DIR"
    else
        error "Soil-AI source directory 'soil-ai' not found"
        exit 1
    fi
    
    log "✓ Soil-AI updated"
}

# Update frontend
update_frontend() {
    log "Updating frontend application..."
    
    if [ -d "soil-frontend" ]; then
        # Create temporary directory for new build
        local temp_dir="/tmp/soilmaster_frontend_$(date +%s)"
        sudo mkdir -p "$temp_dir"
        sudo cp -r soil-frontend/* "$temp_dir/"
        sudo chown -R "$DEPLOY_USER:$DEPLOY_USER" "$temp_dir"
        
        # Build new frontend
        cd "$temp_dir"
        sudo -u "$DEPLOY_USER" npm ci --production
        sudo -u "$DEPLOY_USER" npm run build
        
        # Replace old frontend with new build
        sudo rm -rf "$FRONTEND_DIR/dist.old" 2>/dev/null || true
        sudo mv "$FRONTEND_DIR/dist" "$FRONTEND_DIR/dist.old" 2>/dev/null || true
        sudo mv "$temp_dir/dist" "$FRONTEND_DIR/"
        sudo chown -R "$DEPLOY_USER:$DEPLOY_USER" "$FRONTEND_DIR"
        
        # Cleanup
        sudo rm -rf "$temp_dir"
    else
        error "Frontend source directory 'soil-frontend' not found"
        exit 1
    fi
    
    log "✓ Frontend updated"
}

# Restart services
restart_services() {
    log "Restarting services..."
    
    # Restart backend with PM2
    sudo -u "$DEPLOY_USER" pm2 restart soilmaster-backend
    
    # Wait for backend to be ready
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:8000/health > /dev/null 2>&1; then
            log "✓ Backend is ready"
            break
        fi
        
        if [ $attempt -eq $max_attempts ]; then
            error "Backend failed to start after $max_attempts attempts"
            return 1
        fi
        
        info "Waiting for backend to start (attempt $attempt/$max_attempts)..."
        sleep 2
        ((attempt++))
    done
    
    # Reload Nginx
    sudo nginx -t && sudo systemctl reload nginx
    
    log "✓ Services restarted"
}

# Verify update
verify_update() {
    log "Verifying update..."
    
    # Check backend health
    if ! curl -f http://localhost:8000/health > /dev/null 2>&1; then
        error "Backend health check failed"
        return 1
    fi
    
    # Check frontend
    if ! curl -f http://localhost/ > /dev/null 2>&1; then
        error "Frontend check failed"
        return 1
    fi
    
    # Check PM2 status
    if ! sudo -u "$DEPLOY_USER" pm2 list | grep -q "online"; then
        error "Backend process not running"
        return 1
    fi
    
    log "✓ Update verification completed"
}

# Rollback function
rollback() {
    local backup_path
    
    if [ -f /tmp/last_backup_path ]; then
        backup_path=$(cat /tmp/last_backup_path)
    else
        error "No backup path found for rollback"
        exit 1
    fi
    
    if [ ! -d "$backup_path" ]; then
        error "Backup directory not found: $backup_path"
        exit 1
    fi
    
    warning "Rolling back to backup: $backup_path"
    
    # Stop services
    sudo -u "$DEPLOY_USER" pm2 stop soilmaster-backend || true
    
    # Restore application files
    sudo cp -r "$backup_path/backend/"* "$BACKEND_DIR/"
    sudo cp -r "$backup_path/frontend/"* "$FRONTEND_DIR/"
    sudo cp -r "$backup_path/soil-ai/"* "$AI_DIR/"
    
    # Restore database
    sudo -u postgres psql soilmaster_prod < "$backup_path/database.sql"
    
    # Restore configuration
    sudo cp "$backup_path/.env" "$BACKEND_DIR/" 2>/dev/null || true
    sudo cp "$backup_path/soilmaster" /etc/nginx/sites-available/
    
    sudo chown -R "$DEPLOY_USER:$DEPLOY_USER" "$BACKEND_DIR" "$FRONTEND_DIR" "$AI_DIR"
    
    # Restart services
    restart_services
    
    log "✓ Rollback completed"
}

# Main update function
main() {
    local action="${1:-update}"
    
    case "$action" in
        "update")
            log "Starting Soil Master v1.0.2 Production Update"
            
            create_backup
            
            if update_backend && update_soil_ai && update_frontend && restart_services && verify_update; then
                log "🎉 Update completed successfully!"
                
                # Cleanup old backups (keep last 5)
                sudo find "$BACKUP_DIR" -maxdepth 1 -type d -name "backup_*" | sort -r | tail -n +6 | xargs sudo rm -rf 2>/dev/null || true
                
                log "Application is running with the latest version"
            else
                error "Update failed, initiating rollback..."
                rollback
                exit 1
            fi
            ;;
        "rollback")
            rollback
            ;;
        *)
            echo "Usage: $0 [update|rollback]"
            echo "  update   - Update to latest version (default)"
            echo "  rollback - Rollback to previous backup"
            exit 1
            ;;
    esac
}

# Ensure backup directory exists
sudo mkdir -p "$BACKUP_DIR"
sudo chown -R "$DEPLOY_USER:$DEPLOY_USER" "$BACKUP_DIR"

# Run main function
main "$@"
