/**
 * Soil Master v1.0.2 PM2 Production Configuration
 * 
 * Enterprise-grade process management configuration with
 * clustering, monitoring, and automatic restart capabilities.
 */

module.exports = {
  apps: [
    {
      // Backend API Server
      name: 'soil-master-backend',
      script: 'python',
      args: ['-m', 'uvicorn', 'app.main:app', '--host', '0.0.0.0', '--port', '8000'],
      cwd: '/opt/soilmaster/soil-backend',
      instances: 'max', // Use all CPU cores
      exec_mode: 'cluster',
      
      // Environment configuration
      env: {
        NODE_ENV: 'production',
        ENVIRONMENT: 'production',
        DATABASE_URL: 'postgresql://soil_master:${DB_PASSWORD}@localhost:5432/soil_master',
        REDIS_URL: 'redis://localhost:6379',
        SECRET_KEY: '${SECRET_KEY}',
        JWT_SECRET: '${JWT_SECRET}',
        LOG_LEVEL: 'info',
        WORKERS: 4,
        
        // Demo configuration
        DEMO_MODE: 'true',
        DEMO_SCENARIOS_PATH: '/opt/soilmaster/demo-data',
        DEMO_CACHE_TTL: '300',
        
        // Performance settings
        UVICORN_WORKERS: 4,
        UVICORN_WORKER_CLASS: 'uvicorn.workers.UvicornWorker',
        UVICORN_BACKLOG: 2048,
        UVICORN_KEEPALIVE: 5,
        
        // Security settings
        CORS_ORIGINS: 'https://soilmaster.com,https://www.soilmaster.com',
        ALLOWED_HOSTS: 'soilmaster.com,www.soilmaster.com,localhost',
        
        // Monitoring
        PROMETHEUS_ENABLED: 'true',
        METRICS_PORT: '8001'
      },
      
      // Process management
      min_uptime: '10s',
      max_restarts: 10,
      restart_delay: 4000,
      
      // Auto restart conditions
      max_memory_restart: '1G',
      
      // Logging configuration
      log_file: '/var/log/soilmaster/backend-combined.log',
      out_file: '/var/log/soilmaster/backend-out.log',
      error_file: '/var/log/soilmaster/backend-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      
      // Health monitoring
      health_check_url: 'http://localhost:8000/health',
      health_check_grace_period: 3000,
      
      // Process monitoring
      pmx: true,
      
      // Advanced settings
      kill_timeout: 5000,
      listen_timeout: 3000,
      
      // Node.js specific (for Python, these are ignored but kept for consistency)
      node_args: [],
      
      // Watch and ignore patterns
      watch: false, // Disabled in production
      ignore_watch: ['node_modules', 'logs', '*.log'],
      
      // Source map support
      source_map_support: false,
      
      // Instance variables
      instance_var: 'INSTANCE_ID',
      
      // Graceful shutdown
      kill_timeout: 5000,
      wait_ready: true,
      listen_timeout: 3000
    },
    
    {
      // Frontend Next.js Server
      name: 'soil-master-frontend',
      script: 'npm',
      args: 'start',
      cwd: '/opt/soilmaster/soil-frontend',
      instances: 2, // Limited instances for Next.js
      exec_mode: 'cluster',
      
      // Environment configuration
      env: {
        NODE_ENV: 'production',
        PORT: '3000',
        
        // API configuration
        NEXT_PUBLIC_API_BASE_URL: 'https://api.soilmaster.com',
        NEXT_PUBLIC_WS_URL: 'wss://api.soilmaster.com/ws',
        
        // Demo configuration
        NEXT_PUBLIC_DEMO_MODE: 'true',
        NEXT_PUBLIC_DEMO_AUTO_REFRESH: 'true',
        NEXT_PUBLIC_DEMO_REFRESH_INTERVAL: '30000',
        
        // Performance settings
        NEXT_PUBLIC_CACHE_ENABLED: 'true',
        NEXT_PUBLIC_CDN_URL: 'https://cdn.soilmaster.com',
        
        // Monitoring
        NEXT_PUBLIC_MONITORING_ENABLED: 'true',
        NEXT_PUBLIC_SENTRY_DSN: '${SENTRY_DSN}',
        
        // Maps configuration
        NEXT_PUBLIC_MAPBOX_TOKEN: '${MAPBOX_TOKEN}',
        NEXT_PUBLIC_DEFAULT_MAP_CENTER: '[3.1390,101.6869]',
        NEXT_PUBLIC_DEFAULT_MAP_ZOOM: '10'
      },
      
      // Process management
      min_uptime: '10s',
      max_restarts: 10,
      restart_delay: 4000,
      
      // Auto restart conditions
      max_memory_restart: '512M',
      
      // Logging configuration
      log_file: '/var/log/soilmaster/frontend-combined.log',
      out_file: '/var/log/soilmaster/frontend-out.log',
      error_file: '/var/log/soilmaster/frontend-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      
      // Health monitoring
      health_check_url: 'http://localhost:3000',
      health_check_grace_period: 3000,
      
      // Process monitoring
      pmx: true,
      
      // Advanced settings
      kill_timeout: 5000,
      listen_timeout: 8000, // Next.js takes longer to start
      
      // Node.js specific settings
      node_args: ['--max-old-space-size=512'],
      
      // Watch and ignore patterns
      watch: false,
      ignore_watch: ['node_modules', '.next', 'logs', '*.log'],
      
      // Source map support
      source_map_support: true,
      
      // Instance variables
      instance_var: 'INSTANCE_ID',
      
      // Graceful shutdown
      kill_timeout: 5000,
      wait_ready: true,
      listen_timeout: 8000
    },
    
    {
      // Demo Performance Monitor
      name: 'soil-master-demo-monitor',
      script: 'python',
      args: ['-m', 'monitoring.demo_monitor'],
      cwd: '/opt/soilmaster',
      instances: 1, // Single instance for monitoring
      exec_mode: 'fork',
      
      // Environment configuration
      env: {
        ENVIRONMENT: 'production',
        MONITOR_INTERVAL: '30',
        ALERT_THRESHOLD_RESPONSE_TIME: '1000',
        ALERT_THRESHOLD_ERROR_RATE: '1',
        DEMO_API_URL: 'http://localhost:8000/api/v1/demo',
        FRONTEND_URL: 'http://localhost:3000',
        
        // Notification settings
        SLACK_WEBHOOK_URL: '${SLACK_WEBHOOK_URL}',
        EMAIL_ALERTS_ENABLED: 'true',
        SMTP_HOST: 'localhost',
        SMTP_PORT: '587',
        ALERT_EMAIL: '<EMAIL>'
      },
      
      // Process management
      min_uptime: '10s',
      max_restarts: 5,
      restart_delay: 10000,
      
      // Auto restart conditions
      max_memory_restart: '256M',
      
      // Logging configuration
      log_file: '/var/log/soilmaster/demo-monitor-combined.log',
      out_file: '/var/log/soilmaster/demo-monitor-out.log',
      error_file: '/var/log/soilmaster/demo-monitor-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      
      // Process monitoring
      pmx: false, // Disabled for monitoring process
      
      // Advanced settings
      kill_timeout: 3000,
      listen_timeout: 5000,
      
      // Cron restart (restart daily at 3 AM)
      cron_restart: '0 3 * * *',
      
      // Watch and ignore patterns
      watch: false,
      ignore_watch: ['logs', '*.log'],
      
      // Instance variables
      instance_var: 'MONITOR_INSTANCE_ID'
    }
  ],
  
  // Deployment configuration
  deploy: {
    production: {
      user: 'soilmaster',
      host: ['production-server-1', 'production-server-2'],
      ref: 'origin/main',
      repo: 'https://github.com/Yield-Sight-System/soil-master.git',
      path: '/opt/soilmaster',
      
      // Pre-deployment hooks
      'pre-deploy-local': 'echo "Starting deployment to production"',
      'pre-deploy': 'git fetch --all',
      
      // Post-deployment hooks
      'post-deploy': [
        'cd /opt/soilmaster/soil-backend && pip install -r requirements.txt',
        'cd /opt/soilmaster/soil-frontend && npm ci --production',
        'cd /opt/soilmaster/soil-frontend && npm run build',
        'pm2 reload ecosystem.config.js --env production',
        'pm2 save'
      ].join(' && '),
      
      // Post-setup hooks
      'post-setup': [
        'ls -la',
        'cd /opt/soilmaster && ./scripts/setup-production.sh'
      ].join(' && '),
      
      // Environment
      env: {
        NODE_ENV: 'production'
      }
    },
    
    staging: {
      user: 'soilmaster',
      host: 'staging-server',
      ref: 'origin/dev',
      repo: 'https://github.com/Yield-Sight-System/soil-master.git',
      path: '/opt/soilmaster-staging',
      
      // Pre-deployment hooks
      'pre-deploy-local': 'echo "Starting deployment to staging"',
      'pre-deploy': 'git fetch --all',
      
      // Post-deployment hooks
      'post-deploy': [
        'cd /opt/soilmaster-staging/soil-backend && pip install -r requirements.txt',
        'cd /opt/soilmaster-staging/soil-frontend && npm ci',
        'cd /opt/soilmaster-staging/soil-frontend && npm run build',
        'pm2 reload ecosystem.config.js --env staging',
        'pm2 save'
      ].join(' && '),
      
      // Environment
      env: {
        NODE_ENV: 'staging'
      }
    }
  }
};
