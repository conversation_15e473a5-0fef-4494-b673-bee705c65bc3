# Soil Master v1.0.2 Frontend Service
# SystemD service configuration for production deployment

[Unit]
Description=Soil Master Frontend Application
Documentation=https://docs.soilmaster.com
After=network.target soil-master-backend.service
Wants=soil-master-backend.service
Requires=network.target

[Service]
Type=exec
User=soilmaster
Group=soilmaster
WorkingDirectory=/opt/soilmaster/frontend
Environment=PATH=/usr/bin:/bin
Environment=NODE_ENV=production
Environment=PORT=3000
Environment=HOSTNAME=0.0.0.0
EnvironmentFile=/opt/soilmaster/frontend/.env.production

# Main process
ExecStart=/usr/bin/node server.js

# Health check
ExecStartPost=/bin/sleep 15
ExecStartPost=/bin/bash -c 'curl -f http://localhost:3000 || exit 1'

# Graceful shutdown
ExecStop=/bin/kill -TERM $MAINPID
TimeoutStopSec=30
KillMode=mixed
KillSignal=SIGTERM

# Restart policy
Restart=always
RestartSec=10
StartLimitInterval=60
StartLimitBurst=3

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/soilmaster/frontend/logs /tmp
CapabilityBoundingSet=CAP_NET_BIND_SERVICE
AmbientCapabilities=CAP_NET_BIND_SERVICE

# Resource limits
LimitNOFILE=65536
LimitNPROC=2048
MemoryMax=1G
CPUQuota=150%

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=soil-master-frontend

[Install]
WantedBy=multi-user.target
