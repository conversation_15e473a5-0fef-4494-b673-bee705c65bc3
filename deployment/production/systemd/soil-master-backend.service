# Soil Master v1.0.2 Backend Service
# SystemD service configuration for production deployment

[Unit]
Description=Soil Master Backend API Service
Documentation=https://docs.soilmaster.com
After=network.target postgresql.service redis.service
Wants=postgresql.service redis.service
Requires=network.target

[Service]
Type=exec
User=soilmaster
Group=soilmaster
WorkingDirectory=/opt/soilmaster/backend
Environment=PATH=/opt/soilmaster/backend/venv/bin
Environment=PYTHONPATH=/opt/soilmaster/backend
Environment=ENVIRONMENT=production
Environment=LOG_LEVEL=info
EnvironmentFile=/opt/soilmaster/backend/.env

# Main process
ExecStart=/opt/soilmaster/backend/venv/bin/uvicorn app.main:app \
    --host 0.0.0.0 \
    --port 8000 \
    --workers 4 \
    --worker-class uvicorn.workers.UvicornWorker \
    --access-log \
    --log-config /opt/soilmaster/backend/logging.conf

# Health check
ExecStartPost=/bin/sleep 10
ExecStartPost=/bin/bash -c 'curl -f http://localhost:8000/health || exit 1'

# Graceful shutdown
ExecStop=/bin/kill -TERM $MAINPID
TimeoutStopSec=30
KillMode=mixed
KillSignal=SIGTERM

# Restart policy
Restart=always
RestartSec=10
StartLimitInterval=60
StartLimitBurst=3

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/soilmaster/backend/logs /tmp
CapabilityBoundingSet=CAP_NET_BIND_SERVICE
AmbientCapabilities=CAP_NET_BIND_SERVICE

# Resource limits
LimitNOFILE=65536
LimitNPROC=4096
MemoryMax=2G
CPUQuota=200%

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=soil-master-backend

[Install]
WantedBy=multi-user.target
