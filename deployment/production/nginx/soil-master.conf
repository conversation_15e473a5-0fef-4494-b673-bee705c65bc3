# Soil Master v1.0.2 Nginx Production Configuration
# Enterprise-grade reverse proxy with performance optimization and security hardening

# Rate limiting zones
limit_req_zone $binary_remote_addr zone=api_limit:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=demo_limit:10m rate=20r/s;
limit_req_zone $binary_remote_addr zone=general_limit:10m rate=30r/s;

# Connection limiting
limit_conn_zone $binary_remote_addr zone=conn_limit_per_ip:10m;

# Upstream backend servers
upstream soil_master_backend {
    least_conn;
    server 127.0.0.1:8000 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:8001 max_fails=3 fail_timeout=30s backup;
    keepalive 32;
}

# Upstream frontend servers
upstream soil_master_frontend {
    least_conn;
    server 127.0.0.1:3000 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:3001 max_fails=3 fail_timeout=30s backup;
    keepalive 16;
}

# Cache configuration
proxy_cache_path /var/cache/nginx/soil_master levels=1:2 keys_zone=soil_master_cache:100m 
                 max_size=1g inactive=60m use_temp_path=off;

# Main server block - HTTPS
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name soilmaster.com www.soilmaster.com;
    
    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/soilmaster.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/soilmaster.com/privkey.pem;
    ssl_trusted_certificate /etc/letsencrypt/live/soilmaster.com/chain.pem;
    
    # SSL Security Settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 1d;
    ssl_session_tickets off;
    
    # OCSP Stapling
    ssl_stapling on;
    ssl_stapling_verify on;
    resolver ******* ******* valid=300s;
    resolver_timeout 5s;
    
    # Security Headers
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' wss: https:; frame-ancestors 'self';" always;
    
    # General Settings
    root /opt/soilmaster/soil-frontend/public;
    index index.html;
    
    # Connection and rate limiting
    limit_conn conn_limit_per_ip 20;
    limit_req zone=general_limit burst=50 nodelay;
    
    # Gzip Compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # Brotli Compression (if module available)
    brotli on;
    brotli_comp_level 6;
    brotli_types
        text/plain
        text/css
        application/json
        application/javascript
        text/xml
        application/xml
        application/xml+rss
        text/javascript;
    
    # API Routes
    location /api/ {
        limit_req zone=api_limit burst=20 nodelay;
        
        proxy_pass http://soil_master_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        
        # Timeouts
        proxy_connect_timeout 5s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # Buffering
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        proxy_busy_buffers_size 8k;
        
        # Cache API responses (selective)
        proxy_cache soil_master_cache;
        proxy_cache_valid 200 302 5m;
        proxy_cache_valid 404 1m;
        proxy_cache_use_stale error timeout updating http_500 http_502 http_503 http_504;
        proxy_cache_lock on;
        proxy_cache_lock_timeout 5s;
        
        # Cache bypass for dynamic content
        proxy_cache_bypass $http_pragma $http_authorization;
        proxy_no_cache $http_pragma $http_authorization;
        
        # Add cache status header
        add_header X-Cache-Status $upstream_cache_status;
    }
    
    # Demo API Routes (higher rate limit)
    location /api/v1/demo/ {
        limit_req zone=demo_limit burst=40 nodelay;
        
        proxy_pass http://soil_master_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Optimized timeouts for demo
        proxy_connect_timeout 3s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # Enhanced caching for demo data
        proxy_cache soil_master_cache;
        proxy_cache_valid 200 10m;
        proxy_cache_valid 404 1m;
        proxy_cache_use_stale error timeout updating http_500 http_502 http_503 http_504;
        
        # Demo-specific headers
        add_header X-Demo-Cache $upstream_cache_status;
        add_header X-Demo-Response-Time $upstream_response_time;
    }
    
    # WebSocket Support
    location /ws/ {
        proxy_pass http://soil_master_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket specific timeouts
        proxy_connect_timeout 7s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
        
        # Disable buffering for WebSockets
        proxy_buffering off;
    }
    
    # Static Assets with Long-term Caching
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Content-Type-Options "nosniff";
        
        # Try static files first, then proxy to frontend
        try_files $uri @frontend;
        
        # Gzip static assets
        gzip_static on;
    }
    
    # Next.js static files
    location /_next/static/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        proxy_pass http://soil_master_frontend;
    }
    
    # Demo specific routes
    location /demo {
        limit_req zone=demo_limit burst=30 nodelay;
        
        proxy_pass http://soil_master_frontend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Demo optimized settings
        proxy_connect_timeout 3s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # Cache demo pages briefly
        proxy_cache soil_master_cache;
        proxy_cache_valid 200 5m;
        proxy_cache_bypass $http_pragma;
        
        add_header X-Demo-Server $upstream_addr;
    }
    
    # Health Check Endpoint
    location /health {
        access_log off;
        proxy_pass http://soil_master_backend/health;
        proxy_connect_timeout 1s;
        proxy_send_timeout 1s;
        proxy_read_timeout 1s;
    }
    
    # Monitoring endpoints (restricted access)
    location /metrics {
        allow 127.0.0.1;
        allow 10.0.0.0/8;
        allow **********/12;
        allow ***********/16;
        deny all;
        
        proxy_pass http://soil_master_backend/metrics;
    }
    
    # Frontend fallback
    location @frontend {
        proxy_pass http://soil_master_frontend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        proxy_connect_timeout 5s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # Default route to frontend
    location / {
        limit_req zone=general_limit burst=30 nodelay;
        try_files $uri $uri/ @frontend;
    }
    
    # Security: Block access to sensitive files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ \.(env|config|ini|log|bak)$ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # Error pages
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    
    location = /50x.html {
        root /opt/soilmaster/nginx/html;
    }
    
    # Logging
    access_log /var/log/nginx/soil_master_access.log combined;
    error_log /var/log/nginx/soil_master_error.log warn;
}

# HTTP to HTTPS redirect
server {
    listen 80;
    listen [::]:80;
    server_name soilmaster.com www.soilmaster.com;
    
    # Security headers even for redirects
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;
    
    # Let's Encrypt challenge
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }
    
    # Redirect all other traffic to HTTPS
    location / {
        return 301 https://$server_name$request_uri;
    }
}

# Default server block (catch-all)
server {
    listen 80 default_server;
    listen [::]:80 default_server;
    listen 443 ssl default_server;
    listen [::]:443 ssl default_server;
    
    # Dummy SSL certificate for default server
    ssl_certificate /etc/nginx/ssl/dummy.crt;
    ssl_certificate_key /etc/nginx/ssl/dummy.key;
    
    server_name _;
    
    # Return 444 (connection closed without response)
    return 444;
}
