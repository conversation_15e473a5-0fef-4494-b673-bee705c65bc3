#!/bin/bash

# Production Deployment Script for Soil Master v1.0.2
# Target: Ubuntu Server 24.04 LTS (Native deployment, NO Docker)
# Enterprise-grade deployment with zero-defect standards

set -euo pipefail

# Configuration
DEPLOY_USER="soilmaster"
APP_DIR="/opt/soilmaster"
BACKEND_DIR="$APP_DIR/backend"
FRONTEND_DIR="$APP_DIR/frontend"
AI_DIR="$APP_DIR/soil-ai"
LOG_DIR="/var/log/soilmaster"
SERVICE_NAME="soilmaster"
NGINX_CONF="/etc/nginx/sites-available/soilmaster"
DOMAIN="${DOMAIN:-localhost}"
SSL_EMAIL="${SSL_EMAIL:-<EMAIL>}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}" >&2
}

warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

# Check if running as root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        error "This script should not be run as root for security reasons"
        error "Please run as a regular user with sudo privileges"
        exit 1
    fi
}

# Check Ubuntu version
check_ubuntu_version() {
    log "Checking Ubuntu version..."
    
    if ! grep -q "Ubuntu 24.04" /etc/os-release; then
        error "This deployment script is designed for Ubuntu Server 24.04 LTS"
        error "Current OS: $(lsb_release -d | cut -f2)"
        exit 1
    fi
    
    log "✓ Ubuntu 24.04 LTS detected"
}

# Install system dependencies
install_system_dependencies() {
    log "Installing system dependencies..."
    
    sudo apt update
    sudo apt upgrade -y
    
    # Essential packages
    sudo apt install -y \
        curl \
        wget \
        git \
        build-essential \
        software-properties-common \
        apt-transport-https \
        ca-certificates \
        gnupg \
        lsb-release \
        unzip \
        supervisor \
        nginx \
        certbot \
        python3-certbot-nginx \
        postgresql-16 \
        postgresql-contrib-16 \
        postgresql-16-postgis-3 \
        redis-server \
        fail2ban \
        ufw \
        htop \
        tree \
        jq
    
    log "✓ System dependencies installed"
}

# Install Node.js 20 LTS
install_nodejs() {
    log "Installing Node.js 20 LTS..."
    
    # Install Node.js 20 LTS via NodeSource repository
    curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
    sudo apt install -y nodejs
    
    # Install PM2 globally
    sudo npm install -g pm2
    
    # Verify installation
    node_version=$(node --version)
    npm_version=$(npm --version)
    pm2_version=$(pm2 --version)
    
    log "✓ Node.js installed: $node_version"
    log "✓ npm installed: $npm_version"
    log "✓ PM2 installed: $pm2_version"
}

# Install Python 3.12 and dependencies
install_python() {
    log "Installing Python 3.12 and dependencies..."
    
    # Python 3.12 should be available in Ubuntu 24.04
    sudo apt install -y \
        python3.12 \
        python3.12-dev \
        python3.12-venv \
        python3-pip \
        python3-setuptools \
        python3-wheel
    
    # Create symbolic links
    sudo ln -sf /usr/bin/python3.12 /usr/bin/python3
    sudo ln -sf /usr/bin/python3 /usr/bin/python
    
    # Upgrade pip
    python3 -m pip install --upgrade pip
    
    python_version=$(python3 --version)
    pip_version=$(pip3 --version)
    
    log "✓ Python installed: $python_version"
    log "✓ pip installed: $pip_version"
}

# Create application user and directories
setup_application_user() {
    log "Setting up application user and directories..."
    
    # Create application user
    if ! id "$DEPLOY_USER" &>/dev/null; then
        sudo useradd -r -s /bin/bash -d "$APP_DIR" -m "$DEPLOY_USER"
        log "✓ Created user: $DEPLOY_USER"
    else
        log "✓ User $DEPLOY_USER already exists"
    fi
    
    # Create directories
    sudo mkdir -p "$APP_DIR" "$LOG_DIR"
    sudo chown -R "$DEPLOY_USER:$DEPLOY_USER" "$APP_DIR" "$LOG_DIR"
    sudo chmod 755 "$APP_DIR" "$LOG_DIR"
    
    log "✓ Application directories created"
}

# Configure PostgreSQL
configure_postgresql() {
    log "Configuring PostgreSQL..."
    
    # Start and enable PostgreSQL
    sudo systemctl start postgresql
    sudo systemctl enable postgresql
    
    # Create database and user
    sudo -u postgres psql -c "CREATE DATABASE soilmaster_prod;" || true
    sudo -u postgres psql -c "CREATE USER soilmaster WITH PASSWORD 'secure_password_change_me';" || true
    sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE soilmaster_prod TO soilmaster;" || true
    sudo -u postgres psql -c "ALTER USER soilmaster CREATEDB;" || true
    
    # Enable PostGIS extension
    sudo -u postgres psql -d soilmaster_prod -c "CREATE EXTENSION IF NOT EXISTS postgis;" || true
    sudo -u postgres psql -d soilmaster_prod -c "CREATE EXTENSION IF NOT EXISTS postgis_topology;" || true
    
    # Configure PostgreSQL for production
    PG_VERSION="16"
    PG_CONFIG="/etc/postgresql/$PG_VERSION/main/postgresql.conf"
    PG_HBA="/etc/postgresql/$PG_VERSION/main/pg_hba.conf"
    
    # Backup original configs
    sudo cp "$PG_CONFIG" "$PG_CONFIG.backup"
    sudo cp "$PG_HBA" "$PG_HBA.backup"
    
    # Update PostgreSQL configuration for production
    sudo sed -i "s/#listen_addresses = 'localhost'/listen_addresses = 'localhost'/" "$PG_CONFIG"
    sudo sed -i "s/#max_connections = 100/max_connections = 200/" "$PG_CONFIG"
    sudo sed -i "s/#shared_buffers = 128MB/shared_buffers = 256MB/" "$PG_CONFIG"
    sudo sed -i "s/#effective_cache_size = 4GB/effective_cache_size = 1GB/" "$PG_CONFIG"
    sudo sed -i "s/#maintenance_work_mem = 64MB/maintenance_work_mem = 128MB/" "$PG_CONFIG"
    sudo sed -i "s/#checkpoint_completion_target = 0.9/checkpoint_completion_target = 0.9/" "$PG_CONFIG"
    sudo sed -i "s/#wal_buffers = -1/wal_buffers = 16MB/" "$PG_CONFIG"
    sudo sed -i "s/#default_statistics_target = 100/default_statistics_target = 100/" "$PG_CONFIG"
    
    # Restart PostgreSQL
    sudo systemctl restart postgresql
    
    log "✓ PostgreSQL configured and running"
}

# Configure Redis
configure_redis() {
    log "Configuring Redis..."
    
    # Backup original config
    sudo cp /etc/redis/redis.conf /etc/redis/redis.conf.backup
    
    # Configure Redis for production
    sudo sed -i 's/^# maxmemory <bytes>/maxmemory 256mb/' /etc/redis/redis.conf
    sudo sed -i 's/^# maxmemory-policy noeviction/maxmemory-policy allkeys-lru/' /etc/redis/redis.conf
    sudo sed -i 's/^save 900 1/# save 900 1/' /etc/redis/redis.conf
    sudo sed -i 's/^save 300 10/# save 300 10/' /etc/redis/redis.conf
    sudo sed -i 's/^save 60 10000/# save 60 10000/' /etc/redis/redis.conf
    
    # Start and enable Redis
    sudo systemctl start redis-server
    sudo systemctl enable redis-server
    
    log "✓ Redis configured and running"
}

# Deploy backend application
deploy_backend() {
    log "Deploying backend application..."
    
    # Create backend directory
    sudo mkdir -p "$BACKEND_DIR"
    sudo chown -R "$DEPLOY_USER:$DEPLOY_USER" "$BACKEND_DIR"
    
    # Copy backend files (assuming they're in the current directory)
    if [ -d "soil-backend" ]; then
        sudo cp -r soil-backend/* "$BACKEND_DIR/"
        sudo chown -R "$DEPLOY_USER:$DEPLOY_USER" "$BACKEND_DIR"
    else
        error "Backend source directory 'soil-backend' not found"
        exit 1
    fi
    
    # Create Python virtual environment
    sudo -u "$DEPLOY_USER" python3 -m venv "$BACKEND_DIR/venv"
    
    # Install Python dependencies
    sudo -u "$DEPLOY_USER" "$BACKEND_DIR/venv/bin/pip" install --upgrade pip
    sudo -u "$DEPLOY_USER" "$BACKEND_DIR/venv/bin/pip" install -r "$BACKEND_DIR/requirements.txt"
    
    # Create environment file
    sudo -u "$DEPLOY_USER" tee "$BACKEND_DIR/.env" > /dev/null <<EOF
# Production Environment Configuration
ENVIRONMENT=production
DEBUG=false

# Database Configuration
DATABASE_URL=postgresql://soilmaster:secure_password_change_me@localhost:5432/soilmaster_prod
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# Security Configuration
SECRET_KEY=$(openssl rand -hex 32)
JWT_SECRET_KEY=$(openssl rand -hex 32)
CORS_ORIGINS=["http://localhost", "https://$DOMAIN"]

# Application Configuration
API_V1_STR=/api/v1
PROJECT_NAME="Soil Master v1.0.2"
VERSION=1.0.2

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=$LOG_DIR/backend.log

# Performance Configuration
WORKERS=4
MAX_REQUESTS=1000
MAX_REQUESTS_JITTER=100
TIMEOUT=30
KEEPALIVE=2
EOF
    
    # Run database migrations
    cd "$BACKEND_DIR"
    sudo -u "$DEPLOY_USER" "$BACKEND_DIR/venv/bin/alembic" upgrade head
    
    log "✓ Backend application deployed"
}

# Deploy soil-ai module
deploy_soil_ai() {
    log "Deploying soil-ai module..."
    
    # Create AI directory
    sudo mkdir -p "$AI_DIR"
    sudo chown -R "$DEPLOY_USER:$DEPLOY_USER" "$AI_DIR"
    
    # Copy soil-ai files
    if [ -d "soil-ai" ]; then
        sudo cp -r soil-ai/* "$AI_DIR/"
        sudo chown -R "$DEPLOY_USER:$DEPLOY_USER" "$AI_DIR"
    else
        error "Soil-AI source directory 'soil-ai' not found"
        exit 1
    fi
    
    # Install soil-ai dependencies
    sudo -u "$DEPLOY_USER" "$BACKEND_DIR/venv/bin/pip" install -e "$AI_DIR"
    
    log "✓ Soil-AI module deployed"
}

# Deploy frontend application
deploy_frontend() {
    log "Deploying frontend application..."
    
    # Create frontend directory
    sudo mkdir -p "$FRONTEND_DIR"
    sudo chown -R "$DEPLOY_USER:$DEPLOY_USER" "$FRONTEND_DIR"
    
    # Copy frontend files
    if [ -d "soil-frontend" ]; then
        sudo cp -r soil-frontend/* "$FRONTEND_DIR/"
        sudo chown -R "$DEPLOY_USER:$DEPLOY_USER" "$FRONTEND_DIR"
    else
        error "Frontend source directory 'soil-frontend' not found"
        exit 1
    fi
    
    # Install dependencies and build
    cd "$FRONTEND_DIR"
    sudo -u "$DEPLOY_USER" npm ci --production
    sudo -u "$DEPLOY_USER" npm run build
    
    log "✓ Frontend application deployed"
}

# Configure PM2 for backend
configure_pm2() {
    log "Configuring PM2 for backend..."
    
    # Create PM2 ecosystem file
    sudo -u "$DEPLOY_USER" tee "$BACKEND_DIR/ecosystem.config.js" > /dev/null <<EOF
module.exports = {
  apps: [{
    name: 'soilmaster-backend',
    script: 'venv/bin/uvicorn',
    args: 'app.main:app --host 0.0.0.0 --port 8000 --workers 4',
    cwd: '$BACKEND_DIR',
    instances: 1,
    exec_mode: 'fork',
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PYTHONPATH: '$BACKEND_DIR:$AI_DIR'
    },
    error_file: '$LOG_DIR/backend-error.log',
    out_file: '$LOG_DIR/backend-out.log',
    log_file: '$LOG_DIR/backend-combined.log',
    time: true,
    autorestart: true,
    max_restarts: 10,
    min_uptime: '10s'
  }]
};
EOF
    
    # Start application with PM2
    sudo -u "$DEPLOY_USER" pm2 start "$BACKEND_DIR/ecosystem.config.js"
    sudo -u "$DEPLOY_USER" pm2 save
    
    # Setup PM2 startup script
    sudo env PATH=$PATH:/usr/bin pm2 startup systemd -u "$DEPLOY_USER" --hp "$APP_DIR"
    
    log "✓ PM2 configured and backend started"
}

# Configure Nginx
configure_nginx() {
    log "Configuring Nginx..."
    
    # Create Nginx configuration
    sudo tee "$NGINX_CONF" > /dev/null <<EOF
server {
    listen 80;
    server_name $DOMAIN www.$DOMAIN;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss application/javascript;
    
    # Frontend static files
    location / {
        root $FRONTEND_DIR/dist;
        try_files \$uri \$uri/ /index.html;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # API proxy
    location /api/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }
    
    # Health check endpoint
    location /health {
        proxy_pass http://127.0.0.1:8000/health;
        access_log off;
    }
    
    # Deny access to sensitive files
    location ~ /\. {
        deny all;
    }
    
    location ~ \.(env|log|conf)$ {
        deny all;
    }
}
EOF
    
    # Enable site
    sudo ln -sf "$NGINX_CONF" /etc/nginx/sites-enabled/
    sudo rm -f /etc/nginx/sites-enabled/default
    
    # Test Nginx configuration
    sudo nginx -t
    
    # Start and enable Nginx
    sudo systemctl start nginx
    sudo systemctl enable nginx
    
    log "✓ Nginx configured and running"
}

# Configure SSL with Let's Encrypt
configure_ssl() {
    log "Configuring SSL with Let's Encrypt..."
    
    if [ "$DOMAIN" != "localhost" ]; then
        # Obtain SSL certificate
        sudo certbot --nginx -d "$DOMAIN" -d "www.$DOMAIN" --email "$SSL_EMAIL" --agree-tos --non-interactive
        
        # Setup auto-renewal
        sudo systemctl enable certbot.timer
        sudo systemctl start certbot.timer
        
        log "✓ SSL certificate configured"
    else
        warning "Skipping SSL configuration for localhost"
    fi
}

# Configure firewall
configure_firewall() {
    log "Configuring firewall..."
    
    # Reset UFW to defaults
    sudo ufw --force reset
    
    # Default policies
    sudo ufw default deny incoming
    sudo ufw default allow outgoing
    
    # Allow SSH
    sudo ufw allow ssh
    
    # Allow HTTP and HTTPS
    sudo ufw allow 'Nginx Full'
    
    # Enable firewall
    sudo ufw --force enable
    
    log "✓ Firewall configured"
}

# Configure monitoring and logging
configure_monitoring() {
    log "Configuring monitoring and logging..."
    
    # Setup log rotation
    sudo tee /etc/logrotate.d/soilmaster > /dev/null <<EOF
$LOG_DIR/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 $DEPLOY_USER $DEPLOY_USER
    postrotate
        sudo -u $DEPLOY_USER pm2 reloadLogs
    endscript
}
EOF
    
    # Create systemd service for health monitoring
    sudo tee /etc/systemd/system/soilmaster-health.service > /dev/null <<EOF
[Unit]
Description=Soil Master Health Monitor
After=network.target

[Service]
Type=simple
User=$DEPLOY_USER
WorkingDirectory=$BACKEND_DIR
ExecStart=$BACKEND_DIR/venv/bin/python -m app.monitoring.health_check
Restart=always
RestartSec=30

[Install]
WantedBy=multi-user.target
EOF
    
    sudo systemctl enable soilmaster-health.service
    sudo systemctl start soilmaster-health.service
    
    log "✓ Monitoring and logging configured"
}

# Verify deployment
verify_deployment() {
    log "Verifying deployment..."
    
    # Check services
    services=("postgresql" "redis-server" "nginx")
    for service in "${services[@]}"; do
        if sudo systemctl is-active --quiet "$service"; then
            log "✓ $service is running"
        else
            error "✗ $service is not running"
            exit 1
        fi
    done
    
    # Check PM2 processes
    if sudo -u "$DEPLOY_USER" pm2 list | grep -q "online"; then
        log "✓ Backend application is running"
    else
        error "✗ Backend application is not running"
        exit 1
    fi
    
    # Check API health
    sleep 5
    if curl -f http://localhost:8000/health > /dev/null 2>&1; then
        log "✓ API health check passed"
    else
        error "✗ API health check failed"
        exit 1
    fi
    
    # Check frontend
    if curl -f http://localhost/ > /dev/null 2>&1; then
        log "✓ Frontend is accessible"
    else
        error "✗ Frontend is not accessible"
        exit 1
    fi
    
    log "✓ Deployment verification completed successfully"
}

# Main deployment function
main() {
    log "Starting Soil Master v1.0.2 Production Deployment"
    log "Target: Ubuntu Server 24.04 LTS (Native deployment)"
    
    check_root
    check_ubuntu_version
    install_system_dependencies
    install_nodejs
    install_python
    setup_application_user
    configure_postgresql
    configure_redis
    deploy_backend
    deploy_soil_ai
    deploy_frontend
    configure_pm2
    configure_nginx
    configure_ssl
    configure_firewall
    configure_monitoring
    verify_deployment
    
    log "🎉 Soil Master v1.0.2 deployment completed successfully!"
    log ""
    log "Application URLs:"
    log "  Frontend: http://$DOMAIN"
    log "  API: http://$DOMAIN/api/v1"
    log "  Health: http://$DOMAIN/health"
    log ""
    log "Management Commands:"
    log "  View logs: sudo tail -f $LOG_DIR/*.log"
    log "  Restart backend: sudo -u $DEPLOY_USER pm2 restart soilmaster-backend"
    log "  Restart nginx: sudo systemctl restart nginx"
    log "  Check status: sudo -u $DEPLOY_USER pm2 status"
    log ""
    warning "IMPORTANT: Change default passwords in $BACKEND_DIR/.env"
    warning "IMPORTANT: Review and update firewall rules as needed"
}

# Run main function
main "$@"
