#!/bin/bash

# Soil Master v1.0.2 Health Check Script
# Comprehensive system health validation for production deployment

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_FILE="/var/log/soilmaster/health-check.log"
TIMEOUT=30
VERBOSE=false

# Service endpoints
BACKEND_URL="http://localhost:8000"
FRONTEND_URL="http://localhost:3000"
NGINX_URL="http://localhost:80"
DEMO_URL="http://localhost:80/api/v1/demo"

# Database configuration
DB_HOST="localhost"
DB_PORT="5432"
DB_NAME="soil_master"
DB_USER="soil_master"

# Redis configuration
REDIS_HOST="localhost"
REDIS_PORT="6379"

# Logging functions
log() {
    local message="[$(date +'%Y-%m-%d %H:%M:%S')] $1"
    echo -e "${BLUE}${message}${NC}"
    [[ -f "$LOG_FILE" ]] && echo "$message" >> "$LOG_FILE"
}

success() {
    local message="[SUCCESS] $1"
    echo -e "${GREEN}${message}${NC}"
    [[ -f "$LOG_FILE" ]] && echo "$message" >> "$LOG_FILE"
}

warning() {
    local message="[WARNING] $1"
    echo -e "${YELLOW}${message}${NC}"
    [[ -f "$LOG_FILE" ]] && echo "$message" >> "$LOG_FILE"
}

error() {
    local message="[ERROR] $1"
    echo -e "${RED}${message}${NC}" >&2
    [[ -f "$LOG_FILE" ]] && echo "$message" >> "$LOG_FILE"
}

# Check if service is running
check_service() {
    local service_name="$1"
    
    if systemctl is-active --quiet "$service_name"; then
        success "Service $service_name is running"
        return 0
    else
        error "Service $service_name is not running"
        return 1
    fi
}

# Check HTTP endpoint
check_http_endpoint() {
    local name="$1"
    local url="$2"
    local expected_status="${3:-200}"
    
    log "Checking $name endpoint: $url"
    
    local response
    local status_code
    
    if response=$(curl -s -w "%{http_code}" --max-time "$TIMEOUT" "$url" 2>/dev/null); then
        status_code="${response: -3}"
        
        if [[ "$status_code" == "$expected_status" ]]; then
            success "$name endpoint is healthy (HTTP $status_code)"
            return 0
        else
            error "$name endpoint returned HTTP $status_code (expected $expected_status)"
            return 1
        fi
    else
        error "$name endpoint is unreachable"
        return 1
    fi
}

# Check database connectivity
check_database() {
    log "Checking PostgreSQL database connectivity"
    
    if command -v psql &> /dev/null; then
        if PGPASSWORD="$POSTGRES_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT 1;" &>/dev/null; then
            success "Database connection successful"
            return 0
        else
            error "Database connection failed"
            return 1
        fi
    else
        warning "psql command not found, skipping database check"
        return 0
    fi
}

# Check Redis connectivity
check_redis() {
    log "Checking Redis connectivity"
    
    if command -v redis-cli &> /dev/null; then
        if redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" ping | grep -q "PONG"; then
            success "Redis connection successful"
            return 0
        else
            error "Redis connection failed"
            return 1
        fi
    else
        warning "redis-cli command not found, skipping Redis check"
        return 0
    fi
}

# Check disk space
check_disk_space() {
    log "Checking disk space"
    
    local usage
    usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    
    if [[ $usage -lt 80 ]]; then
        success "Disk usage is ${usage}% (healthy)"
        return 0
    elif [[ $usage -lt 90 ]]; then
        warning "Disk usage is ${usage}% (warning threshold)"
        return 0
    else
        error "Disk usage is ${usage}% (critical threshold)"
        return 1
    fi
}

# Check memory usage
check_memory() {
    log "Checking memory usage"
    
    local usage
    usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    
    if [[ $usage -lt 80 ]]; then
        success "Memory usage is ${usage}% (healthy)"
        return 0
    elif [[ $usage -lt 90 ]]; then
        warning "Memory usage is ${usage}% (warning threshold)"
        return 0
    else
        error "Memory usage is ${usage}% (critical threshold)"
        return 1
    fi
}

# Check CPU load
check_cpu_load() {
    log "Checking CPU load"
    
    local load
    load=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    local cpu_cores
    cpu_cores=$(nproc)
    local load_percentage
    load_percentage=$(echo "$load $cpu_cores" | awk '{printf "%.0f", ($1/$2)*100}')
    
    if [[ $load_percentage -lt 70 ]]; then
        success "CPU load is ${load_percentage}% (healthy)"
        return 0
    elif [[ $load_percentage -lt 90 ]]; then
        warning "CPU load is ${load_percentage}% (warning threshold)"
        return 0
    else
        error "CPU load is ${load_percentage}% (critical threshold)"
        return 1
    fi
}

# Check SSL certificate (if applicable)
check_ssl_certificate() {
    local domain="${1:-localhost}"
    
    if [[ "$domain" == "localhost" ]]; then
        log "Skipping SSL check for localhost"
        return 0
    fi
    
    log "Checking SSL certificate for $domain"
    
    local expiry_date
    if expiry_date=$(echo | openssl s_client -servername "$domain" -connect "$domain:443" 2>/dev/null | openssl x509 -noout -dates | grep notAfter | cut -d= -f2); then
        local expiry_timestamp
        expiry_timestamp=$(date -d "$expiry_date" +%s)
        local current_timestamp
        current_timestamp=$(date +%s)
        local days_until_expiry
        days_until_expiry=$(( (expiry_timestamp - current_timestamp) / 86400 ))
        
        if [[ $days_until_expiry -gt 30 ]]; then
            success "SSL certificate expires in $days_until_expiry days"
            return 0
        elif [[ $days_until_expiry -gt 7 ]]; then
            warning "SSL certificate expires in $days_until_expiry days"
            return 0
        else
            error "SSL certificate expires in $days_until_expiry days (critical)"
            return 1
        fi
    else
        error "Failed to check SSL certificate"
        return 1
    fi
}

# Check demo system functionality
check_demo_system() {
    log "Checking demo system functionality"
    
    local demo_health_url="${DEMO_URL}/health"
    local demo_scenarios_url="${DEMO_URL}/scenarios"
    
    # Check demo health endpoint
    if ! check_http_endpoint "Demo Health" "$demo_health_url"; then
        return 1
    fi
    
    # Check demo scenarios endpoint
    if ! check_http_endpoint "Demo Scenarios" "$demo_scenarios_url"; then
        return 1
    fi
    
    # Check if demo returns valid JSON
    local response
    if response=$(curl -s --max-time "$TIMEOUT" "$demo_scenarios_url" 2>/dev/null); then
        if echo "$response" | jq . &>/dev/null; then
            success "Demo system returns valid JSON"
        else
            error "Demo system returns invalid JSON"
            return 1
        fi
    else
        error "Failed to get demo scenarios response"
        return 1
    fi
    
    return 0
}

# Check log files
check_log_files() {
    log "Checking log files"
    
    local log_files=(
        "/var/log/soilmaster/backend.log"
        "/var/log/soilmaster/frontend.log"
        "/var/log/nginx/soil_master_access.log"
        "/var/log/nginx/soil_master_error.log"
    )
    
    local issues=0
    
    for log_file in "${log_files[@]}"; do
        if [[ -f "$log_file" ]]; then
            local size
            size=$(stat -f%z "$log_file" 2>/dev/null || stat -c%s "$log_file" 2>/dev/null || echo "0")
            
            # Check if log file is too large (>100MB)
            if [[ $size -gt 104857600 ]]; then
                warning "Log file $log_file is large ($(( size / 1024 / 1024 ))MB)"
                ((issues++))
            fi
            
            # Check for recent errors
            if grep -q "ERROR\|CRITICAL\|FATAL" "$log_file" 2>/dev/null; then
                local error_count
                error_count=$(grep -c "ERROR\|CRITICAL\|FATAL" "$log_file" 2>/dev/null || echo "0")
                if [[ $error_count -gt 10 ]]; then
                    warning "Log file $log_file contains $error_count errors"
                    ((issues++))
                fi
            fi
        else
            warning "Log file $log_file does not exist"
            ((issues++))
        fi
    done
    
    if [[ $issues -eq 0 ]]; then
        success "All log files are healthy"
        return 0
    else
        warning "Found $issues log file issues"
        return 0
    fi
}

# Run comprehensive health check
run_health_check() {
    log "Starting comprehensive health check for Soil Master v1.0.2"
    
    local checks_passed=0
    local checks_failed=0
    local checks_warned=0
    
    # System checks
    echo -e "\n${BLUE}=== System Health ===${NC}"
    
    if check_disk_space; then ((checks_passed++)); else ((checks_failed++)); fi
    if check_memory; then ((checks_passed++)); else ((checks_failed++)); fi
    if check_cpu_load; then ((checks_passed++)); else ((checks_failed++)); fi
    
    # Service checks
    echo -e "\n${BLUE}=== Service Health ===${NC}"
    
    if check_service "soil-master-backend"; then ((checks_passed++)); else ((checks_failed++)); fi
    if check_service "soil-master-frontend"; then ((checks_passed++)); else ((checks_failed++)); fi
    if check_service "nginx"; then ((checks_passed++)); else ((checks_failed++)); fi
    if check_service "postgresql"; then ((checks_passed++)); else ((checks_failed++)); fi
    if check_service "redis"; then ((checks_passed++)); else ((checks_failed++)); fi
    
    # Connectivity checks
    echo -e "\n${BLUE}=== Connectivity Health ===${NC}"
    
    if check_database; then ((checks_passed++)); else ((checks_failed++)); fi
    if check_redis; then ((checks_passed++)); else ((checks_failed++)); fi
    
    # Endpoint checks
    echo -e "\n${BLUE}=== Endpoint Health ===${NC}"
    
    if check_http_endpoint "Backend API" "${BACKEND_URL}/health"; then ((checks_passed++)); else ((checks_failed++)); fi
    if check_http_endpoint "Frontend" "$FRONTEND_URL"; then ((checks_passed++)); else ((checks_failed++)); fi
    if check_http_endpoint "Nginx" "${NGINX_URL}/health"; then ((checks_passed++)); else ((checks_failed++)); fi
    
    # Demo system checks
    echo -e "\n${BLUE}=== Demo System Health ===${NC}"
    
    if check_demo_system; then ((checks_passed++)); else ((checks_failed++)); fi
    
    # Log file checks
    echo -e "\n${BLUE}=== Log File Health ===${NC}"
    
    if check_log_files; then ((checks_passed++)); else ((checks_warned++)); fi
    
    # SSL certificate check (if not localhost)
    if [[ "${DOMAIN:-localhost}" != "localhost" ]]; then
        echo -e "\n${BLUE}=== SSL Certificate Health ===${NC}"
        if check_ssl_certificate "$DOMAIN"; then ((checks_passed++)); else ((checks_failed++)); fi
    fi
    
    # Summary
    echo -e "\n${BLUE}=== Health Check Summary ===${NC}"
    
    local total_checks=$((checks_passed + checks_failed + checks_warned))
    
    echo "Total checks: $total_checks"
    echo -e "${GREEN}Passed: $checks_passed${NC}"
    echo -e "${YELLOW}Warnings: $checks_warned${NC}"
    echo -e "${RED}Failed: $checks_failed${NC}"
    
    if [[ $checks_failed -eq 0 ]]; then
        success "All critical health checks passed!"
        return 0
    else
        error "$checks_failed critical health checks failed"
        return 1
    fi
}

# Main function
main() {
    # Create log directory if it doesn't exist
    mkdir -p "$(dirname "$LOG_FILE")"
    
    case "${1:-check}" in
        "check"|"")
            run_health_check
            ;;
        "service")
            check_service "${2:-soil-master-backend}"
            ;;
        "endpoint")
            check_http_endpoint "${2:-Backend}" "${3:-$BACKEND_URL/health}"
            ;;
        "database")
            check_database
            ;;
        "redis")
            check_redis
            ;;
        "demo")
            check_demo_system
            ;;
        "ssl")
            check_ssl_certificate "${2:-$DOMAIN}"
            ;;
        *)
            echo "Usage: $0 {check|service|endpoint|database|redis|demo|ssl}"
            echo
            echo "Commands:"
            echo "  check     - Run comprehensive health check (default)"
            echo "  service   - Check specific service status"
            echo "  endpoint  - Check specific HTTP endpoint"
            echo "  database  - Check database connectivity"
            echo "  redis     - Check Redis connectivity"
            echo "  demo      - Check demo system functionality"
            echo "  ssl       - Check SSL certificate"
            exit 1
            ;;
    esac
}

# Load environment variables if available
if [[ -f "/opt/soilmaster/backend/.env" ]]; then
    set -a
    source "/opt/soilmaster/backend/.env"
    set +a
fi

# Run main function
main "$@"
