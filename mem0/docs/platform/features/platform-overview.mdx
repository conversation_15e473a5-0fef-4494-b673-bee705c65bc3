---
title: Overview
icon: "info"
iconType: "solid"
---

<Snippet file="security-compliance.mdx" />

Learn about the key features and capabilities that make Mem0 a powerful platform for memory management and retrieval.

## Core Features

<CardGroup>
  <Card title="Advanced Retrieval" icon="magnifying-glass" href="advanced-retrieval">
    Superior search results using state-of-the-art algorithms, including keyword search, reranking, and filtering capabilities.
  </Card>
  <Card title="Contextual Add" icon="square-plus" href="contextual-add">
    Only send your latest conversation history - we automatically retrieve the rest and generate properly contextualized memories.
  </Card>
  <Card title="Multimodal Support" icon="photo-film" href="multimodal-support">
    Process and analyze various types of content including images.
  </Card>
  <Card title="Memory Customization" icon="filter" href="selective-memory">
    Customize and curate stored memories to focus on relevant information while excluding unnecessary data, enabling improved accuracy, privacy control, and resource efficiency.
  </Card>
  <Card title="Custom Categories" icon="tags" href="custom-categories">
    Create and manage custom categories to organize memories based on your specific needs and requirements.
  </Card>
  <Card title="Custom Instructions" icon="list-check" href="custom-instructions">
    Define specific guidelines for your project to ensure consistent handling of information and requirements.
  </Card>
  <Card title="Direct Import" icon="message-bot" href="direct-import">
    Tailor the behavior of your Mem0 instance with custom prompts for specific use cases or domains.
  </Card>
  <Card title="Async Client" icon="bolt" href="async-client">
    Asynchronous client for non-blocking operations and high concurrency applications.
  </Card>
  <Card title="Memory Export" icon="file-export" href="memory-export">
    Export memories in structured formats using customizable Pydantic schemas.
  </Card>
  <Card title="Graph Memory" icon="graph" href="graph-memory">
    Add memories in the form of nodes and edges in a graph database and search for related memories.
  </Card>
</CardGroup>

## Getting Help

If you have any questions about these features or need assistance, our team is here to help:

<Snippet file="get-help.mdx" />
