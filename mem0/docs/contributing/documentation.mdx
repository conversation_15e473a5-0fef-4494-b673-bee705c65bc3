---
title: Documentation
icon: "book"
---

<Snippet file="security-compliance.mdx" />

# Documentation Contributions

## 📌 Prerequisites

Before getting started, ensure you have **Node.js (version 23.6.0 or higher)** installed on your system.

---

## 🚀 Setting Up Mintlify

### Step 1: Install Mintlify

Install Mintlify globally using your preferred package manager:

<CodeGroup>

```bash npm
npm i -g mintlify
```

```bash yarn
yarn global add mintlify
```

</CodeGroup>

### Step 2: Run the Documentation Server

Navigate to the `docs/` directory (where `docs.json` is located) and start the development server:

```bash
mintlify dev
```

The documentation website will be available at: [http://localhost:3000](http://localhost:3000).

---

## 🔧 Custom Ports

By default, Mintlify runs on **port 3000**. To use a different port, add the `--port` flag:

```bash
mintlify dev --port 3333
```

---

By following these steps, you can efficiently contribute to **Mem0's documentation**. Happy documenting! ✍️

