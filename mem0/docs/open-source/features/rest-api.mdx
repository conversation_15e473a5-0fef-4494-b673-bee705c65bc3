---
title: REST API Server
icon: "server"
iconType: "solid"
---

<Snippet file="security-compliance.mdx" />

Mem0 provides a REST API server (written using FastAPI). Users can perform all operations through REST endpoints. The API also includes OpenAPI documentation, accessible at `/docs` when the server is running.

<Frame caption="APIs supported by Mem0 REST API Server">
    <img src="/images/rest-api-server.png"/>
</Frame>

## Features

- **Create memories:** Create memories based on messages for a user, agent, or run.
- **Retrieve memories:** Get all memories for a given user, agent, or run.
- **Search memories:** Search stored memories based on a query.
- **Update memories:** Update an existing memory.
- **Delete memories:** Delete a specific memory or all memories for a user, agent, or run.
- **Reset memories:** Reset all memories for a user, agent, or run.
- **OpenAPI Documentation:** Accessible via `/docs` endpoint.

## Running Locally

<Tabs>
    <Tab title="With Docker Compose">
        The Development Docker Compose comes pre-configured with postgres pgvector, neo4j and a `server/history/history.db` volume for the history database.

        The only required environment variable to run the server is `OPENAI_API_KEY`.

        1. Create a `.env` file in the `server/` directory and set your environment variables. For example:

        ```txt
        OPENAI_API_KEY=your-openai-api-key
        ```

        2. Run the Docker container using Docker Compose:

        ```bash
        cd server
        docker compose up
        ```

        3. Access the API at http://localhost:8888.

        4. Making changes to the server code or the library code will automatically reload the server.
    </Tab>

    <Tab title="With Docker">

        1. Create a `.env` file in the current directory and set your environment variables. For example:

        ```txt
        OPENAI_API_KEY=your-openai-api-key
        ```

        2. Either pull the docker image from docker hub or build the docker image locally.

        <Tabs>
            <Tab title="Pull from Docker Hub">

                ```bash
                docker pull mem0/mem0-api-server
                ```

            </Tab>

            <Tab title="Build Locally">

                ```bash
                docker build -t mem0-api-server .
                ```

            </Tab>
        </Tabs>

        3. Run the Docker container:

        ``` bash
        docker run -p 8000:8000 mem0-api-server --env-file .env
        ```

        4. Access the API at http://localhost:8000.

    </Tab>

    <Tab title="Without Docker">

        1. Create a `.env` file in the current directory and set your environment variables. For example:

        ```txt
        OPENAI_API_KEY=your-openai-api-key
        ```

        2. Install dependencies:

        ```bash
        pip install -r requirements.txt
        ```

        3. Start the FastAPI server:

        ```bash
        uvicorn main:app --reload
        ```

        4. Access the API at http://localhost:8000.

    </Tab>
</Tabs>

## Usage

Once the server is running (locally or via Docker), you can interact with it using any REST client or through your preferred programming language (e.g., Go, Java, etc.). You can test out the APIs using the OpenAPI documentation at [http://localhost:8000/docs](http://localhost:8000/docs) endpoint.
