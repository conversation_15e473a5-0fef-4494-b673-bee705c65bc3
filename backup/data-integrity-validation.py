#!/usr/bin/env python3
"""
Soil Master v1.0.2 Data Integrity Validation

Comprehensive data integrity validation system for backup verification,
database consistency checks, and enterprise data quality assurance.
"""

import asyncio
import asyncpg
import redis
import json
import hashlib
import os
import sys
import logging
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
import yaml
import subprocess
from pathlib import Path
import psutil

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DataIntegrityValidator:
    """Comprehensive data integrity validation for Soil Master."""
    
    def __init__(self, config_file: str = "backup/config/integrity-validation.yaml"):
        self.config_file = config_file
        self.config = self._load_config()
        self.validation_results = {
            'database_integrity': {},
            'redis_integrity': {},
            'file_system_integrity': {},
            'backup_integrity': {},
            'cross_system_consistency': {},
            'performance_validation': {},
            'overall_score': 0,
            'validation_timestamp': datetime.now().isoformat()
        }
    
    def _load_config(self) -> Dict:
        """Load validation configuration."""
        default_config = {
            'database': {
                'host': 'localhost',
                'port': 5432,
                'database': 'soil_master',
                'user': 'soil_master',
                'password_file': '/etc/soilmaster/db-password'
            },
            'redis': {
                'host': 'localhost',
                'port': 6379,
                'password_file': '/etc/soilmaster/redis-password'
            },
            'file_system': {
                'application_dir': '/opt/soilmaster',
                'config_dir': '/etc/soilmaster',
                'log_dir': '/var/log/soilmaster',
                'backup_dir': '/opt/soilmaster/backups'
            },
            'validation_rules': {
                'min_scenario_count': 3,
                'max_response_time_ms': 1000,
                'required_tables': [
                    'scenarios', 'demo_sessions', 'users', 'soil_data',
                    'heatmap_cache', 'performance_metrics'
                ],
                'required_files': [
                    'ecosystem.config.js',
                    'package.json',
                    'requirements.txt'
                ]
            }
        }
        
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    loaded_config = yaml.safe_load(f)
                    default_config.update(loaded_config)
            return default_config
        except Exception as e:
            logger.warning(f"Could not load config file {self.config_file}: {e}")
            return default_config
    
    async def validate_database_integrity(self) -> Dict:
        """Validate PostgreSQL database integrity."""
        logger.info("Validating database integrity...")
        
        results = {
            'connection_test': False,
            'table_existence': {},
            'data_consistency': {},
            'foreign_key_integrity': {},
            'index_integrity': {},
            'performance_metrics': {},
            'score': 0
        }
        
        try:
            # Read database password
            db_password = ""
            password_file = self.config['database']['password_file']
            if os.path.exists(password_file):
                with open(password_file, 'r') as f:
                    db_password = f.read().strip()
            
            # Connect to database
            conn = await asyncpg.connect(
                host=self.config['database']['host'],
                port=self.config['database']['port'],
                database=self.config['database']['database'],
                user=self.config['database']['user'],
                password=db_password
            )
            
            results['connection_test'] = True
            logger.info("Database connection successful")
            
            # Check table existence
            required_tables = self.config['validation_rules']['required_tables']
            for table in required_tables:
                try:
                    query = """
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_schema = 'public' 
                        AND table_name = $1
                    );
                    """
                    exists = await conn.fetchval(query, table)
                    results['table_existence'][table] = exists
                    
                    if exists:
                        # Get row count
                        count_query = f"SELECT COUNT(*) FROM {table};"
                        row_count = await conn.fetchval(count_query)
                        results['table_existence'][f"{table}_count"] = row_count
                        
                except Exception as e:
                    logger.error(f"Error checking table {table}: {e}")
                    results['table_existence'][table] = False
            
            # Data consistency checks
            await self._check_data_consistency(conn, results)
            
            # Foreign key integrity
            await self._check_foreign_key_integrity(conn, results)
            
            # Index integrity
            await self._check_index_integrity(conn, results)
            
            # Performance metrics
            await self._check_database_performance(conn, results)
            
            await conn.close()
            
        except Exception as e:
            logger.error(f"Database validation error: {e}")
            results['error'] = str(e)
        
        # Calculate score
        score_components = [
            results['connection_test'],
            len([t for t in results['table_existence'].values() if t]) / len(required_tables),
            results['data_consistency'].get('scenarios_valid', False),
            results['foreign_key_integrity'].get('all_valid', False),
            results['performance_metrics'].get('acceptable_performance', False)
        ]
        
        results['score'] = sum(score_components) / len(score_components) * 100
        
        return results
    
    async def _check_data_consistency(self, conn: asyncpg.Connection, results: Dict):
        """Check data consistency rules."""
        logger.info("Checking data consistency...")
        
        consistency_checks = {}
        
        try:
            # Check scenario data consistency
            scenario_count = await conn.fetchval("SELECT COUNT(*) FROM scenarios WHERE is_active = true;")
            min_scenarios = self.config['validation_rules']['min_scenario_count']
            consistency_checks['scenarios_valid'] = scenario_count >= min_scenarios
            consistency_checks['scenario_count'] = scenario_count
            
            # Check demo session data
            recent_sessions = await conn.fetchval("""
                SELECT COUNT(*) FROM demo_sessions 
                WHERE created_at > NOW() - INTERVAL '7 days';
            """)
            consistency_checks['recent_demo_sessions'] = recent_sessions
            
            # Check soil data integrity
            soil_data_count = await conn.fetchval("SELECT COUNT(*) FROM soil_data;")
            consistency_checks['soil_data_count'] = soil_data_count
            consistency_checks['soil_data_valid'] = soil_data_count > 0
            
            # Check for orphaned records
            orphaned_sessions = await conn.fetchval("""
                SELECT COUNT(*) FROM demo_sessions ds
                LEFT JOIN scenarios s ON ds.scenario_id = s.id
                WHERE s.id IS NULL;
            """)
            consistency_checks['orphaned_sessions'] = orphaned_sessions
            consistency_checks['no_orphaned_data'] = orphaned_sessions == 0
            
        except Exception as e:
            logger.error(f"Data consistency check error: {e}")
            consistency_checks['error'] = str(e)
        
        results['data_consistency'] = consistency_checks
    
    async def _check_foreign_key_integrity(self, conn: asyncpg.Connection, results: Dict):
        """Check foreign key integrity."""
        logger.info("Checking foreign key integrity...")
        
        fk_checks = {}
        
        try:
            # Get all foreign key constraints
            fk_query = """
                SELECT 
                    tc.table_name,
                    kcu.column_name,
                    ccu.table_name AS foreign_table_name,
                    ccu.column_name AS foreign_column_name
                FROM information_schema.table_constraints AS tc
                JOIN information_schema.key_column_usage AS kcu
                    ON tc.constraint_name = kcu.constraint_name
                    AND tc.table_schema = kcu.table_schema
                JOIN information_schema.constraint_column_usage AS ccu
                    ON ccu.constraint_name = tc.constraint_name
                    AND ccu.table_schema = tc.table_schema
                WHERE tc.constraint_type = 'FOREIGN KEY'
                AND tc.table_schema = 'public';
            """
            
            foreign_keys = await conn.fetch(fk_query)
            
            violations = 0
            total_checks = 0
            
            for fk in foreign_keys:
                table = fk['table_name']
                column = fk['column_name']
                foreign_table = fk['foreign_table_name']
                foreign_column = fk['foreign_column_name']
                
                # Check for violations
                violation_query = f"""
                    SELECT COUNT(*) FROM {table} t
                    LEFT JOIN {foreign_table} ft ON t.{column} = ft.{foreign_column}
                    WHERE t.{column} IS NOT NULL AND ft.{foreign_column} IS NULL;
                """
                
                try:
                    violation_count = await conn.fetchval(violation_query)
                    violations += violation_count
                    total_checks += 1
                    
                    if violation_count > 0:
                        fk_checks[f"{table}.{column}"] = f"{violation_count} violations"
                
                except Exception as e:
                    logger.warning(f"Could not check FK {table}.{column}: {e}")
            
            fk_checks['total_violations'] = violations
            fk_checks['total_checks'] = total_checks
            fk_checks['all_valid'] = violations == 0
            
        except Exception as e:
            logger.error(f"Foreign key integrity check error: {e}")
            fk_checks['error'] = str(e)
        
        results['foreign_key_integrity'] = fk_checks
    
    async def _check_index_integrity(self, conn: asyncpg.Connection, results: Dict):
        """Check database index integrity."""
        logger.info("Checking index integrity...")
        
        index_checks = {}
        
        try:
            # Get index information
            index_query = """
                SELECT 
                    schemaname,
                    tablename,
                    indexname,
                    indexdef
                FROM pg_indexes
                WHERE schemaname = 'public'
                ORDER BY tablename, indexname;
            """
            
            indexes = await conn.fetch(index_query)
            index_checks['total_indexes'] = len(indexes)
            
            # Check for missing critical indexes
            critical_indexes = [
                ('scenarios', 'scenarios_pkey'),
                ('demo_sessions', 'demo_sessions_pkey'),
                ('soil_data', 'soil_data_pkey')
            ]
            
            missing_indexes = []
            for table, index_name in critical_indexes:
                index_exists = any(
                    idx['tablename'] == table and idx['indexname'] == index_name
                    for idx in indexes
                )
                if not index_exists:
                    missing_indexes.append(f"{table}.{index_name}")
            
            index_checks['missing_critical_indexes'] = missing_indexes
            index_checks['all_critical_present'] = len(missing_indexes) == 0
            
        except Exception as e:
            logger.error(f"Index integrity check error: {e}")
            index_checks['error'] = str(e)
        
        results['index_integrity'] = index_checks
    
    async def _check_database_performance(self, conn: asyncpg.Connection, results: Dict):
        """Check database performance metrics."""
        logger.info("Checking database performance...")
        
        perf_metrics = {}
        
        try:
            # Test query performance
            import time
            
            # Simple query performance
            start_time = time.time()
            await conn.fetchval("SELECT COUNT(*) FROM scenarios;")
            simple_query_time = (time.time() - start_time) * 1000
            
            # Complex query performance
            start_time = time.time()
            await conn.fetch("""
                SELECT s.name, COUNT(ds.id) as session_count
                FROM scenarios s
                LEFT JOIN demo_sessions ds ON s.id = ds.scenario_id
                GROUP BY s.id, s.name
                ORDER BY session_count DESC;
            """)
            complex_query_time = (time.time() - start_time) * 1000
            
            perf_metrics['simple_query_time_ms'] = simple_query_time
            perf_metrics['complex_query_time_ms'] = complex_query_time
            
            max_response_time = self.config['validation_rules']['max_response_time_ms']
            perf_metrics['acceptable_performance'] = (
                simple_query_time < max_response_time and 
                complex_query_time < max_response_time * 2
            )
            
            # Database size information
            db_size = await conn.fetchval("""
                SELECT pg_size_pretty(pg_database_size(current_database()));
            """)
            perf_metrics['database_size'] = db_size
            
        except Exception as e:
            logger.error(f"Database performance check error: {e}")
            perf_metrics['error'] = str(e)
        
        results['performance_metrics'] = perf_metrics
    
    async def validate_redis_integrity(self) -> Dict:
        """Validate Redis cache integrity."""
        logger.info("Validating Redis integrity...")
        
        results = {
            'connection_test': False,
            'memory_usage': {},
            'key_analysis': {},
            'performance_metrics': {},
            'score': 0
        }
        
        try:
            # Connect to Redis
            redis_client = redis.Redis(
                host=self.config['redis']['host'],
                port=self.config['redis']['port'],
                decode_responses=True
            )
            
            # Test connection
            redis_client.ping()
            results['connection_test'] = True
            logger.info("Redis connection successful")
            
            # Memory usage analysis
            memory_info = redis_client.info('memory')
            results['memory_usage'] = {
                'used_memory': memory_info.get('used_memory', 0),
                'used_memory_human': memory_info.get('used_memory_human', '0B'),
                'used_memory_peak': memory_info.get('used_memory_peak', 0),
                'memory_fragmentation_ratio': memory_info.get('mem_fragmentation_ratio', 0)
            }
            
            # Key analysis
            total_keys = redis_client.dbsize()
            results['key_analysis']['total_keys'] = total_keys
            
            # Sample key patterns
            key_patterns = ['demo:*', 'heatmap:*', 'session:*', 'cache:*']
            for pattern in key_patterns:
                pattern_keys = redis_client.keys(pattern)
                results['key_analysis'][f"{pattern}_count"] = len(pattern_keys)
            
            # Performance test
            import time
            start_time = time.time()
            redis_client.set('test_key', 'test_value')
            redis_client.get('test_key')
            redis_client.delete('test_key')
            operation_time = (time.time() - start_time) * 1000
            
            results['performance_metrics']['basic_operations_ms'] = operation_time
            results['performance_metrics']['acceptable_performance'] = operation_time < 100
            
        except Exception as e:
            logger.error(f"Redis validation error: {e}")
            results['error'] = str(e)
        
        # Calculate score
        score_components = [
            results['connection_test'],
            results['memory_usage'].get('mem_fragmentation_ratio', 2) < 1.5,
            results['key_analysis'].get('total_keys', 0) > 0,
            results['performance_metrics'].get('acceptable_performance', False)
        ]
        
        results['score'] = sum(score_components) / len(score_components) * 100
        
        return results
    
    def validate_file_system_integrity(self) -> Dict:
        """Validate file system integrity."""
        logger.info("Validating file system integrity...")
        
        results = {
            'directory_existence': {},
            'file_existence': {},
            'permissions_check': {},
            'disk_usage': {},
            'score': 0
        }
        
        try:
            # Check directory existence
            directories = [
                self.config['file_system']['application_dir'],
                self.config['file_system']['config_dir'],
                self.config['file_system']['log_dir'],
                self.config['file_system']['backup_dir']
            ]
            
            for directory in directories:
                exists = os.path.exists(directory)
                results['directory_existence'][directory] = exists
                
                if exists:
                    # Check permissions
                    stat_info = os.stat(directory)
                    results['permissions_check'][directory] = oct(stat_info.st_mode)[-3:]
            
            # Check required files
            app_dir = self.config['file_system']['application_dir']
            required_files = self.config['validation_rules']['required_files']
            
            for file_name in required_files:
                file_path = os.path.join(app_dir, file_name)
                exists = os.path.exists(file_path)
                results['file_existence'][file_name] = exists
                
                if exists:
                    # Check file size
                    file_size = os.path.getsize(file_path)
                    results['file_existence'][f"{file_name}_size"] = file_size
            
            # Disk usage analysis
            for directory in directories:
                if os.path.exists(directory):
                    usage = psutil.disk_usage(directory)
                    results['disk_usage'][directory] = {
                        'total': usage.total,
                        'used': usage.used,
                        'free': usage.free,
                        'percent': (usage.used / usage.total) * 100
                    }
            
        except Exception as e:
            logger.error(f"File system validation error: {e}")
            results['error'] = str(e)
        
        # Calculate score
        existing_dirs = sum(results['directory_existence'].values())
        total_dirs = len(directories)
        existing_files = sum(1 for k, v in results['file_existence'].items() if not k.endswith('_size') and v)
        total_files = len(required_files)
        
        results['score'] = ((existing_dirs / total_dirs) * 0.6 + (existing_files / total_files) * 0.4) * 100
        
        return results
    
    def validate_backup_integrity(self) -> Dict:
        """Validate backup file integrity."""
        logger.info("Validating backup integrity...")
        
        results = {
            'backup_availability': {},
            'backup_freshness': {},
            'backup_completeness': {},
            'score': 0
        }
        
        try:
            backup_dir = Path(self.config['file_system']['backup_dir'])
            
            if not backup_dir.exists():
                results['error'] = f"Backup directory not found: {backup_dir}"
                return results
            
            # Check backup types
            backup_types = ['database', 'redis', 'application', 'configuration', 'ssl']
            
            for backup_type in backup_types:
                type_dir = backup_dir / backup_type
                
                if type_dir.exists():
                    # Find latest backup
                    backup_dirs = [d for d in type_dir.iterdir() if d.is_dir()]
                    
                    if backup_dirs:
                        latest_backup = max(backup_dirs, key=lambda d: d.stat().st_mtime)
                        
                        # Check backup age
                        backup_age = datetime.now() - datetime.fromtimestamp(latest_backup.stat().st_mtime)
                        
                        results['backup_availability'][backup_type] = True
                        results['backup_freshness'][backup_type] = backup_age.total_seconds() / 3600  # hours
                        
                        # Check metadata file
                        metadata_file = latest_backup / 'metadata.json'
                        if metadata_file.exists():
                            with open(metadata_file, 'r') as f:
                                metadata = json.load(f)
                                results['backup_completeness'][backup_type] = {
                                    'has_metadata': True,
                                    'file_size': metadata.get('file_size', 0),
                                    'checksum': metadata.get('checksum', '')
                                }
                        else:
                            results['backup_completeness'][backup_type] = {'has_metadata': False}
                    else:
                        results['backup_availability'][backup_type] = False
                else:
                    results['backup_availability'][backup_type] = False
            
        except Exception as e:
            logger.error(f"Backup validation error: {e}")
            results['error'] = str(e)
        
        # Calculate score
        available_backups = sum(results['backup_availability'].values())
        total_backup_types = len(backup_types)
        fresh_backups = sum(1 for age in results['backup_freshness'].values() if age < 24)  # Less than 24 hours
        complete_backups = sum(1 for comp in results['backup_completeness'].values() if comp.get('has_metadata', False))
        
        results['score'] = (
            (available_backups / total_backup_types) * 0.4 +
            (fresh_backups / total_backup_types) * 0.4 +
            (complete_backups / total_backup_types) * 0.2
        ) * 100
        
        return results
    
    async def run_comprehensive_validation(self) -> Dict:
        """Run comprehensive data integrity validation."""
        logger.info("Starting comprehensive data integrity validation...")
        
        # Run all validation components
        self.validation_results['database_integrity'] = await self.validate_database_integrity()
        self.validation_results['redis_integrity'] = await self.validate_redis_integrity()
        self.validation_results['file_system_integrity'] = self.validate_file_system_integrity()
        self.validation_results['backup_integrity'] = self.validate_backup_integrity()
        
        # Calculate overall score
        scores = [
            self.validation_results['database_integrity']['score'],
            self.validation_results['redis_integrity']['score'],
            self.validation_results['file_system_integrity']['score'],
            self.validation_results['backup_integrity']['score']
        ]
        
        self.validation_results['overall_score'] = sum(scores) / len(scores)
        
        logger.info(f"Data integrity validation completed. Overall score: {self.validation_results['overall_score']:.1f}/100")
        
        return self.validation_results
    
    def generate_validation_report(self, output_file: str = None) -> str:
        """Generate comprehensive validation report."""
        
        report = f"""
# Soil Master v1.0.2 Data Integrity Validation Report

## Executive Summary
- **Overall Score**: {self.validation_results['overall_score']:.1f}/100
- **Validation Date**: {self.validation_results['validation_timestamp']}
- **Status**: {'✅ PASSED' if self.validation_results['overall_score'] >= 80 else '❌ FAILED'}

## Component Validation Results

### Database Integrity
- **Score**: {self.validation_results['database_integrity']['score']:.1f}/100
- **Connection**: {'✅' if self.validation_results['database_integrity']['connection_test'] else '❌'}
- **Tables**: {sum(1 for k, v in self.validation_results['database_integrity']['table_existence'].items() if not k.endswith('_count') and v)}/{len(self.config['validation_rules']['required_tables'])}
- **Data Consistency**: {'✅' if self.validation_results['database_integrity']['data_consistency'].get('scenarios_valid', False) else '❌'}

### Redis Integrity
- **Score**: {self.validation_results['redis_integrity']['score']:.1f}/100
- **Connection**: {'✅' if self.validation_results['redis_integrity']['connection_test'] else '❌'}
- **Total Keys**: {self.validation_results['redis_integrity']['key_analysis'].get('total_keys', 0)}
- **Performance**: {'✅' if self.validation_results['redis_integrity']['performance_metrics'].get('acceptable_performance', False) else '❌'}

### File System Integrity
- **Score**: {self.validation_results['file_system_integrity']['score']:.1f}/100
- **Directories**: {sum(self.validation_results['file_system_integrity']['directory_existence'].values())}/{len(self.validation_results['file_system_integrity']['directory_existence'])}
- **Required Files**: {sum(1 for k, v in self.validation_results['file_system_integrity']['file_existence'].items() if not k.endswith('_size') and v)}/{len(self.config['validation_rules']['required_files'])}

### Backup Integrity
- **Score**: {self.validation_results['backup_integrity']['score']:.1f}/100
- **Available Backups**: {sum(self.validation_results['backup_integrity']['backup_availability'].values())}/5
- **Fresh Backups**: {sum(1 for age in self.validation_results['backup_integrity']['backup_freshness'].values() if age < 24)}/5

## Recommendations

"""
        
        # Add specific recommendations
        if self.validation_results['overall_score'] < 80:
            report += "### Critical Issues\n"
            
            if self.validation_results['database_integrity']['score'] < 80:
                report += "- ⚠️ Database integrity issues detected\n"
            
            if self.validation_results['backup_integrity']['score'] < 80:
                report += "- ⚠️ Backup system requires attention\n"
        
        if self.validation_results['overall_score'] >= 80:
            report += "### Status: Data Integrity Validated\n"
            report += "- ✅ All data integrity checks passed\n"
            report += "- ✅ Backup systems are operational\n"
            report += "- ✅ Database consistency verified\n"
        
        report += f"\n## Detailed Results\n\n```json\n{json.dumps(self.validation_results, indent=2, default=str)}\n```\n"
        
        if output_file:
            with open(output_file, 'w') as f:
                f.write(report)
            logger.info(f"Validation report saved to {output_file}")
        
        return report

async def main():
    """Main validation execution."""
    validator = DataIntegrityValidator()
    
    try:
        results = await validator.run_comprehensive_validation()
        report = validator.generate_validation_report('data-integrity-validation-report.md')
        
        print(report)
        
        # Exit with appropriate code
        if results['overall_score'] >= 80:
            print("✅ Data integrity validation PASSED")
            sys.exit(0)
        else:
            print("❌ Data integrity validation FAILED")
            sys.exit(1)
    
    except Exception as e:
        logger.error(f"Validation failed with error: {e}")
        sys.exit(1)

if __name__ == '__main__':
    asyncio.run(main())
