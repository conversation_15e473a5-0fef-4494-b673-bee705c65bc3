#!/bin/bash

# Soil Master v1.0.2 Automated Backup System
# Enterprise-grade backup and disaster recovery solution

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_FILE="${SCRIPT_DIR}/backup-config.conf"
LOG_FILE="/var/log/soilmaster/backup.log"
BACKUP_BASE_DIR="/opt/soilmaster/backups"
REMOTE_BACKUP_DIR="${REMOTE_BACKUP_DIR:-}"
RETENTION_DAYS="${RETENTION_DAYS:-30}"
ENCRYPTION_KEY_FILE="/etc/soilmaster/backup-encryption.key"

# Create directories
mkdir -p "$(dirname "$LOG_FILE")"
mkdir -p "$BACKUP_BASE_DIR"
mkdir -p "/etc/soilmaster"

# Logging functions
log() {
    local message="[$(date +'%Y-%m-%d %H:%M:%S')] $1"
    echo -e "${BLUE}${message}${NC}"
    echo "$message" >> "$LOG_FILE"
}

success() {
    local message="[SUCCESS] $1"
    echo -e "${GREEN}${message}${NC}"
    echo "$message" >> "$LOG_FILE"
}

warning() {
    local message="[WARNING] $1"
    echo -e "${YELLOW}${message}${NC}"
    echo "$message" >> "$LOG_FILE"
}

error() {
    local message="[ERROR] $1"
    echo -e "${RED}${message}${NC}" >&2
    echo "$message" >> "$LOG_FILE"
}

# Load configuration
load_config() {
    if [[ -f "$CONFIG_FILE" ]]; then
        source "$CONFIG_FILE"
        log "Configuration loaded from $CONFIG_FILE"
    else
        warning "Configuration file not found, using defaults"
        create_default_config
    fi
}

# Create default configuration
create_default_config() {
    cat > "$CONFIG_FILE" << 'EOF'
# Soil Master Backup Configuration

# Database settings
DB_HOST="localhost"
DB_PORT="5432"
DB_NAME="soil_master"
DB_USER="soil_master"
DB_PASSWORD_FILE="/etc/soilmaster/db-password"

# Redis settings
REDIS_HOST="localhost"
REDIS_PORT="6379"
REDIS_PASSWORD_FILE="/etc/soilmaster/redis-password"

# Application directories
APP_DIR="/opt/soilmaster"
CONFIG_DIR="/etc/soilmaster"
LOG_DIR="/var/log/soilmaster"
NGINX_CONFIG_DIR="/etc/nginx/sites-available"
SSL_CERT_DIR="/etc/letsencrypt"

# Backup settings
BACKUP_TYPES="database redis application config logs ssl"
COMPRESSION_LEVEL="6"
ENCRYPTION_ENABLED="true"
VERIFY_BACKUPS="true"

# Remote backup settings
REMOTE_ENABLED="false"
REMOTE_TYPE="s3"  # s3, rsync, ftp
REMOTE_S3_BUCKET=""
REMOTE_S3_REGION="us-east-1"
REMOTE_RSYNC_HOST=""
REMOTE_RSYNC_PATH=""

# Notification settings
NOTIFICATION_EMAIL="<EMAIL>"
SLACK_WEBHOOK_URL=""
NOTIFICATION_ON_SUCCESS="false"
NOTIFICATION_ON_FAILURE="true"
EOF

    log "Default configuration created at $CONFIG_FILE"
}

# Generate encryption key
generate_encryption_key() {
    if [[ ! -f "$ENCRYPTION_KEY_FILE" ]]; then
        openssl rand -base64 32 > "$ENCRYPTION_KEY_FILE"
        chmod 600 "$ENCRYPTION_KEY_FILE"
        chown root:root "$ENCRYPTION_KEY_FILE"
        success "Encryption key generated"
    fi
}

# Database backup
backup_database() {
    log "Starting database backup..."
    
    local backup_dir="$BACKUP_BASE_DIR/database/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    # Read database password
    local db_password=""
    if [[ -f "$DB_PASSWORD_FILE" ]]; then
        db_password=$(cat "$DB_PASSWORD_FILE")
    fi
    
    # Create database dump
    export PGPASSWORD="$db_password"
    
    local dump_file="$backup_dir/soil_master_db.sql"
    
    if pg_dump -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
        --verbose --clean --if-exists --create > "$dump_file" 2>>"$LOG_FILE"; then
        
        # Compress and encrypt if enabled
        if [[ "$COMPRESSION_LEVEL" -gt 0 ]]; then
            gzip -"$COMPRESSION_LEVEL" "$dump_file"
            dump_file="${dump_file}.gz"
        fi
        
        if [[ "$ENCRYPTION_ENABLED" == "true" ]]; then
            openssl enc -aes-256-cbc -salt -in "$dump_file" -out "${dump_file}.enc" \
                -pass file:"$ENCRYPTION_KEY_FILE"
            rm "$dump_file"
            dump_file="${dump_file}.enc"
        fi
        
        # Create metadata
        cat > "$backup_dir/metadata.json" << EOF
{
    "backup_type": "database",
    "timestamp": "$(date -Iseconds)",
    "database": "$DB_NAME",
    "host": "$DB_HOST",
    "compressed": $([ "$COMPRESSION_LEVEL" -gt 0 ] && echo "true" || echo "false"),
    "encrypted": $([ "$ENCRYPTION_ENABLED" == "true" ] && echo "true" || echo "false"),
    "file_size": $(stat -c%s "$dump_file"),
    "checksum": "$(sha256sum "$dump_file" | cut -d' ' -f1)"
}
EOF
        
        success "Database backup completed: $backup_dir"
        echo "$backup_dir"
    else
        error "Database backup failed"
        return 1
    fi
    
    unset PGPASSWORD
}

# Redis backup
backup_redis() {
    log "Starting Redis backup..."
    
    local backup_dir="$BACKUP_BASE_DIR/redis/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    # Redis data directory
    local redis_data_dir="/var/lib/redis"
    local redis_dump_file="$redis_data_dir/dump.rdb"
    
    if [[ -f "$redis_dump_file" ]]; then
        # Copy Redis dump file
        cp "$redis_dump_file" "$backup_dir/"
        
        # Also create a Redis save
        redis-cli BGSAVE
        sleep 5  # Wait for background save to complete
        
        # Copy the fresh dump
        cp "$redis_dump_file" "$backup_dir/dump_fresh.rdb"
        
        # Compress and encrypt
        local backup_file="$backup_dir/redis_backup.tar"
        tar -cf "$backup_file" -C "$backup_dir" dump.rdb dump_fresh.rdb
        
        if [[ "$COMPRESSION_LEVEL" -gt 0 ]]; then
            gzip -"$COMPRESSION_LEVEL" "$backup_file"
            backup_file="${backup_file}.gz"
        fi
        
        if [[ "$ENCRYPTION_ENABLED" == "true" ]]; then
            openssl enc -aes-256-cbc -salt -in "$backup_file" -out "${backup_file}.enc" \
                -pass file:"$ENCRYPTION_KEY_FILE"
            rm "$backup_file"
            backup_file="${backup_file}.enc"
        fi
        
        # Clean up temporary files
        rm -f "$backup_dir/dump.rdb" "$backup_dir/dump_fresh.rdb"
        
        # Create metadata
        cat > "$backup_dir/metadata.json" << EOF
{
    "backup_type": "redis",
    "timestamp": "$(date -Iseconds)",
    "host": "$REDIS_HOST",
    "port": "$REDIS_PORT",
    "compressed": $([ "$COMPRESSION_LEVEL" -gt 0 ] && echo "true" || echo "false"),
    "encrypted": $([ "$ENCRYPTION_ENABLED" == "true" ] && echo "true" || echo "false"),
    "file_size": $(stat -c%s "$backup_file"),
    "checksum": "$(sha256sum "$backup_file" | cut -d' ' -f1)"
}
EOF
        
        success "Redis backup completed: $backup_dir"
        echo "$backup_dir"
    else
        error "Redis dump file not found"
        return 1
    fi
}

# Application backup
backup_application() {
    log "Starting application backup..."
    
    local backup_dir="$BACKUP_BASE_DIR/application/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    # Create application archive
    local app_backup_file="$backup_dir/application.tar"
    
    # Exclude certain directories and files
    local exclude_patterns=(
        "--exclude=node_modules"
        "--exclude=.git"
        "--exclude=*.log"
        "--exclude=__pycache__"
        "--exclude=.pytest_cache"
        "--exclude=venv"
        "--exclude=.env"
        "--exclude=backups"
    )
    
    if tar -cf "$app_backup_file" "${exclude_patterns[@]}" -C "$(dirname "$APP_DIR")" "$(basename "$APP_DIR")" 2>>"$LOG_FILE"; then
        
        # Compress and encrypt
        if [[ "$COMPRESSION_LEVEL" -gt 0 ]]; then
            gzip -"$COMPRESSION_LEVEL" "$app_backup_file"
            app_backup_file="${app_backup_file}.gz"
        fi
        
        if [[ "$ENCRYPTION_ENABLED" == "true" ]]; then
            openssl enc -aes-256-cbc -salt -in "$app_backup_file" -out "${app_backup_file}.enc" \
                -pass file:"$ENCRYPTION_KEY_FILE"
            rm "$app_backup_file"
            app_backup_file="${app_backup_file}.enc"
        fi
        
        # Create metadata
        cat > "$backup_dir/metadata.json" << EOF
{
    "backup_type": "application",
    "timestamp": "$(date -Iseconds)",
    "source_directory": "$APP_DIR",
    "compressed": $([ "$COMPRESSION_LEVEL" -gt 0 ] && echo "true" || echo "false"),
    "encrypted": $([ "$ENCRYPTION_ENABLED" == "true" ] && echo "true" || echo "false"),
    "file_size": $(stat -c%s "$app_backup_file"),
    "checksum": "$(sha256sum "$app_backup_file" | cut -d' ' -f1)"
}
EOF
        
        success "Application backup completed: $backup_dir"
        echo "$backup_dir"
    else
        error "Application backup failed"
        return 1
    fi
}

# Configuration backup
backup_configuration() {
    log "Starting configuration backup..."
    
    local backup_dir="$BACKUP_BASE_DIR/configuration/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    # Backup various configuration directories
    local config_dirs=(
        "$CONFIG_DIR"
        "$NGINX_CONFIG_DIR"
        "/etc/systemd/system/soil-master*"
        "/etc/fail2ban/jail.local"
        "/etc/ufw"
        "/etc/ssh/sshd_config.d"
    )
    
    local config_backup_file="$backup_dir/configuration.tar"
    
    # Create configuration archive
    tar -cf "$config_backup_file" "${config_dirs[@]}" 2>/dev/null || true
    
    # Add PM2 ecosystem file if exists
    if [[ -f "/opt/soilmaster/ecosystem.config.js" ]]; then
        tar -rf "$config_backup_file" "/opt/soilmaster/ecosystem.config.js" 2>/dev/null || true
    fi
    
    # Compress and encrypt
    if [[ "$COMPRESSION_LEVEL" -gt 0 ]]; then
        gzip -"$COMPRESSION_LEVEL" "$config_backup_file"
        config_backup_file="${config_backup_file}.gz"
    fi
    
    if [[ "$ENCRYPTION_ENABLED" == "true" ]]; then
        openssl enc -aes-256-cbc -salt -in "$config_backup_file" -out "${config_backup_file}.enc" \
            -pass file:"$ENCRYPTION_KEY_FILE"
        rm "$config_backup_file"
        config_backup_file="${config_backup_file}.enc"
    fi
    
    # Create metadata
    cat > "$backup_dir/metadata.json" << EOF
{
    "backup_type": "configuration",
    "timestamp": "$(date -Iseconds)",
    "config_directories": $(printf '%s\n' "${config_dirs[@]}" | jq -R . | jq -s .),
    "compressed": $([ "$COMPRESSION_LEVEL" -gt 0 ] && echo "true" || echo "false"),
    "encrypted": $([ "$ENCRYPTION_ENABLED" == "true" ] && echo "true" || echo "false"),
    "file_size": $(stat -c%s "$config_backup_file"),
    "checksum": "$(sha256sum "$config_backup_file" | cut -d' ' -f1)"
}
EOF
    
    success "Configuration backup completed: $backup_dir"
    echo "$backup_dir"
}

# SSL certificates backup
backup_ssl_certificates() {
    log "Starting SSL certificates backup..."
    
    local backup_dir="$BACKUP_BASE_DIR/ssl/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    if [[ -d "$SSL_CERT_DIR" ]]; then
        local ssl_backup_file="$backup_dir/ssl_certificates.tar"
        
        # Create SSL certificates archive
        tar -cf "$ssl_backup_file" -C "$(dirname "$SSL_CERT_DIR")" "$(basename "$SSL_CERT_DIR")" 2>>"$LOG_FILE"
        
        # Compress and encrypt (SSL certs should always be encrypted)
        if [[ "$COMPRESSION_LEVEL" -gt 0 ]]; then
            gzip -"$COMPRESSION_LEVEL" "$ssl_backup_file"
            ssl_backup_file="${ssl_backup_file}.gz"
        fi
        
        # Always encrypt SSL certificates
        openssl enc -aes-256-cbc -salt -in "$ssl_backup_file" -out "${ssl_backup_file}.enc" \
            -pass file:"$ENCRYPTION_KEY_FILE"
        rm "$ssl_backup_file"
        ssl_backup_file="${ssl_backup_file}.enc"
        
        # Create metadata
        cat > "$backup_dir/metadata.json" << EOF
{
    "backup_type": "ssl_certificates",
    "timestamp": "$(date -Iseconds)",
    "source_directory": "$SSL_CERT_DIR",
    "compressed": $([ "$COMPRESSION_LEVEL" -gt 0 ] && echo "true" || echo "false"),
    "encrypted": true,
    "file_size": $(stat -c%s "$ssl_backup_file"),
    "checksum": "$(sha256sum "$ssl_backup_file" | cut -d' ' -f1)"
}
EOF
        
        success "SSL certificates backup completed: $backup_dir"
        echo "$backup_dir"
    else
        warning "SSL certificates directory not found: $SSL_CERT_DIR"
    fi
}

# Verify backup integrity
verify_backup() {
    local backup_dir="$1"
    log "Verifying backup integrity: $backup_dir"
    
    if [[ ! -f "$backup_dir/metadata.json" ]]; then
        error "Metadata file not found in $backup_dir"
        return 1
    fi
    
    # Read metadata
    local metadata=$(cat "$backup_dir/metadata.json")
    local stored_checksum=$(echo "$metadata" | jq -r '.checksum')
    local backup_type=$(echo "$metadata" | jq -r '.backup_type')
    
    # Find the backup file
    local backup_file=""
    case "$backup_type" in
        "database")
            backup_file=$(find "$backup_dir" -name "*.sql*" -type f | head -1)
            ;;
        "redis")
            backup_file=$(find "$backup_dir" -name "redis_backup.tar*" -type f | head -1)
            ;;
        "application")
            backup_file=$(find "$backup_dir" -name "application.tar*" -type f | head -1)
            ;;
        "configuration")
            backup_file=$(find "$backup_dir" -name "configuration.tar*" -type f | head -1)
            ;;
        "ssl_certificates")
            backup_file=$(find "$backup_dir" -name "ssl_certificates.tar*" -type f | head -1)
            ;;
    esac
    
    if [[ -n "$backup_file" && -f "$backup_file" ]]; then
        local current_checksum=$(sha256sum "$backup_file" | cut -d' ' -f1)
        
        if [[ "$stored_checksum" == "$current_checksum" ]]; then
            success "Backup integrity verified: $backup_dir"
            return 0
        else
            error "Backup integrity check failed: $backup_dir"
            error "Expected: $stored_checksum"
            error "Actual: $current_checksum"
            return 1
        fi
    else
        error "Backup file not found in $backup_dir"
        return 1
    fi
}

# Clean old backups
cleanup_old_backups() {
    log "Cleaning up old backups (retention: $RETENTION_DAYS days)..."
    
    local backup_types=("database" "redis" "application" "configuration" "ssl")
    
    for backup_type in "${backup_types[@]}"; do
        local type_dir="$BACKUP_BASE_DIR/$backup_type"
        
        if [[ -d "$type_dir" ]]; then
            # Find and remove old backup directories
            find "$type_dir" -type d -name "*_*" -mtime +$RETENTION_DAYS -exec rm -rf {} + 2>/dev/null || true
            
            local remaining_count=$(find "$type_dir" -type d -name "*_*" | wc -l)
            log "Cleaned up old $backup_type backups, $remaining_count remaining"
        fi
    done
}

# Send notification
send_notification() {
    local status="$1"
    local message="$2"
    
    # Email notification
    if [[ -n "$NOTIFICATION_EMAIL" ]] && command -v mail >/dev/null 2>&1; then
        if [[ "$status" == "success" && "$NOTIFICATION_ON_SUCCESS" == "true" ]] || \
           [[ "$status" == "failure" && "$NOTIFICATION_ON_FAILURE" == "true" ]]; then
            
            local subject="Soil Master Backup $status"
            echo "$message" | mail -s "$subject" "$NOTIFICATION_EMAIL"
        fi
    fi
    
    # Slack notification
    if [[ -n "$SLACK_WEBHOOK_URL" ]]; then
        if [[ "$status" == "success" && "$NOTIFICATION_ON_SUCCESS" == "true" ]] || \
           [[ "$status" == "failure" && "$NOTIFICATION_ON_FAILURE" == "true" ]]; then
            
            local color="good"
            [[ "$status" == "failure" ]] && color="danger"
            
            curl -X POST -H 'Content-type: application/json' \
                --data "{\"text\":\"$message\",\"color\":\"$color\"}" \
                "$SLACK_WEBHOOK_URL" 2>/dev/null || true
        fi
    fi
}

# Main backup function
run_backup() {
    local backup_types_array=($BACKUP_TYPES)
    local backup_dirs=()
    local failed_backups=()
    
    log "Starting Soil Master backup process..."
    log "Backup types: ${backup_types_array[*]}"
    
    # Generate encryption key if needed
    if [[ "$ENCRYPTION_ENABLED" == "true" ]]; then
        generate_encryption_key
    fi
    
    # Run backups
    for backup_type in "${backup_types_array[@]}"; do
        case "$backup_type" in
            "database")
                if backup_dir=$(backup_database); then
                    backup_dirs+=("$backup_dir")
                else
                    failed_backups+=("database")
                fi
                ;;
            "redis")
                if backup_dir=$(backup_redis); then
                    backup_dirs+=("$backup_dir")
                else
                    failed_backups+=("redis")
                fi
                ;;
            "application")
                if backup_dir=$(backup_application); then
                    backup_dirs+=("$backup_dir")
                else
                    failed_backups+=("application")
                fi
                ;;
            "config")
                if backup_dir=$(backup_configuration); then
                    backup_dirs+=("$backup_dir")
                else
                    failed_backups+=("configuration")
                fi
                ;;
            "ssl")
                if backup_dir=$(backup_ssl_certificates); then
                    backup_dirs+=("$backup_dir")
                else
                    failed_backups+=("ssl")
                fi
                ;;
        esac
    done
    
    # Verify backups if enabled
    if [[ "$VERIFY_BACKUPS" == "true" ]]; then
        log "Verifying backup integrity..."
        for backup_dir in "${backup_dirs[@]}"; do
            if ! verify_backup "$backup_dir"; then
                failed_backups+=("verification:$(basename "$backup_dir")")
            fi
        done
    fi
    
    # Clean up old backups
    cleanup_old_backups
    
    # Generate summary
    local total_backups=${#backup_types_array[@]}
    local successful_backups=$((total_backups - ${#failed_backups[@]}))
    
    if [[ ${#failed_backups[@]} -eq 0 ]]; then
        success "All backups completed successfully ($successful_backups/$total_backups)"
        send_notification "success" "Soil Master backup completed successfully. $successful_backups/$total_backups backups created."
    else
        error "Some backups failed ($successful_backups/$total_backups successful)"
        error "Failed backups: ${failed_backups[*]}"
        send_notification "failure" "Soil Master backup completed with errors. $successful_backups/$total_backups successful. Failed: ${failed_backups[*]}"
    fi
    
    # Create backup summary
    cat > "$BACKUP_BASE_DIR/last_backup_summary.json" << EOF
{
    "timestamp": "$(date -Iseconds)",
    "total_backups": $total_backups,
    "successful_backups": $successful_backups,
    "failed_backups": $(printf '%s\n' "${failed_backups[@]}" | jq -R . | jq -s . 2>/dev/null || echo "[]"),
    "backup_directories": $(printf '%s\n' "${backup_dirs[@]}" | jq -R . | jq -s . 2>/dev/null || echo "[]"),
    "retention_days": $RETENTION_DAYS,
    "encryption_enabled": $([ "$ENCRYPTION_ENABLED" == "true" ] && echo "true" || echo "false")
}
EOF
    
    log "Backup process completed"
    
    # Return appropriate exit code
    [[ ${#failed_backups[@]} -eq 0 ]]
}

# Main execution
main() {
    echo -e "${BLUE}=== Soil Master v1.0.2 Automated Backup System ===${NC}"
    echo "Started at: $(date)"
    echo "Log file: $LOG_FILE"
    echo
    
    load_config
    
    if run_backup; then
        echo -e "${GREEN}✅ Backup completed successfully${NC}"
        exit 0
    else
        echo -e "${RED}❌ Backup completed with errors${NC}"
        exit 1
    fi
}

# Handle command line arguments
case "${1:-}" in
    "config")
        create_default_config
        echo "Default configuration created at $CONFIG_FILE"
        echo "Please edit the configuration file and run the backup again."
        ;;
    "verify")
        if [[ -n "${2:-}" ]]; then
            verify_backup "$2"
        else
            echo "Usage: $0 verify <backup_directory>"
            exit 1
        fi
        ;;
    "cleanup")
        load_config
        cleanup_old_backups
        ;;
    *)
        main "$@"
        ;;
esac
