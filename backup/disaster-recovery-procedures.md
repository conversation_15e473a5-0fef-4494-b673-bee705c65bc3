# Soil Master v1.0.2 Disaster Recovery Procedures

## Executive Summary

This document outlines comprehensive disaster recovery procedures for the Soil Master v1.0.2 production environment. These procedures ensure business continuity and rapid recovery from various disaster scenarios.

## Recovery Time Objectives (RTO) and Recovery Point Objectives (RPO)

| Component | RTO | RPO | Priority |
|-----------|-----|-----|----------|
| Demo System | 2 hours | 1 hour | Critical |
| Database | 4 hours | 15 minutes | Critical |
| Application Services | 1 hour | 30 minutes | High |
| Monitoring Systems | 30 minutes | 1 hour | Medium |
| SSL Certificates | 1 hour | 24 hours | High |

## Disaster Scenarios and Response Procedures

### 1. Complete Server Failure

**Scenario**: Primary server hardware failure or complete system corruption.

**Detection**:
- Monitoring alerts show all services down
- Server is unreachable via SSH
- No response from health check endpoints

**Recovery Steps**:

1. **Immediate Response** (0-15 minutes)
   ```bash
   # Activate incident response team
   # Notify stakeholders via emergency communication plan
   # Assess scope of failure
   ```

2. **Infrastructure Recovery** (15-60 minutes)
   ```bash
   # Deploy new server instance
   # Configure basic networking and security
   # Install base operating system and dependencies
   ```

3. **System Restoration** (60-120 minutes)
   ```bash
   # Restore from latest backup
   cd /opt/soilmaster/backup
   ./disaster-recovery-restore.sh --full-restore --backup-date=latest
   
   # Verify system integrity
   ./system-health-check.sh --comprehensive
   ```

### 2. Database Corruption or Loss

**Scenario**: PostgreSQL database corruption, accidental deletion, or data loss.

**Detection**:
- Database connection errors
- Data integrity check failures
- Application errors related to data access

**Recovery Steps**:

1. **Stop Application Services**
   ```bash
   pm2 stop soil-master-backend
   pm2 stop soil-master-frontend
   ```

2. **Assess Database State**
   ```bash
   # Check database connectivity
   pg_isready -h localhost -p 5432
   
   # Verify data integrity
   psql -h localhost -U soil_master -d soil_master -c "SELECT COUNT(*) FROM scenarios;"
   ```

3. **Restore Database**
   ```bash
   # Find latest database backup
   LATEST_DB_BACKUP=$(find /opt/soilmaster/backups/database -name "*.sql*" -type f | sort | tail -1)
   
   # Restore database
   ./backup/restore-database.sh --backup-file="$LATEST_DB_BACKUP"
   ```

4. **Verify and Restart Services**
   ```bash
   # Verify database restoration
   ./scripts/verify-database-integrity.sh
   
   # Restart services
   pm2 start ecosystem.config.js
   ```

### 3. Application Code Corruption

**Scenario**: Application files corrupted, malicious changes, or deployment failures.

**Recovery Steps**:

1. **Isolate the Issue**
   ```bash
   # Stop affected services
   pm2 stop soil-master-backend soil-master-frontend
   
   # Backup current state for analysis
   tar -czf /tmp/corrupted-app-$(date +%Y%m%d_%H%M%S).tar.gz /opt/soilmaster/
   ```

2. **Restore Application**
   ```bash
   # Restore from latest application backup
   ./backup/restore-application.sh --backup-date=latest
   
   # Or restore from Git repository
   cd /opt/soilmaster
   git fetch --all
   git reset --hard origin/main
   ```

3. **Rebuild and Deploy**
   ```bash
   # Rebuild frontend
   cd soil-frontend
   npm ci
   npm run build
   
   # Install backend dependencies
   cd ../soil-backend
   pip install -r requirements.txt
   
   # Restart services
   pm2 restart ecosystem.config.js
   ```

### 4. SSL Certificate Expiry or Loss

**Scenario**: SSL certificates expired or lost, causing HTTPS failures.

**Recovery Steps**:

1. **Immediate Mitigation**
   ```bash
   # Switch to HTTP temporarily (emergency only)
   # Update Nginx configuration to serve HTTP
   sed -i 's/return 301 https/return 200/g' /etc/nginx/sites-available/soil-master
   systemctl reload nginx
   ```

2. **Restore SSL Certificates**
   ```bash
   # Restore from backup
   ./backup/restore-ssl-certificates.sh --backup-date=latest
   
   # Or regenerate certificates
   ./security/enterprise-ssl-setup.sh soilmaster.com <EMAIL>
   ```

3. **Verify SSL Configuration**
   ```bash
   # Test SSL configuration
   nginx -t
   systemctl reload nginx
   
   # Verify HTTPS access
   curl -I https://soilmaster.com
   ```

### 5. Redis Cache Failure

**Scenario**: Redis service failure or data corruption.

**Recovery Steps**:

1. **Assess Redis State**
   ```bash
   # Check Redis service
   systemctl status redis-server
   
   # Test Redis connectivity
   redis-cli ping
   ```

2. **Restore Redis Data**
   ```bash
   # Stop Redis service
   systemctl stop redis-server
   
   # Restore from backup
   ./backup/restore-redis.sh --backup-date=latest
   
   # Start Redis service
   systemctl start redis-server
   ```

3. **Verify Cache Functionality**
   ```bash
   # Test cache operations
   redis-cli set test_key "test_value"
   redis-cli get test_key
   redis-cli del test_key
   ```

## Automated Recovery Scripts

### Full System Recovery Script

```bash
#!/bin/bash
# /opt/soilmaster/backup/disaster-recovery-restore.sh

set -euo pipefail

BACKUP_DATE="${1:-latest}"
RESTORE_TYPE="${2:-full}"

echo "Starting disaster recovery restore..."
echo "Backup date: $BACKUP_DATE"
echo "Restore type: $RESTORE_TYPE"

# Stop all services
pm2 stop all
systemctl stop nginx

# Restore components based on type
case "$RESTORE_TYPE" in
    "full")
        ./restore-database.sh --backup-date="$BACKUP_DATE"
        ./restore-redis.sh --backup-date="$BACKUP_DATE"
        ./restore-application.sh --backup-date="$BACKUP_DATE"
        ./restore-configuration.sh --backup-date="$BACKUP_DATE"
        ./restore-ssl-certificates.sh --backup-date="$BACKUP_DATE"
        ;;
    "data-only")
        ./restore-database.sh --backup-date="$BACKUP_DATE"
        ./restore-redis.sh --backup-date="$BACKUP_DATE"
        ;;
    "app-only")
        ./restore-application.sh --backup-date="$BACKUP_DATE"
        ./restore-configuration.sh --backup-date="$BACKUP_DATE"
        ;;
esac

# Restart services
systemctl start nginx
pm2 start ecosystem.config.js

# Verify system health
./system-health-check.sh --comprehensive

echo "Disaster recovery restore completed"
```

## Recovery Validation Procedures

### 1. Database Integrity Check

```bash
#!/bin/bash
# Verify database integrity after restoration

echo "Checking database integrity..."

# Check table counts
psql -h localhost -U soil_master -d soil_master -c "
SELECT 
    schemaname,
    tablename,
    n_tup_ins as inserts,
    n_tup_upd as updates,
    n_tup_del as deletes
FROM pg_stat_user_tables;
"

# Verify critical data
psql -h localhost -U soil_master -d soil_master -c "
SELECT COUNT(*) as scenario_count FROM scenarios;
SELECT COUNT(*) as user_count FROM users;
SELECT COUNT(*) as demo_session_count FROM demo_sessions;
"

echo "Database integrity check completed"
```

### 2. Application Functionality Test

```bash
#!/bin/bash
# Test critical application functionality

echo "Testing application functionality..."

# Test health endpoints
curl -f http://localhost:8000/health || echo "Backend health check failed"
curl -f http://localhost:3000 || echo "Frontend health check failed"

# Test demo API
curl -f http://localhost:8000/api/v1/demo/scenarios || echo "Demo API test failed"

# Test database connectivity
curl -f http://localhost:8000/api/v1/health/database || echo "Database connectivity test failed"

echo "Application functionality test completed"
```

## Communication Plan

### Incident Response Team

| Role | Primary Contact | Secondary Contact |
|------|----------------|-------------------|
| Incident Commander | <EMAIL> | <EMAIL> |
| Technical Lead | <EMAIL> | <EMAIL> |
| Communications | <EMAIL> | <EMAIL> |

### Stakeholder Notification

1. **Immediate Notification** (within 15 minutes)
   - Internal incident response team
   - Key technical stakeholders

2. **Status Updates** (every 30 minutes)
   - Executive team
   - Customer support team
   - Key customers (if applicable)

3. **Resolution Notification**
   - All stakeholders
   - Post-incident report within 24 hours

### Communication Templates

#### Initial Incident Notification
```
Subject: [URGENT] Soil Master System Incident - [INCIDENT_ID]

We are currently experiencing a system incident affecting the Soil Master platform.

Incident Details:
- Start Time: [TIME]
- Impact: [DESCRIPTION]
- Affected Services: [SERVICES]
- Current Status: [STATUS]

Our team is actively working to resolve this issue. We will provide updates every 30 minutes.

Next Update: [TIME]
```

#### Resolution Notification
```
Subject: [RESOLVED] Soil Master System Incident - [INCIDENT_ID]

The system incident affecting the Soil Master platform has been resolved.

Resolution Details:
- Resolution Time: [TIME]
- Total Downtime: [DURATION]
- Root Cause: [CAUSE]
- Actions Taken: [ACTIONS]

All services are now operational. A detailed post-incident report will be provided within 24 hours.
```

## Testing and Maintenance

### Monthly Disaster Recovery Tests

1. **Database Recovery Test**
   - Restore database from backup to test environment
   - Verify data integrity and completeness
   - Test application functionality with restored data

2. **Application Recovery Test**
   - Deploy application from backup to test environment
   - Verify all services start correctly
   - Test critical functionality

3. **Full System Recovery Test** (Quarterly)
   - Complete system restoration in test environment
   - End-to-end functionality testing
   - Performance validation

### Backup Validation

```bash
#!/bin/bash
# Monthly backup validation script

echo "Starting monthly backup validation..."

# Test database backup restoration
./test-database-restore.sh

# Test application backup restoration
./test-application-restore.sh

# Verify backup integrity
./verify-backup-integrity.sh

# Generate validation report
./generate-backup-validation-report.sh

echo "Monthly backup validation completed"
```

## Documentation Updates

This disaster recovery plan should be reviewed and updated:

- **Monthly**: Review and test procedures
- **Quarterly**: Update contact information and communication plans
- **Annually**: Comprehensive review and update of all procedures
- **After Incidents**: Update based on lessons learned

## Compliance and Audit

### Audit Trail

All disaster recovery activities must be logged and documented:

1. **Incident Logs**: Detailed timeline of events and actions taken
2. **Recovery Logs**: All commands executed during recovery
3. **Validation Logs**: Results of post-recovery testing
4. **Communication Logs**: All stakeholder communications

### Compliance Requirements

- **Data Protection**: Ensure all recovery procedures comply with GDPR and data protection regulations
- **Security**: Maintain security standards during recovery operations
- **Documentation**: Keep detailed records for audit purposes

---

**Document Version**: 1.0.2  
**Last Updated**: $(date)  
**Next Review Date**: $(date -d "+3 months")  
**Owner**: Soil Master Operations Team
